import { Component } from '@angular/core';
import { ProductModuleService } from '../../../home/<USER>/product-module.service';
import { DashboardModulesHolder } from 'src/app/modules/shared/components/dashboard-modules-holder/modulesHolder';

@Component({
  selector: 'app-ownerdashboard',
  templateUrl: './ownerdashboard.component.html',
  styleUrls: ['./ownerdashboard.component.scss'],
})
export class OwnerdashboardComponent {
  productModulesList: DashboardModulesHolder[] = [
    {
      moduleName: 'navigationMenus.tenantConfiguration',
      moduleDescription: 'Manage Sales.',
      modulePermission: ['Provider.TenantManagement', 'AllPermissions'],
      moduleRouterLink: '../tenants',
      moduleType: 'subModule',
    },
    {
      moduleName: 'navigationMenus.zatcaListings',
      moduleDescription: 'Manage Sales.',
      modulePermission: ['Provider.TenantManagement', 'AllPermissions'],
      moduleRouterLink: '../tenants/zatcalistings',
      moduleType: 'subModule',
    },
  ];

  constructor() {
    // Constructor can be used for dependency injection if needed
  }

  ngOnInit(): void {
    // Initialization logic can be added here if needed
  }
}
