<div class="nav-caption" *ngIf="item.navCap" mat-subheader>
  {{ item.navCap }}
</div>
<ng-container *appHasPermission="item?.permissions">
  <mat-list-item
    class="menu-list-item"
    *ngIf="!item.navCap && !item.external && !item.twoLines"
    [ngClass]="{
      'mat-toolbar mat-accent activeMenu': item.route ? router.isActive(item.route, true) : false,
      expanded: expanded,
      disabled: item.disabled
    }"
    (click)="onItemSelected(item)">
    <i-tabler class="routeIcon" name="{{ item.iconName }}" matListItemIcon></i-tabler>
    <span class="hide-menu">{{ item.displayName | translate }}</span>
    <span class="arrow-icon" *ngIf="item.children && item.children.length" fxFlex>
      <span fxFlex></span>
      <mat-icon [@indicatorRotate]="expanded ? 'expanded' : 'collapsed'"> expand_more </mat-icon>
    </span>
  </mat-list-item>
</ng-container>

<!-- children -->
<div *ngIf="expanded">
  <app-vertical-side-nav
    class="children-menu-item"
    *ngFor="let child of item.children"
    [item]="child"
    [depth]="depth + 1"
    (click)="onSubItemSelected(child)"
    >test
  </app-vertical-side-nav>
</div>
