$mat-primary: (
  100: #f4f9fb,
  500: #0a7ea4,
  700: #06769a,
  contrast: (
    100: rgba(black, 0.87),
    500: white,
    700: white,
  ),
);
$mat-secondary: (
  100: white,
  500: #1e88e5,
  700: #1e88e5,
  contrast: (
    100: white,
    500: white,
    700: white,
  ),
);
$mat-warn: (
  100: FDEDE8,
  500: #e91e63,
  700: #e91e63,
  contrast: (
    100: white,
    500: white,
    700: white,
  ),
);

$theme-primary: mat.define-palette($mat-primary, 500);
$theme-accent: mat.define-palette($mat-secondary, 500);
$theme-warn: mat.define-palette($mat-warn, 500);

$greentheme: mat.define-light-theme(
  (
    color: (
      primary: $theme-primary,
      accent: $theme-accent,
      warn: $theme-warn,
    ),
    typography:
      mat.define-typography-config(
        $font-family: "'Poppins', sans-serif",
        $subtitle-2: mat.define-typography-level(16px, 14px, 400),
        $subtitle-1: mat.define-typography-level(21px, 26px, 500),
        $headline-6: mat.define-typography-level(18px, 26px, 500),
        $headline-5: mat.define-typography-level(24px, 30px, 700),
        $button: mat.define-typography-level(14px, 14px, 400),
        $body-1: mat.define-typography-level(14px, 20px, 400),
        $body-2: mat.define-typography-level(14px, 22px, 400),
      ),
    density: minimum,
  )
);

// .border {
//   border: 1px solid $borderColor;
// }

.readOnly .mat-mdc-slide-toggle {
  --mdc-switch-disabled-selected-handle-color: #6200ee !important;
  --mdc-switch-disabled-selected-track-color: #0a7ea4 !important;
  --mdc-switch-disabled-unselected-handle-color: #6200ee !important;
  --mdc-switch-disabled-unselected-track-color: #0a7ea4 !important;
}

.readOnly .mat-mdc-slide-toggle .mdc-switch.mdc-switch--unselected:disabled .mdc-switch__icon {
  fill: black;
}

.readOnly .mat-mdc-slide-toggle .mdc-switch.mdc-switch--selected:disabled .mdc-switch__icon {
  fill: black;
}

.mat-mdc-slide-toggle .mdc-switch.mdc-switch--selected:disabled .mdc-switch__icons {
  opacity: 0.8 !important;
}

.mat-mdc-slide-toggle .mdc-switch.mdc-switch--unselected:disabled .mdc-switch__icons {
  opacity: 0.8 !important;
}

mat-label {
  color: mat.get-color-from-palette($theme-primary, 700) !important;
}

mat-form-field.mat-focused .mdc-text-field.mdc-text-field--focused {
  border-color: mat.get-color-from-palette($theme-primary) !important;
}

i-tabler.routeIcon {
  color: mat.get-color-from-palette($theme-primary, 500) !important;
}

.children-menu-item i-tabler {
  color: mat.get-color-from-palette($theme-warn, 500) !important;
}

.children-menu-item mat-list-item:dir(ltr) {
  margin-left: 10px !important;
  margin-right: 0 !important;
}

.children-menu-item mat-list-item:dir(rtl) {
  margin-right: 10px !important;
  margin-left: 0 !important;
}

i-tabler.icon-16,
i-tabler.icon-15,
i-tabler.icon-14,
i-tabler.icon-12,
i-tabler.icon-20 {
  color: mat.get-color-from-palette($theme-primary, 500) !important;
}

i-tabler.text-error {
  color: mat.get-color-from-palette($theme-warn, 500) !important;
}

.mat-mdc-table .mdc-data-table__header-row {
  background: mat.get-color-from-palette($theme-accent, 500) !important;
}

.bg-primary,
.topbar {
  background: mat.get-color-from-palette($theme-primary, 500) !important;
}

.horizontal-navbar .parentBox.pactive > a,
.horizontal-navbar .parentBox.pactive > a:hover,
.horizontal-navbar .parentBox.pactive:hover > a {
  background-color: mat.get-color-from-palette($theme-accent, 500) !important;
}

.activeMenu {
  background-color: mat.get-color-from-palette($theme-accent, 500) !important;
}

.topbar .mat-mdc-icon-button,
.topbar .mat-mdc-button {
  color: $white !important;
}

.mat-mdc-outlined-button {
  &.mat-primary {
    border-color: mat.get-color-from-palette($theme-primary, 500) !important;
  }
  &.mat-accent {
    border-color: mat.get-color-from-palette($theme-accent, 500) !important;
  }
  &.mat-warn {
    border-color: mat.get-color-from-palette($theme-warn, 500) !important;
  }
}
