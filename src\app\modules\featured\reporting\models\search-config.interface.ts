export interface ISearchConfig {
  type: SearchConfigType;
  backendParam: string;
  fieldLabel: string;
  mandatory?: boolean;
  position?: number;
  idField?: string;
  placeholder?: string;
  
  // For dropdown and multiSelectDropdown
  options?: IDropdownOption[];
  
  // For searchBox
  accountNo?: boolean;
  accountName?: boolean;
  nameEnglish?: boolean;
  nameArabic?: boolean;
  itemCode?: boolean;
  defaultSearchType?: string;
  
  // For radioGroup
  radioOptions?: IRadioOption[];
  
  // For date fields
  max?: Date;
  min?: Date;
  
  // For validation
  validators?: any[];
  
  // For conditional display
  dependsOn?: string;
  showWhen?: (formValue: any) => boolean;
}

export interface IDropdownOption {
  value: any;
  display: string;
  name?: string;
  year?: number;
  [key: string]: any;
}

export interface IRadioOption {
  value: any;
  display: string;
}

export type SearchConfigType = 
  | 'dropdown'
  | 'multiSelectDropdown'
  | 'input'
  | 'dateRange'
  | 'date'
  | 'accountSearch'
  | 'searchBox'
  | 'checkbox'
  | 'radioGroup';

// Common search config templates
export class SearchConfigTemplates {
  static branchConfig(): ISearchConfig {
    return {
      type: 'multiSelectDropdown',
      backendParam: 'branchId',
      fieldLabel: 'branch',
      mandatory: true,
      position: 1,
      idField: 'branchId'
    };
  }

  static warehouseConfig(): ISearchConfig {
    return {
      type: 'multiSelectDropdown',
      backendParam: 'warehouseId',
      fieldLabel: 'warehouse',
      mandatory: false,
      position: 2,
      idField: 'warehouseId',
      dependsOn: 'branchId'
    };
  }

  static categoryConfig(): ISearchConfig {
    return {
      type: 'multiSelectDropdown',
      backendParam: 'categoryId',
      fieldLabel: 'category',
      mandatory: false,
      position: 3,
      idField: 'categoryId'
    };
  }

  static yearConfig(): ISearchConfig {
    return {
      type: 'dropdown',
      backendParam: 'yearId',
      fieldLabel: 'year',
      mandatory: true,
      position: 4,
      idField: 'yearId',
      dependsOn: 'branchId'
    };
  }

  static dateRangeConfig(): ISearchConfig {
    return {
      type: 'dateRange',
      backendParam: 'issueDate',
      fieldLabel: 'dateRange',
      mandatory: false,
      position: 5
    };
  }

  static searchBoxConfig(): ISearchConfig {
    return {
      type: 'searchBox',
      backendParam: 'searchBoxForm',
      fieldLabel: 'search',
      mandatory: false,
      position: 0,
      itemCode: true,
      nameEnglish: true,
      nameArabic: true,
      defaultSearchType: 'itemCode'
    };
  }

  static outputFormatConfig(): ISearchConfig {
    return {
      type: 'radioGroup',
      backendParam: 'options',
      fieldLabel: 'outputFormat',
      mandatory: false,
      position: 10,
      radioOptions: [
        { value: 'PDF', display: 'PDF' },
        { value: 'XLS', display: 'XLS' },
        { value: 'CSV', display: 'CSV' }
      ]
    };
  }

  static showDetailedConfig(): ISearchConfig {
    return {
      type: 'checkbox',
      backendParam: 'showDetailed',
      fieldLabel: 'showDetailed',
      mandatory: false,
      position: 8
    };
  }

  static showGroupedConfig(): ISearchConfig {
    return {
      type: 'checkbox',
      backendParam: 'showGrouped',
      fieldLabel: 'showGrouped',
      mandatory: false,
      position: 9
    };
  }
}
