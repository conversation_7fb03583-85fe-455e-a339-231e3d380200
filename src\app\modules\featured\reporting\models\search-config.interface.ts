export interface ISearchConfig {
  field: string;
  type: 'dropdown' | 'multiSelectDropdown' | 'input' | 'dateRange' | 'date' | 'checkbox' | 'radio' | 'accountSearch';
  mandatory: boolean;
  placeholder: string;
  position: number;
  isAdvanced: boolean;
  fieldLabel: string;
  backendParam: string;
  idField: string;
  options?: any[]; // For radio buttons or static options
  validation?: {
    required?: boolean;
    minLength?: number;
    maxLength?: number;
    pattern?: string;
  };
}
