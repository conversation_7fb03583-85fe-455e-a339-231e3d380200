import { navItems } from 'src/app/layouts/full/app-navigation-common-menus';
import { DashboardModulesHolder } from 'src/app/modules/shared/components/dashboard-modules-holder/modulesHolder';

export const productModulesList: DashboardModulesHolder[] = navItems
  .filter(item => item.route) // Exclude items without a route
  .map(item => ({
    moduleName: item.displayName,
    moduleDescription: `Manage ${item.displayName}.`,
    modulePermission: item.permissions,
    moduleRouterLink: item.route,
    moduleType: 'mainModule',
  }));
