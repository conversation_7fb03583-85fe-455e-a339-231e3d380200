<app-dialog-header [title]="'branchSwitcher.title' | translate"></app-dialog-header>

<mat-dialog-content>
  {{ 'branchSwitcher.currentlyLoggedOn' | translate }}:
  <span class="current-branch">{{ data.currentBranch }}</span> /
  <span class="current-year">{{ data.currentYear }}</span>
  <div class="branch-switcher-content">
    <mat-label>{{ 'branchSwitcher.selectBranch' | translate }}</mat-label>
    <mat-form-field class="w-100" appearance="outline">
      <mat-select [(value)]="selectedBranch" (selectionChange)="onBranchChange($event.value)">
        <mat-option *ngFor="let branch of data.branches" [value]="branch">
          {{ branch.branchName }}
        </mat-option>
      </mat-select>
    </mat-form-field>

    <mat-label>{{ 'branchSwitcher.selectYear' | translate }}</mat-label>
    <mat-form-field class="w-100" appearance="outline">
      <mat-select [(value)]="selectedYear" (selectionChange)="onYearChange($event.value)">
        <mat-option *ngFor="let year of selectedBranch?.years" [value]="year">
          {{ year.year }}
        </mat-option>
      </mat-select>
    </mat-form-field>
  </div>
</mat-dialog-content>

<mat-dialog-actions align="center">
  <button (click)="cancel()" mat-button>{{ 'branchSwitcher.cancel' | translate }}</button>
  <button
    [disabled]="
      !selectedBranch ||
      !selectedYear ||
      (data.currentBranch === selectedBranch?.branchName && data.currentYear === selectedYear?.year)
    "
    (click)="switchBranch()"
    mat-raised-button
    color="primary">
    {{ 'branchSwitcher.switch' | translate }}
  </button>
</mat-dialog-actions>
