import { Component, OnInit } from '@angular/core';
import { DashboardModulesHolder } from 'src/app/modules/shared/components/dashboard-modules-holder/modulesHolder';
import { navItems } from 'src/app/layouts/full/app-navigation-common-menus';

@Component({
  selector: 'app-financedashboard',
  templateUrl: './financedashboard.component.html',
  styleUrls: ['./financedashboard.component.scss'],
})
export class FinancedashboardComponent implements OnInit {
  productModulesList: DashboardModulesHolder[] = [];

  constructor() {
    // Constructor can be used for dependency injection if needed
  }

  ngOnInit(): void {
    const financeMenu = navItems.find(item => item.route === 'finance');
    if (financeMenu && financeMenu.children) {
      this.productModulesList = financeMenu.children.map(child => ({
        moduleName: child.displayName,
        moduleDescription: `Manage ${child.displayName}.`,
        modulePermission: child.permissions,
        moduleRouterLink: `../${child.route.split('/').pop()}`,
        moduleType: 'subModule',
      }));
    }
  }
}
