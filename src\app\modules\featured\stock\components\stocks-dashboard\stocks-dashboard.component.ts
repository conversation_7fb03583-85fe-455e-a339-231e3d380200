import { Component } from '@angular/core';
import { DashboardModulesHolder } from 'src/app/modules/shared/components/dashboard-modules-holder/modulesHolder';

@Component({
  selector: 'app-stocks-dashboard',
  templateUrl: './stocks-dashboard.component.html',
  styleUrls: ['./stocks-dashboard.component.scss'],
})
export class StocksDashboardComponent {
  productModulesList: DashboardModulesHolder[] = [
    {
      moduleName: 'navigationMenus.openquantityAdjustment',
      moduleDescription: 'Manage and update Open Quantity.',
      modulePermission: ['OpenQtyAdjustments.Create', 'AllPermissions'],
      moduleRouterLink: '../adjustments',
      moduleType: 'subModule',
    },
    {
      moduleName: 'navigationMenus.stockTracking',
      moduleDescription: 'Manage stock adjustments.',
      modulePermission: ['Adjustments.PriceUpdate', 'AllPermissions'],
      moduleRouterLink: '../stockadjustment',
      moduleType: 'subModule',
    },
    {
      moduleName: 'navigationMenus.priceAdjustment',
      moduleDescription: 'Manage unit price adjustments.',
      modulePermission: ['UnitPriceAdjustment.Create', 'AllPermissions'],
      moduleRouterLink: '../priceupdate',
      moduleType: 'subModule',
    },
    {
      moduleName: 'navigationMenus.branchPartner',
      moduleDescription: 'Manage unit price adjustments.',
      modulePermission: ['UnitPriceAdjustment.Create', 'AllPermissions'],
      moduleRouterLink: '../branchpartner',
      moduleType: 'subModule',
    },
    {
      moduleName: 'navigationMenus.stockTransfer',
      moduleDescription: 'Manage unit price adjustments.',
      modulePermission: ['UnitPriceAdjustment.Create', 'AllPermissions'],
      moduleRouterLink: '../transfer',
      moduleType: 'subModule',
    },
  ];
}
