import { Injectable } from '@angular/core';
import { UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import moment from 'moment';
import { CookieService } from 'ngx-cookie-service';
import { BehaviorSubject, Observable, forkJoin } from 'rxjs';
import { catchError, map, tap } from 'rxjs/operators';

import { CategoryService } from 'src/app/core/api/category.service';
import { CommonService } from 'src/app/core/api/common.service';
import { StoreService } from 'src/app/core/api/store.service';
import { UnitService } from 'src/app/core/api/unit.service';
import { AuthService } from 'src/app/modules/core/core/services/auth.service';
import { BranchService as CoreBranchService } from 'src/app/modules/core/core/services/branch.service';
import { MultilingualService } from 'src/app/modules/core/core/services/multilingual.service';

import { Branch } from '../../catalog/models/branch';
import { Category } from '../../catalog/models/category';
import { CategoryParams } from '../../catalog/models/categoryParams';
import { WareHouse } from '../../catalog/models/store';
import { Units } from '../../catalog/models/units';
import { BranchParams } from '../../settings/models/branchParams';
import { ReportParams } from '../../settings/models/reportParams';
import { StoreParams } from '../../settings/models/storeParams';
import { BranchService } from '../../settings/services/branch.service';
import { ReportService } from '../../settings/services/report.service';
import { Report, ReportData, SearchConfig } from '../models/report-template';

// Interfaces
export interface YearBranch {
  branchId: string | number;
  years: object[];
}

export interface ItemCode {
  itemId: string | number;
  itemCode: string;
  nameEnglish: string;
  nameArabic: string;
}

export interface ReportResult {
  reportData: object[];
  columnsToDisplay: string[];
  totalRecordsCount: number;
  totalPages: number;
  morePages: boolean;
}

export interface ReportState {
  loading: boolean;
  isReportPulling: boolean;
  categoryList: Category[];
  branchList: Branch[];
  warehouseList: object[];
  yearIdList: YearBranch[];
  filteredYearIdList: object[];
  allWarehouses: WareHouse[];
  itemCodeList: ItemCode[];
  filteredItemCodes: ItemCode[];
  unitList: object[];
  reportTypeList: ReportData;
  reportData: object[];
  selectedReportType: Report | null;
  selectedReportEndPoint: string;
  selectedJasperReportEndPoint: string;
  templateId: number | null;
  displayedColumns: string[];
  allColumns: string[];
  visibleColumns: string[];
  totalItems: number;
  totalPages: number;
  morePages: boolean;
  selectedItemId: string | number | null;
  searchConfigs: SearchConfig[];
}

@Injectable({
  providedIn: 'root',
})
export class UserReportService {
  private readonly initialState: ReportState = {
    loading: true,
    isReportPulling: false,
    categoryList: [],
    branchList: [],
    warehouseList: [],
    yearIdList: [],
    filteredYearIdList: [],
    allWarehouses: [],
    itemCodeList: [],
    filteredItemCodes: [],
    unitList: [],
    reportTypeList: [],
    reportData: [],
    selectedReportType: null,
    selectedReportEndPoint: '',
    selectedJasperReportEndPoint: '',
    templateId: null,
    displayedColumns: [],
    allColumns: [],
    visibleColumns: [],
    totalItems: 0,
    totalPages: 0,
    morePages: false,
    selectedItemId: null,
    searchConfigs: [],
  };

  private readonly stateSubject = new BehaviorSubject<ReportState>(this.initialState);
  public readonly state$ = this.stateSubject.asObservable();

  constructor(
    private branchService: BranchService,
    private storeService: StoreService,
    private categoryService: CategoryService,
    private authService: AuthService,
    private reportService: ReportService,
    private fb: UntypedFormBuilder,
    private unitApiService: UnitService,
    private cookieService: CookieService,
    private translateService: MultilingualService,
    private branchServiceAll: CoreBranchService,
    private commonService: CommonService
  ) {}

  /**
   * Get current state snapshot
   */
  get currentState(): ReportState {
    return this.stateSubject.value;
  }

  /**
   * Update state partially
   */
  private updateState(partialState: Partial<ReportState>): void {
    this.stateSubject.next({ ...this.currentState, ...partialState });
  }

  /**
   * Reset state to initial values
   */
  resetState(): void {
    this.stateSubject.next(this.initialState);
  }

  /**
   * Load all dropdown data required for the report
   */
  loadAllDropdownData(): Observable<boolean> {
    this.updateState({ loading: true });

    const requests = [
      this.categoryService.getAllCategories(new CategoryParams()),
      this.branchService.getAllBranchesWithYear(new BranchParams()),
      this.storeService.getAllStores(new StoreParams()),
      this.unitApiService.getAllUnits(),
      this.reportService.getAllReportType(),
      this.branchServiceAll.getUserBranchesYearId(this.authService.getCompanyID),
    ];

    return forkJoin(requests).pipe(
      tap(results => {
        const [categories, branches, stores, units, reportTypes, yearIds] = results as [
          Category[],
          Branch[],
          WareHouse[],
          Units[],
          ReportData,
          YearBranch[]
        ];

        this.updateState({
          categoryList: Array.isArray(categories) ? categories : [],
          branchList: Array.isArray(branches) ? branches : [],
          allWarehouses: Array.isArray(stores) ? stores : [],
          unitList: Array.isArray(units) ? units : [],
          reportTypeList: reportTypes,
          yearIdList: Array.isArray(yearIds)
            ? yearIds.filter((y: YearBranch) => y && y.branchId && y.years)
            : [],
          loading: false,
        });
      }),
      map(() => true),
      catchError(error => {
        console.error('Error loading dropdown data:', error);
        this.updateState({ loading: false });
        throw error;
      })
    );
  }

  /**
   * Filter year IDs based on selected branch
   */
  filterYearIdForSelectedBranch(branchId: string | number | null): object[] {
    if (!branchId) {
      this.updateState({ filteredYearIdList: [] });
      return [];
    }

    const filteredYears = this.flattenYearListForBranch(branchId);
    this.updateState({ filteredYearIdList: filteredYears });
    return filteredYears;
  }

  /**
   * Flatten year list for a specific branch
   */
  private flattenYearListForBranch(branchId: string | number): object[] {
    return this.currentState.yearIdList
      .filter(year => branchId === year.branchId)
      .map(filteredYear => filteredYear.years)
      .reduce((acc, val) => acc.concat(val), []);
  }

  /**
   * Set selected item ID
   */
  setSelectedItemId(itemId: string | number | null): void {
    this.updateState({ selectedItemId: itemId });
  }

  /**
   * Filter warehouses by branch
   */
  filterWarehousesByBranch(branchId: string | number | null): object[] {
    if (!branchId) {
      this.updateState({ warehouseList: [] });
      return [];
    }

    const filtered = this.currentState.allWarehouses.filter(item => item.branchId === branchId);

    this.updateState({ warehouseList: filtered });
    return filtered;
  }

  /**
   * Get report data with parameters
   */
  getReportData(params: ReportParams): Observable<ReportResult> {
    this.updateState({ isReportPulling: true });

    return this.reportService.getInventoryReports(params).pipe(
      tap((result: ReportResult) => {
        const currentState = this.currentState;
        const displayedColumns =
          currentState.displayedColumns.length === 0
            ? this.translateService.updateDisplayedColumns(result?.columnsToDisplay)
            : currentState.displayedColumns;

        this.updateState({
          reportData: result.reportData,
          displayedColumns,
          allColumns: displayedColumns,
          visibleColumns:
            currentState.visibleColumns.length === 0
              ? [...displayedColumns]
              : currentState.visibleColumns,
          totalItems: result.totalRecordsCount,
          totalPages: result.totalPages,
          morePages: result.morePages,
          isReportPulling: false,
        });
      }),
      catchError(error => {
        console.error('Error fetching report data:', error);
        this.updateState({ isReportPulling: false });
        throw error;
      })
    );
  }

  /**
   * Download report as PDF
   */
  downloadReport(params: ReportParams): Observable<unknown> {
    const downloadParams = { ...params, type: 'PDF' } as ReportParams;

    return this.reportService.getInventoryJasperReports(downloadParams).pipe(
      tap(() => console.log('Report download initiated successfully')),
      catchError(error => {
        console.error('Error downloading report:', error);
        throw error;
      })
    );
  }

  /**
   * Create and configure form based on search configs
   */
  createForm(searchConfigs: SearchConfig[]): UntypedFormGroup {
    const form = this.fb.group({});

    searchConfigs.forEach(config => {
      const validators = config.mandatory ? [Validators.required] : [];
      form.addControl(config.backendParam, this.fb.control(null, validators));

      if (config.type === 'dateRange') {
        form.addControl(config.backendParam + 'From', this.fb.control(null, validators));
        form.addControl(config.backendParam + 'To', this.fb.control(null, validators));
      }
    });

    // Apply stored values from cookies
    const branchIdFromCookie = +this.cookieService.get('branchId');
    const yearIdFromCookie = +this.cookieService.get('yearId');

    if (branchIdFromCookie || yearIdFromCookie) {
      form.patchValue({
        branchId: branchIdFromCookie || null,
        yearId: yearIdFromCookie || null,
      });
    }

    return form;
  }

  /**
   * Build report parameters from form values
   */
  buildReportParams(
    form: UntypedFormGroup,
    currentPage: number,
    pageSize: number,
    sortFields?: string[],
    sortDir?: string,
    sortBy?: string
  ): ReportParams {
    const state = this.currentState;
    const branchIdValue = form?.get('branchId')?.value;

    return {
      yearIds: [],
      branchId: branchIdValue ? [branchIdValue] : [],
      warehouseId: form?.get('warehouseIds')?.value || [],
      categoryId: form?.get('categoryIds')?.value || [],
      unitId: form?.get('unitIds')?.value || [],
      type: undefined,
      searchString: form.get('searchString')?.value || '',
      searchType: '',
      showGrouped: undefined,
      showDetailed: undefined,
      reportType: state.selectedReportType?.name ?? '',
      page: currentPage + 1,
      pageSize: pageSize,
      templateId: state.templateId ?? 0,
      sortFields: sortFields || [],
      sortDir: sortDir || '',
      sortBy: sortBy || '',
      yearId: form.get('yearId')?.value || '',
      itemId: state.selectedItemId ? String(state.selectedItemId) : '',
      date: this.formatDate(form?.get('date')?.value),
      dateFrom: this.formatDate(form?.get('dateFrom')?.value),
      dateTo: this.formatDate(form?.get('dateTo')?.value),
      endPoint: state.selectedReportEndPoint || '',
      jasperEndPoint: state.selectedJasperReportEndPoint || '',
    };
  }

  /**
   * Select and configure report type
   */
  selectReportType(report: Report): void {
    this.updateState({
      selectedReportType: report,
      templateId: report.id,
      selectedReportEndPoint: String(report.endPoint ?? ''),
      selectedJasperReportEndPoint: String(report.jasperEndPoint ?? ''),
      searchConfigs: report.searchConfigs,
    });
  }

  /**
   * Reset report type and related data
   */
  resetReportType(): void {
    this.updateState({
      selectedReportType: null,
      searchConfigs: [],
      reportData: [],
      visibleColumns: [],
      displayedColumns: [],
      templateId: null,
      selectedReportEndPoint: '',
      selectedJasperReportEndPoint: '',
    });
  }

  /**
   * Toggle column visibility
   */
  toggleColumn(column: string, checked: boolean): void {
    const currentVisible = [...this.currentState.visibleColumns];

    if (checked) {
      if (!currentVisible.includes(column)) {
        currentVisible.push(column);
      }
    } else if (currentVisible.length > 1) {
      const index = currentVisible.indexOf(column);
      if (index > -1) {
        currentVisible.splice(index, 1);
      }
    }

    this.updateState({ visibleColumns: currentVisible });
  }

  /**
   * Get list for specific field
   */
  getListForField(field: string): object[] {
    const state = this.currentState;
    switch (field) {
      case 'branchId':
        return state.branchList;
      case 'yearId':
        return state.filteredYearIdList;
      case 'warehouseIds':
        return state.warehouseList;
      case 'unitIds':
        return state.unitList;
      case 'searchString':
        return state.filteredItemCodes;
      case 'categoryIds':
        return state.categoryList;
      case 'reportType':
        return state.reportTypeList;
      default:
        return [];
    }
  }

  /**
   * Format date using moment
   */
  private formatDate(date: unknown): string {
    return date ? moment(date).format('YYYY-MM-DD') : '';
  }

  /**
   * Validate form and play error sound if invalid
   */
  validateForm(form: UntypedFormGroup): boolean {
    form.markAllAsTouched();
    if (!form.valid) {
      this.commonService.playErrorSound();
      return false;
    }
    return true;
  }
}
