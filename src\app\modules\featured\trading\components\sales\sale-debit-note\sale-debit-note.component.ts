import { animate, state, style, transition, trigger } from '@angular/animations';
import { Directionality } from '@angular/cdk/bidi';
import { Component, NgZone, ViewChild } from '@angular/core';
import {
  AbstractControl,
  UntypedFormArray,
  UntypedFormBuilder,
  UntypedFormControl,
  UntypedFormGroup,
  Validators,
} from '@angular/forms';
import { MatDialog, MatDialogConfig } from '@angular/material/dialog';
import { MatSelectChange } from '@angular/material/select';
import { MatTable } from '@angular/material/table';
import { ActivatedRoute, Router } from '@angular/router';
import { CustomValidators } from 'ngx-custom-validators';
import { ToastrService } from 'ngx-toastr';
import { BehaviorSubject, forkJoin, of } from 'rxjs';
import { CostCentresApiService } from 'src/app/core/api/accounts/cost-centres-api.service';
import { CommonService } from 'src/app/core/api/common.service';
import { StaticDataService } from 'src/app/core/api/static-data.service';
import { DistributorService } from 'src/app/core/api/trading/distributor.service';
import { SalesService } from 'src/app/core/api/trading/sales.service';
import { ZatcaService } from 'src/app/core/api/trading/zatca.service';
import { ActionType } from 'src/app/core/enums/actionType';
import { ICustomer, ICustomerView } from 'src/app/core/interfaces/customer';
import { IDistributor } from 'src/app/core/interfaces/distributor';
import { InvalidQuantity } from 'src/app/core/interfaces/error';
import { IPaymentViewData } from 'src/app/core/interfaces/payment';
import {
  IInvoice,
  ISaleDetails,
  ISalesItem,
  SalesIntegratedCreateResponse,
  SaletransactionTypes,
  ZatcaExceptionResponse,
} from 'src/app/core/interfaces/sales';
import { DistributorParams } from 'src/app/core/models/params/distributorParams';
import { SalesIntegeratedParams, SalesParams } from 'src/app/core/models/params/salesParams';
import { displayValue, miscOptions } from 'src/app/core/models/wrappers/displayValue';
import { convertDateForBE } from 'src/app/core/utils/date-utils';
import { AuthService } from 'src/app/modules/core/core/services/auth.service';
import { CostCentre } from 'src/app/modules/featured/accounts/models/costCentre';
import { IInventory } from 'src/app/modules/featured/catalog/models/product';
import { StandardPaymentsComponent } from 'src/app/modules/shared/components/standard-payments/standard-payments.component';
import { CustomerSelectionComponent } from '../../customers/customer-selection/customer-selection.component';
import { SalesCalculation } from '../sales-calculation';
import { SaleDebitNoteModalComponent } from './sale-debit-note-modal/sale-debit-note-modal.component';
import {
  PrintDialogComponent,
  PrintDialogData,
} from 'src/app/modules/shared/components/print-dialog/print-dialog.component';
@Component({
  selector: 'app-sale-debit-note',
  templateUrl: './sale-debit-note.component.html',
  styleUrls: ['./sale-debit-note.component.scss'],
  animations: [
    trigger('detailExpand', [
      state('collapsed', style({ height: '0px', minHeight: '0' })),
      state('expanded', style({ height: '*' })),
      transition('expanded <=> collapsed', animate('225ms cubic-bezier(0.4, 0.0, 0.2, 1)')),
    ]),
  ],
})
export class SaleDebitNoteComponent extends SalesCalculation {
  @ViewChild(MatTable) table: MatTable<AbstractControl[]>;
  @ViewChild('paymentForm') private paymentForm: StandardPaymentsComponent;
  @ViewChild('customerSelection') private customerSelection: CustomerSelectionComponent;
  // Matatable
  dataSource = new BehaviorSubject<AbstractControl[]>([]);
  displayedColumns: string[] = [
    'itemCode',
    'itemName',
    'unitName',
    'priceType',
    'quantity',
    'price',
    'discount',
    'vatAmount',
    'subtotal',
    'notes',
    'action',
  ];
  expandedFieldNames: Array<displayValue | miscOptions>;
  columnsToDisplayWithExpand = [...this.displayedColumns, 'expand'];
  // Holders

  priceTypes: displayValue[];
  distributorAccounts: IDistributor[] = [];
  costCentreAccounts: CostCentre[] = [];
  currentSalesData: IInvoice;
  saleDetails: ISaleDetails;
  products: IInventory[];
  salesId: string;
  quantityUpdated: boolean;
  customerViewData: ICustomerView = <ICustomerView>{};
  paymentViewData: IPaymentViewData = <IPaymentViewData>{};
  // Main Form
  itemRows: UntypedFormArray = this.formBuilder.array([]);
  salesForm: UntypedFormGroup = this.formBuilder.group({
    issueDate: new UntypedFormControl({ value: null, disabled: true }),
    orderNumber: new UntypedFormControl({ value: null, disabled: true }),
    orderDate: new UntypedFormControl(new Date(), Validators.required),
    costCentreId: new UntypedFormControl({ value: null, disabled: true }),
    distributorAccountId: new UntypedFormControl({ value: null, disabled: true }),
    invoiceDiscount: new UntypedFormControl(0),
    notes: new UntypedFormControl({ value: null, disabled: true }),
    items: this.itemRows,
    noteIssueDate: new UntypedFormControl({ value: new Date(), disabled: true }),
    noteRemark: new UntypedFormControl(null),
  });
  loading = true;
  mode: ActionType;
  initialFormStatus: UntypedFormArray = this.formBuilder.array([]);
  expandedRowIndex: number | null = null;
  constructor(
    private formBuilder: UntypedFormBuilder,
    private dialog: MatDialog,
    private costCentresApiService: CostCentresApiService,
    private distributorService: DistributorService,
    private salesService: SalesService,
    private toastr: ToastrService,
    private authService: AuthService,
    private router: Router,
    private route: ActivatedRoute,
    private staticDataService: StaticDataService,
    private readonly zone: NgZone,
    private commonService: CommonService,
    private zatcaService: ZatcaService,
    private direction: Directionality
  ) {
    super();
  }

  isQuantityUpdated(): void {
    console.log(
      'isQuantityUpdated-form',
      this.salesForm,
      this.salesForm.valid,
      this.items.controls.some(control => {
        return control.get('quantity').value > 0;
      })
    );
    this.quantityUpdated =
      this.salesForm.valid &&
      this.items.controls.some(control => {
        return control.get('quantity').value > 0;
      });
  }

  get canSelectProduct() {
    return !(this.mode === ActionType.view || this.mode === ActionType.debitNote);
  }

  get actionType() {
    return ActionType;
  }

  get canDeleteProduct() {
    return !(this.mode === ActionType.view || this.mode === ActionType.debitNote);
  }

  get isFormUpdated(): boolean {
    return this.commonService.isEqual(this.initialFormStatus.value, this.itemRows.value);
  }

  get itemsArray(): UntypedFormArray {
    return this.salesForm.get('items') as UntypedFormArray;
  }

  ngOnInit(): void {
    this.loading = true;
    this.mode = this.route.snapshot.data['mode'];
    this.route.params.subscribe(params => {
      this.salesId = params['id'];
      this.getAllDropDownData();
    });
  }

  setPageDisabled(): void {
    if (this.mode === ActionType.view) {
      this.salesForm.disable();
    }
  }

  patchData(data: ISaleDetails): void {
    const viewData: ICustomerView = {};
    viewData.isViewMode = true;
    viewData.customer = data.customer;
    viewData.existingClient = data.existingClient;
    this.customerViewData = viewData;
    this.saleDetails = data;
    if (data?.items && data.items.length) {
      data.items.forEach(element => {
        this.addSaleDetailForView(element);
      });
    }
    this.salesForm.patchValue({
      issueDate: data.issueDate,
      orderNumber: data.orderNumber,
      orderDate: data.orderDate,
      costCentreId: data.costCentreId,
      distributorAccountId: data.distributorAccountId,
      invoiceDiscount: data.invoiceDiscount,
      notes: data.notes,
    });
    // save for compare
    this.initialFormStatus = this.commonService.deepCloneFormArray(this.itemRows);
  }

  getAllDropDownData(): void {
    const salesParams = new SalesParams();
    salesParams.transactionType = SaletransactionTypes.sales;
    const salesById = this.salesId
      ? this.salesService.getSalesById(this.salesId, salesParams)
      : of(null);
    const distributorAccounts = this.distributorService.getAllDistributors(new DistributorParams());
    const costCentreAccounts = this.costCentresApiService.getAllCostCentres();
    forkJoin([distributorAccounts, costCentreAccounts, salesById]).subscribe(results => {
      console.log(results);
      this.distributorAccounts = results[0]?.distributors;
      this.costCentreAccounts = results[1];
      if (results[2]) {
        this.currentSalesData = results[2];
        this.patchData(results[2]);
      }
      this.loading = false;
    });
    this.expandedFieldNames = this.staticDataService.getSalesExpandedColumns;
    this.priceTypes = this.staticDataService.getSalesPriceTypes;
  }

  addSaleDetailForView(product: ISalesItem): void {
    product.quantity = 0;
    const saleDetail: ISalesItem = {
      returnableQty: product.returnableQty,
      transactionItemId: product.transactionItemId,
      itemId: product.itemId,
      itemCode: product.itemCode,
      quantity: product.quantity,
      itemUnitId: product.itemUnitId,
      price: product.price,
      subtotal: product.subtotal,
      vat: product.product.vat,
      vatAmount: product.vatAmount,
      product: product.product,
      discount: product.discount,
      isGeneralDscntMethod: product.product.isGeneralDscntMethod,
      priceType: product.priceType,
      warehouseName: product.warehouseName,
      warehouseId: product.warehouseId,
      itemName: product.itemName,
      unitName: product.unitName,
      subTotalVat: product.subTotalVat,
    };
    this.onAddNewItem(saleDetail);
    this.dataSource.next(this.itemRows.controls);
    this.formItems(this.itemRows);
    this.updateAllRelevantCounts();
  }

  getControlsIndexFromArray(controlErrors: InvalidQuantity) {
    console.log('getControlsIndexFromArray -> ', controlErrors);
    return this.itemsArray.controls.findIndex(product => {
      console.log(product);
      return (
        product.value.itemCode === controlErrors.itemCode &&
        product.value.warehouseId === controlErrors.warehouseId &&
        product.value.unitName === controlErrors.unitName
      );
    });
  }

  onPriceTypeChange(event: MatSelectChange, index: number) {
    console.log('onPriceTypeChange -> ', event, event.source.triggerValue, index);
    const data = this.getSpecificFormArray(index);
    const priceType = this.priceTypes.find(data => data.value === event.value);
    const productData = data.get('product').value;
    data.patchValue({
      price: productData[priceType.display],
      priceType: +event.value,
      subtotal: this.countSubTotal(
        data.get('quantity').value,
        data.get('price').value,
        data.get('discount').value,
        data.get('isGeneralDscntMethod').value,
        this.subTotalVat(
          data.get('price').value,
          data.get('vat').value,
          data.get('discount').value,
          data.get('isGeneralDscntMethod').value
        )
      ),
      subTotalVat:
        this.subTotalVat(
          data.get('price').value,
          data.get('vat').value,
          data.get('discount').value,
          data.get('isGeneralDscntMethod').value
        ) * data.get('quantity').value,
      vatAmount:
        this.vatAmount(
          data.get('price').value,
          data.get('discount').value,
          data.get('isGeneralDscntMethod').value,
          data.get('vat').value
        ) * data.get('quantity').value,
    });
    this.dataSource.next(this.itemRows.controls);
    this.formItems(this.itemRows);
    this.updateAllRelevantCounts();
  }

  onQuantityChange(event, index: number) {
    console.log('onQuantityChange -> ', event.srcElement.value, index);
    const data = this.getSpecificFormArray(index);
    console.log('onQuantityChange', data);
    data.patchValue({
      quantity: +event.srcElement.value || 0,
      subtotal: this.countSubTotal(
        event.srcElement.value,
        data.get('price').value,
        data.get('discount').value,
        data.get('isGeneralDscntMethod').value,
        this.subTotalVat(
          data.get('price').value,
          data.get('vat').value,
          data.get('discount').value,
          data.get('isGeneralDscntMethod').value
        )
      ),
      subTotalVat:
        this.subTotalVat(
          data.get('price').value,
          data.get('vat').value,
          data.get('discount').value,
          data.get('isGeneralDscntMethod').value
        ) * event.srcElement.value,
      vatAmount:
        this.vatAmount(
          data.get('price').value,
          data.get('discount').value,
          data.get('isGeneralDscntMethod').value,
          data.get('vat').value
        ) * event.srcElement.value,
    });
    this.dataSource.next(this.itemRows.controls);
    this.formItems(this.itemRows);
    this.updateAllRelevantCounts();
    this.isQuantityUpdated();
  }

  onPriceChange(event, index: number) {
    console.log('onPriceChange -> ', event, index);
    const data = this.getSpecificFormArray(index);
    data.patchValue({
      price: +event.srcElement.value || 0,
      subtotal: this.countSubTotal(
        data.get('quantity').value,
        event.srcElement.value,
        data.get('discount').value,
        data.get('isGeneralDscntMethod').value,
        this.subTotalVat(
          event.srcElement.value,
          data.get('vat').value,
          data.get('discount').value,
          data.get('isGeneralDscntMethod').value
        )
      ),
      subTotalVat:
        this.subTotalVat(
          event.srcElement.value,
          data.get('vat').value,
          data.get('discount').value,
          data.get('isGeneralDscntMethod').value
        ) * data.get('quantity').value,
      vatAmount:
        this.vatAmount(
          event.srcElement.value,
          data.get('discount').value,
          data.get('isGeneralDscntMethod').value,
          data.get('vat').value
        ) * data.get('quantity').value,
    });
    this.dataSource.next(this.itemRows.controls);
    this.formItems(this.itemRows);
    this.updateAllRelevantCounts();
  }

  onDiscountChange(event, index: number) {
    console.log('onDiscountChange -> ', event.srcElement.value, index);
    const data = this.getSpecificFormArray(index);
    data.patchValue({
      discount: +event.srcElement.value || 0,
      subtotal: this.countSubTotal(
        data.get('quantity').value,
        data.get('price').value,
        event.srcElement.value,
        data.get('isGeneralDscntMethod').value,
        this.subTotalVat(
          data.get('price').value,
          data.get('vat').value,
          event.srcElement.value,
          data.get('isGeneralDscntMethod').value
        )
      ),
      subTotalVat:
        this.subTotalVat(
          data.get('price').value,
          data.get('vat').value,
          event.srcElement.value,
          data.get('isGeneralDscntMethod').value
        ) * data.get('quantity').value,
      vatAmount:
        this.vatAmount(
          data.get('price').value,
          event.srcElement.value,
          data.get('isGeneralDscntMethod').value,
          data.get('vat').value
        ) * data.get('quantity').value,
    });
    this.dataSource.next(this.itemRows.controls);
    this.formItems(this.itemRows);
    this.updateAllRelevantCounts();
  }

  getSpecificFormArray(index: number): AbstractControl {
    return (<UntypedFormArray>this.salesForm.get('items')).at(index);
  }

  jumpToNext(event: Event, index: number) {
    console.log('jump to next field  -> ', event, index);
    event.preventDefault();
    const nextField = document.querySelectorAll('.next');
    nextField.forEach((element, index) => {
      if (element === event.target) {
        const nextfield = nextField[index + 1] as HTMLInputElement;
        nextfield.focus();
      }
    });
  }

  onAddNewItem(product: ISalesItem) {
    console.log('onAddNewItem -> ', product);
    if (this.itemsArray && this.itemsArray?.length > 0) {
      this.itemsArray.insert(0, this.addnewFormGroup(product));
    } else {
      this.itemsArray.push(this.addnewFormGroup(product));
    }
  }

  addnewFormGroup(saleData: ISalesItem): UntypedFormGroup {
    console.log('addnewFormGroup -> ', saleData);
    const row = this.formBuilder.group({
      itemId: saleData.itemId,
      itemCode: saleData.itemCode,
      quantity: [
        saleData.quantity,
        Validators.compose([
          Validators.required,
          CustomValidators.lte(saleData.product?.totalQuantityPerUnit),
        ]),
      ],
      itemUnitId: saleData.itemUnitId,
      price: saleData.price,
      subtotal: [saleData.subtotal],
      vat: saleData.vat,
      product: saleData.product,
      discount: [saleData.discount],
      isGeneralDscntMethod: saleData.isGeneralDscntMethod,
      priceType: saleData.priceType,
      warehouseName: saleData.warehouseName,
      itemName: saleData.itemName,
      unitName: saleData.unitName,
      notes: saleData.notes,
      subTotalVat: saleData.subTotalVat,
      warehouseId: saleData.warehouseId,
      vatAmount: [saleData.vatAmount, Validators.compose([CustomValidators.gte(0)])],
      transactionItemId: saleData.transactionItemId,
      returnableQty: saleData.returnableQty,
    });
    return row;
  }

  submitSales(event: Event): void {
    event.preventDefault();
    this.processSalesDebitNotes();
  }

  processSalesDebitNotes(): void {
    const isAllValid = this.paymentForm.paymentFormIsAllValid() && this.salesForm.valid;
    console.log('is all valid', isAllValid);
    if (isAllValid) {
      this.onPostClick();
    } else {
      this.commonService.playErrorSound();
    }
  }

  onPostClick() {
    const dialogConfig = new MatDialogConfig();
    dialogConfig.data = {
      message: 'compareTableDebitNote.debitNoteReturnText',
      form1: this.initialFormStatus,
      form2: this.itemRows,
    };
    dialogConfig.minWidth = '90vw';
    dialogConfig.position = {
      top: '50px',
    };
    dialogConfig.maxHeight = '90vh';
    dialogConfig.panelClass = ['green_theme'];
    dialogConfig.hasBackdrop = true;
    dialogConfig.disableClose = false;
    dialogConfig.autoFocus = false;
    dialogConfig.direction = this.direction.value;
    const dialogRef = this.dialog.open(SaleDebitNoteModalComponent, dialogConfig);
    dialogRef.afterClosed().subscribe(result => {
      console.log('result', result);
      if (result) {
        this.debitProcess();
      }
    });
  }

  debitProcess(zatcaAccept = false): void {
    const data: ISaleDetails = {
      payments: {
        ...this.paymentForm.getPaymentsFormValue(),
        balanceAmount: 0,
        changeAmount: 0,
        grandTotal: this.grandTotal,
        totalDiscount: this.discount,
        totalVat: this.totalVat,
        totalExclVatDiscount: this.totalExcVatDisc,
      },
      referenceDocumentId: this.currentSalesData.id,
      issueDate: convertDateForBE(this.salesForm.get('noteIssueDate').value),
      transactionType: SaletransactionTypes.debitNote,
      notes: this.salesForm.get('noteRemark')?.value ?? null,
      ...this.customerSelection.customerSelectionForm.value,
      items: this.salesForm.get('items').value,
      referenceDocumentDate: this.currentSalesData.issueDate,
    };
    const salesParams = new SalesIntegeratedParams();
    salesParams.areWarningsAccepted = zatcaAccept;
    console.log('DEBIT NOTE structure', data);
    this.salesService.createSalesIntegrated(data, salesParams).subscribe(
      (result: SalesIntegratedCreateResponse) => {
        console.log('sales submit has zatca response', result);
        this.salesDebitResponse(result);
      },
      error => {
        this.loading = false;
        console.log('errors =>', error);
        if (error.status === 422) {
          this.setFormErrors(error);
        }
        this.zatcaErrorResponse(error);
      }
    );
  }

  openZatcaStatusModal(zatcaResponse: ZatcaExceptionResponse): void {
    const dialog = this.zatcaService.openZatcaStatusModal(zatcaResponse, this.direction.value);
    dialog.componentInstance.accepted.subscribe(() => {
      dialog.close();
      this.salesForm.markAllAsTouched();
      this.debitProcess(true);
    });
  }

  salesDebitResponse(result: SalesIntegratedCreateResponse) {
    if (result?.zatcaExceptionResponse) {
      console.log('sales submit has zatca response', result);
      this.openZatcaStatusModal(result.zatcaExceptionResponse);
    } else {
      console.log('sales service response', result);
      //this.toastr.success(`${result.documentNumber} created successfully`);
      this.showPrintDialog(result);
      this.commonService.playSuccessSound();
    }
  }

  zatcaErrorResponse(error) {
    if (
      error &&
      error?.details?.zatcaResponse &&
      (error?.details?.zatcaResponse !== null || error?.details?.zatcaResponse !== undefined)
    ) {
      this.openZatcaStatusModal(error?.details?.zatcaResponse);
    }
  }

  setFormErrors(validationErrors: any): void {
    const serverErrors = validationErrors.details.invalidQuantity;
    serverErrors.forEach((element: InvalidQuantity) => {
      const controlIndex = this.getControlsIndexFromArray(element);
      console.log('index => ', controlIndex);
      const formArray = this.getSpecificFormArray(controlIndex);
      console.log('formArray => ', formArray);
      formArray.get('quantity').setErrors({ serverSideError: element.error });
      formArray.updateValueAndValidity();
    });
  }

  getUpdatedQuantituesEntries() {
    return this.items.controls
      .filter(control => {
        return control.get('quantity').value > 0;
      })
      .map(data => data.value);
  }

  customerTypeSelection(isExistingCustomer: boolean): void {
    if (!isExistingCustomer) {
      this.salesForm.get('distributorAccountId').setValue(null);
    }
  }

  customerProfileSelection(customer: ICustomer): void {
    console.log('customer profile selected ->', customer);
    if (customer) {
      this.patchCustomerProfileData(customer);
    }
  }

  patchCustomerProfileData(customer: ICustomer): void {
    this.salesForm.get('costCentreId').setValue(customer.costCentreId);
    this.salesForm.get('distributorAccountId').setValue(customer.distributorAccountId);
  }

  toggleExpansion(rowIndex: number): void {
    this.expandedRowIndex = this.expandedRowIndex === rowIndex ? null : rowIndex;
  }

  isRowExpanded(index: number): boolean {
    return this.expandedRowIndex === index;
  }

  /**
   * Shows the print dialog after successful sales creation
   */
  private showPrintDialog(result: SalesIntegratedCreateResponse): void {
    const dialogConfig = new MatDialogConfig();
    dialogConfig.minWidth = '400px';
    dialogConfig.maxWidth = '500px';
    dialogConfig.panelClass = 'green_theme';
    dialogConfig.hasBackdrop = true;
    dialogConfig.disableClose = false;
    dialogConfig.autoFocus = true;
    dialogConfig.direction = this.direction.value;

    const dialogData: PrintDialogData = {
      documentResponse: result,
      title: 'Sales Document Created Successfully',
      message: `Your sales document has been created successfully!`,
    };

    dialogConfig.data = dialogData;

    const dialogRef = this.dialog.open(PrintDialogComponent, dialogConfig);

    dialogRef.componentInstance.printRequested.subscribe((shouldPrint: boolean) => {
      if (shouldPrint) {
        this.handlePrintRequest(result);
      } else {
        this.resetPageForNewCreation();
      }
    });

    dialogRef.afterClosed().subscribe((shouldPrint: boolean | null) => {
      if (shouldPrint === null) {
        // User clicked cancel, just reset the page
        this.resetPageForNewCreation();
      }
    });
  }

  /**
   * Handles the print request - opens Invoice Demo component in new window using common service
   */
  private handlePrintRequest(result: SalesIntegratedCreateResponse): void {
    console.log('Print requested for document:', result);
    console.log('Document ID:', result.documentId);
    console.log('Document Number:', result.documentNumber);

    if (result.documentId) {
      console.log('Opening Invoice Demo in new window with params:', {
        documentId: result.documentId,
        transactionType: 'DEBIT_NOTE',
      });

      // Use the common service to open invoice in new window
      const printWindow = this.commonService.openInvoiceInNewWindow(
        result.documentId,
        'DEBIT_NOTE'
      );
      // Use polling to detect when window is closed
      if (printWindow) {
        const checkWindowClosed = setInterval(() => {
          if (printWindow.closed) {
            clearInterval(checkWindowClosed);
            console.log('Invoice Demo window closed', this.mode);
            this.resetPageForNewCreation();
          }
        }, 500);
      }
    } else {
      console.warn('No document ID available for printing');
    }
  }

  resetPageForNewCreation(): void {
    this.router.navigate(['../../'], { relativeTo: this.route });
  }
}
