import { Component, ViewChild, ViewEncapsulation } from '@angular/core';
import { MatSidenav } from '@angular/material/sidenav';
import { AppSettings } from 'src/app/app.config';
import { BusyService } from 'src/app/modules/core/core/services/busy.service';
import { MultilingualService } from 'src/app/modules/core/core/services/multilingual.service';
import { navItems } from './app-navigation-common-menus';

@Component({
  selector: 'app-full',
  templateUrl: './full.component.html',
  styleUrls: [],
  encapsulation: ViewEncapsulation.None,
})
export class FullComponent {
  // Navigation items from sidebar data
  navItems = navItems;

  // Reference to the sidenav element
  @ViewChild('leftsidenav')
  public sidenav: MatSidenav;

  // Layout and view properties
  options: AppSettings;
  constructor(private multilingualService: MultilingualService, public busyService: BusyService) {
    // Get initial options
    this.options = this.multilingualService.getOptions();
  }

  onSidenavOpenedChange(isOpened: boolean) {
    this.options.sidenavOpened = isOpened;

    // Update options in the service
    const updatedOptions = this.multilingualService.getOptions();
    updatedOptions.sidenavOpened = isOpened;
    this.multilingualService.setOptions(updatedOptions);
  }
}
