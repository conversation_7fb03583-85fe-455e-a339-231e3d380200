import {
  AfterViewInit,
  Component,
  Inject,
  OnInit,
  Optional,
  SkipSelf,
  ViewChild,
} from '@angular/core';
import { UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { ActivatedRoute, Router } from '@angular/router';
import { CustomValidators } from 'ngx-custom-validators';
import { ToastrService } from 'ngx-toastr';
import { forkJoin } from 'rxjs';
import { CostCentresApiService } from 'src/app/core/api/accounts/cost-centres-api.service';
import { CommonService } from 'src/app/core/api/common.service';
import { CustomerService } from 'src/app/core/api/trading/customer.service';
import { DistributorService } from 'src/app/core/api/trading/distributor.service';
import { customerIndentifactionCode } from 'src/app/core/configs/dropDownConfig';
import { ActionType } from 'src/app/core/enums/actionType';
import { IActionEventType } from 'src/app/core/interfaces/actionEventType';
import { IDistributor } from 'src/app/core/interfaces/distributor';
import { ISupplier } from 'src/app/core/interfaces/supplier';
import { DistributorParams } from 'src/app/core/models/params/distributorParams';
import { AuthService } from 'src/app/modules/core/core/services/auth.service';
import { Account } from 'src/app/modules/featured/accounts/models/account';
import { CostCentre } from 'src/app/modules/featured/accounts/models/costCentre';
import { ChartOfAccountsService } from 'src/app/modules/featured/accounts/services/chart-of-accounts.service';
import { AddressComponent } from 'src/app/modules/shared/components/address/address.component';
import { ICustomer, ICustomerModalData } from '../../../../../../core/interfaces/customer';

@Component({
  selector: 'app-customer-form',
  templateUrl: './customer-form.component.html',
  styleUrls: ['./customer-form.component.scss'],
})
export class CustomerFormComponent implements OnInit, AfterViewInit {
  @ViewChild('addressForm', { static: false }) addressForm: AddressComponent;
  customerForm: UntypedFormGroup;
  customerId: string;
  parentAccounts: Account[] = [];
  distributorAccounts: IDistributor[] = [];
  costCentreAccounts: CostCentre[] = [];
  mode: ActionType;
  actionMode = ActionType;
  identificationCodes = customerIndentifactionCode;
  constructor(
    @Optional() @SkipSelf() @Inject(MAT_DIALOG_DATA) public modalData: ICustomerModalData,
    @Optional() @SkipSelf() public dialogRef: MatDialogRef<CustomerFormComponent>,
    private authService: AuthService,
    private toastr: ToastrService,
    private customerService: CustomerService,
    private fb: UntypedFormBuilder,
    private route: ActivatedRoute,
    private router: Router,
    private chartOfAccountsService: ChartOfAccountsService,
    private costCentresApiService: CostCentresApiService,
    private distributorService: DistributorService,
    private commonService: CommonService
  ) {}

  get IsViewMode() {
    return this.mode === this.actionMode.view || this.customerForm?.disabled;
  }

  ngOnInit(): void {
    this.initializeForm(this.modalData?.data);
    this.getAllDropDownData();
    this.mode = this.route.snapshot.data['mode'];
    this.route.params.subscribe(params => {
      this.customerId = params['id'];
      if (this.customerId) {
        this.getCustomer(this.customerId);
      }
    });
  }

  ngAfterViewInit(): void {
    if (this.modalData?.data) {
      this.disbaleAllForms();
    }
  }

  getAllDropDownData(): void {
    const parentAccounts =
      this.chartOfAccountsService.getChartOfAccountsByAccountCategoryAndAccountSubCategory(
        'ASSETS',
        'CUSTOMER'
      );
    const distributorAccounts = this.distributorService.getAllDistributors(new DistributorParams());
    const costCentreAccounts = this.costCentresApiService.getAllCostCentres();

    forkJoin([parentAccounts, distributorAccounts, costCentreAccounts]).subscribe(results => {
      console.log(results);
      this.parentAccounts = results[0];
      this.distributorAccounts = results[1]?.distributors;
      this.costCentreAccounts = results[2];
    });
  }

  getCustomer(customerId: string): void {
    this.customerService.getCustomerById(customerId).subscribe(
      response => {
        this.patchData(response);
      },
      error => console.log(error)
    );
  }

  initializeForm(data?: ICustomer | ISupplier) {
    this.customerForm = this.fb.group(
      {
        parentAccountId: [data?.parentAccountId ?? null, Validators.compose([Validators.required])],
        nameArabic: [data?.nameArabic ?? null, Validators.compose([Validators.required])],
        nameEnglish: [data?.nameEnglish ?? null, Validators.compose([Validators.required])],
        hasTransferPrivilege: [
          data?.hasTransferPrivilege ?? false,
          Validators.compose([Validators.required]),
        ],
        hasCardPrivilege: [
          data?.hasCardPrivilege ?? false,
          Validators.compose([Validators.required]),
        ],
        hasCashPrivilege: [
          data?.hasCashPrivilege ?? false,
          Validators.compose([Validators.required]),
        ],
        hasCreditPrivilege: [
          data?.hasCreditPrivilege ?? false,
          Validators.compose([Validators.required]),
        ],
        isFreezed: [data?.isFreezed ?? false, Validators.compose([Validators.required])],
        discountPercent: [data?.discountPercent ?? 0, Validators.compose([Validators.required])],
        paymentToleranceDays: [
          data?.paymentToleranceDays ?? 0,
          Validators.compose([Validators.required]),
        ],
        allowedBalance: [
          data?.allowedBalance ?? **********.0,
          Validators.compose([Validators.required]),
        ],
        phoneNumber: [data?.phoneNumber ?? null, Validators.compose([])],
        vatNumber: [
          data?.vatNumber ?? 0,
          Validators.compose([Validators.required, this.commonService.vatNumberValidator()]),
        ],
        emailId: [data?.emailId ?? null, Validators.compose([CustomValidators.email])],
        accountNumber: [data?.accountNumber ?? null],
        commission: [data?.commission ?? 0],
        yearlyTarget: [data?.yearlyTarget ?? 0],
        address: [data?.address ?? null],
        costCentreId: [data?.costCentreId ?? null],
        distributorAccountId: [data?.distributorAccountId ?? null],
        identification: [data?.identification ?? '', Validators.compose([Validators.required])],
        identificationCode: [
          data?.identificationCode ?? null,
          Validators.compose([Validators.required]),
        ],
      },
      { validators: this.commonService.atLeastOnePrivilegeValidator() }
    );
    this.disableForm();
  }

  patchData(data: ICustomer): void {
    this.initializeForm(data);
  }

  private disableForm(): void {
    if (this.mode === this.actionMode.view) {
      this.disbaleAllForms();
    }
  }

  private disbaleAllForms(): void {
    this.customerForm.disable();
    this.addressForm.disableForm();
  }

  onSubmit(event: IActionEventType) {
    this.addressForm.markFormAsTouched();
    this.customerForm.markAllAsTouched();
    if (this.customerForm && this.customerForm?.valid && this.addressForm.isValid()) {
      // create Mode
      if (event.actionType === this.actionMode.create) {
        console.log('this.customerForm.value', this.customerForm.value);
        this.customerService.createCustomer(this.customerForm.value).subscribe(() => {
          //this.toastr.success('Customer Created Successfully');
          this.commonService.playSuccessSound();
          this.router.navigate(['../'], { relativeTo: this.route });
        });
      }
      // edit Mode
      if (event.actionType === this.actionMode.edit) {
        this.customerService
          .updateCustomer(this.customerForm.value, this.customerId)
          .subscribe(() => {
            //this.toastr.success('Customer Updated Successfully');
            this.commonService.playSuccessSound();
            this.router.navigate(['../../'], { relativeTo: this.route });
          });
      }
    } else {
      this.commonService.scrollToError();
    }
  }

  onNoClick(): void {
    this.dialogRef.close('cancel');
  }
}
