export interface Account {
  accountId: number;
  branchId: number;
  accountNumber: number;
  nameArabic: string;
  nameEnglish: string;
  parentAccountId: number;
  accountLevel: number;
  accountType: string;
  hasDetails: boolean;
  accountCategory: string;
  accountNature: string;
  financialStatementType: string;
  accountSubCategory: string;
  isDepreciating: boolean;
  isHidden: boolean;
  isFreezed: boolean;
  isContraAccount: boolean;
  costAccountNumber: number;
  costCentreId: number;
  note: string;
}

export interface ConfiguredAccount {
  cashSalesAccount: AccountBasic;
  creditSalesAccount: AccountBasic;
  cardSalesAccount: AccountBasic;
  wireTransferSalesAccount: AccountBasic;
  vatSalesAccount: AccountBasic;
  discountSalesAccount: AccountBasic;
  returnSalesAccount: AccountBasic;
  roundingDiscountAccount: AccountBasic;
  cashPurchaseAccount: AccountBasic;
  creditPurchaseAccount: AccountBasic;
  cardPurchaseAccount: AccountBasic;
  wireTransferPurchaseAccount: AccountBasic;
  vatPurchaseAccount: AccountBasic;
  discountPurchaseAccount: AccountBasic;
  returnPurchaseAccount: AccountBasic;
  transferInAccount: AccountBasic;
  transferOutAccount: AccountBasic;
  transferInReturnAccount: AccountBasic;
  transferOutReturnAccount: AccountBasic;
  badInventoryDebitAccount: AccountBasic;
  badInventoryCreditAccount: AccountBasic;
  shortageInventoryDebitAccount: AccountBasic;
  shortageInventoryCreditAccount: AccountBasic;
  surplusInventoryDebitAccount: AccountBasic;
  surplusInventoryCreditAccount: AccountBasic;
  cogsAccount: AccountBasic;
  cogsEndAccount: AccountBasic;
}

export interface AccountBasic {
  accountId: number;
  accountNumber: number;
  nameArabic: string;
  nameEnglish: string;
}
