import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { MatSortModule } from '@angular/material/sort';
import { MatTableModule } from '@angular/material/table';
import { TranslateModule } from '@ngx-translate/core';
import { OrderModule } from 'ngx-order-pipe';
import { SharedModule } from '../../shared/shared.module';
import { AccountsModule } from '../accounts/accounts.module';
import { AccountAutoSearchComponent } from '../accounts/components/accountAutoSearch/account-auto-search.component';
import { ReportDashboardComponent } from './components/dashboard/report-dashboard.component';
import { FilterSummaryComponent } from './components/filter-summary/filter-summary.component';
import { FilterFormComponent } from './components/flter-form/filter-form.component';
import { InventoryReportComponent } from './components/inventory-report/inventory-report.component';
import { ReportSetupComponent } from './components/report-setup/report-setup.component';
import { ReportingRoutingModule } from './reporting-routing.module';
@NgModule({
  declarations: [
    ReportDashboardComponent,
    InventoryReportComponent,
    FilterFormComponent,
    FilterSummaryComponent,
    ReportSetupComponent,
  ],
  imports: [
    CommonModule,
    ReportingRoutingModule,
    SharedModule,
    TranslateModule,
    AccountsModule,
    MatTableModule,
    MatSortModule,
    OrderModule,
  ],
  providers: [AccountAutoSearchComponent],
})
export class ReportingModule {}
