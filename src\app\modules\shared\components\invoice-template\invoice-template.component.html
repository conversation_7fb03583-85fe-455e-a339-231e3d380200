<div class="action-buttons">
  <button (click)="printInvoice()" mat-raised-button color="primary">
    <mat-icon>print</mat-icon>
    Print Invoice
  </button>
</div>

<div class="invoice-container" dir="ltr">
  <div class="invoice-header">
    <div class="header-layout">
      <!-- Left Address (English) -->
      <div class="english-address address">
        <h3>{{ invoice.templateStaticData.optionalText1 }}</h3>
        <p class="company-info">{{ invoice.templateStaticData.optionalText2 }}</p>
        <p class="company-info">{{ invoice.templateStaticData.optionalText3 }}</p>
        <p class="contact-detail">{{ invoice.templateStaticData.optionalContactLine1 }}</p>
        <p class="contact-detail">{{ invoice.templateStaticData.optionalContactLine2 }}</p>
        <p class="contact-detail">{{ invoice.templateStaticData.optionalContactLine3 }}</p>
      </div>

      <!-- Center Logo -->
      <div class="logo">
        <img [src]="companyLogo" alt="Company Logo" />
      </div>

      <!-- Right Address (Arabic) -->
      <div class="arabic-address address">
        <h3>{{ invoice.templateStaticData.mainText1 }}</h3>
        <p class="company-info">{{ invoice.templateStaticData.mainText2 }}</p>
        <p class="company-info">{{ invoice.templateStaticData.mainText3 }}</p>
        <p class="contact-detail">{{ invoice.templateStaticData.contactLine1 }}</p>
        <p class="contact-detail">{{ invoice.templateStaticData.contactLine2 }}</p>
        <p class="contact-detail">{{ invoice.templateStaticData.contactLine3 }}</p>
      </div>
    </div>

    <div class="invoice-title">
      <table class="title-table">
        <tr>
          <td class="title-cell" colspan="2">
            <span class="title-text">{{ getInvoiceTitle() }}</span>
          </td>
        </tr>
      </table>
    </div>

    <div class="client-details-qrcode-row">
      <table class="client-details-table">
        <!-- Invoice Details Section -->
        <tr>
          <td class="label">Doc No:</td>
          <td class="value">
            {{ invoice.documentNumber }} ({{ invoice.createdAt | date : 'dd/MM/yyyy HH:mm:ss' }})
          </td>
          <td class="arabic">رقم الفاتورة</td>
        </tr>
        <tr>
          <td class="label">Ref No:</td>
          <td class="value" *ngIf="isOrderNumber()">
            {{ invoice?.orderNumber || '-' }} ({{
              invoice.orderDate | date : 'dd/MM/yyyy HH:mm:ss'
            }})
          </td>
          <td class="value" *ngIf="isReferenceNumber()">
            {{ invoice?.referenceDocumentNumber || '-' }} ({{
              invoice.referenceDocumentDate | date : 'dd/MM/yyyy HH:mm:ss'
            }})
          </td>
          <td class="arabic">رقم المرجع</td>
        </tr>
        <tr>
          <td class="label">Payment Type:</td>
          <td class="value">{{ getPaymentTypeText(invoice.payments) }}</td>
          <td class="arabic">طريقة الدفع</td>
        </tr>
        <tr>
          <td class="label">Distributor:</td>
          <td class="value">{{ getFormattedDistributorName() }}</td>
          <td class="arabic">الموزع</td>
        </tr>

        <!-- Client Details Section -->
        <tr class="info-header">
          <td colspan="3"><div>Client Information بيانات العميل</div></td>
        </tr>
        <tr>
          <td class="label">Client Name</td>
          <td class="value">
            <span class="client-name-arabic">{{ invoice.customer?.nameArabic || '-' }}</span>
            <span class="client-name-english">{{ invoice.customer?.nameEnglish || '-' }}</span>
          </td>
          <td class="arabic">اسم العميل</td>
        </tr>
        <tr>
          <td class="label">Account No</td>
          <td class="value">{{ invoice.customer?.accountNumber || '-' }}</td>
          <td class="arabic">رقم العميل</td>
        </tr>
        <tr>
          <td class="label">Address</td>
          <td class="value">{{ getFormattedAddress() }}</td>
          <td class="arabic">عنوان العميل</td>
        </tr>
        <tr>
          <td class="label">Phone / Email</td>
          <td class="value">{{ getFormattedPhoneEmail() }}</td>
          <td class="arabic">رقم الهاتف والايميل</td>
        </tr>
        <tr>
          <td class="label">VAT No. / Identification</td>
          <td class="value">{{ getFormattedVatIdentification() }}</td>
          <td class="arabic">الرقم الضريبي /الهوية</td>
        </tr>
      </table>
      <div class="qr-code-section" *ngIf="invoice?.qrInvoice && invoice?.qrInvoice !== null">
        <qrcode
          [elementType]="'img'"
          [errorCorrectionLevel]="'M'"
          [qrdata]="invoice.qrInvoice"
          [colorDark]="'#000000ff'"
          [width]="190"
          [height]="190">
        </qrcode>
      </div>
    </div>
  </div>

  <div class="invoice-items">
    <table class="items-table">
      <thead>
        <tr>
          <th>
            <div class="header-bilingual">
              <span class="arabic">رقم الصنف</span>
              <span class="en">Item Code</span>
            </div>
          </th>
          <th>
            <div class="header-bilingual">
              <span class="arabic">البيان</span>
              <span class="en">Description</span>
            </div>
          </th>
          <th>
            <div class="header-bilingual">
              <span class="arabic">الوحدة</span>
              <span class="en">Unit</span>
            </div>
          </th>
          <th>
            <div class="header-bilingual">
              <span class="arabic">الكمية</span>
              <span class="en">Quantity</span>
            </div>
          </th>
          <th class="sar-header">
            <div class="header-bilingual">
              <span class="arabic">السعر</span>
              <span class="en">Price</span>
            </div>
            <img class="sar-symbol" src="assets/images/sar.svg" alt="SAR" />
          </th>
          <th class="sar-header">
            <div class="header-bilingual">
              <span class="arabic">المجموع قبل الضريبة</span>
              <span class="en">Total Excl VAT</span>
            </div>
            <img class="sar-symbol" src="assets/images/sar.svg" alt="SAR" />
          </th>
          <th>
            <div class="header-bilingual">
              <span class="arabic">نسبة الضريبة</span>
              <span class="en">% VAT</span>
            </div>
          </th>
          <th class="sar-header">
            <div class="header-bilingual">
              <span class="arabic">قيمة الضريبة</span>
              <span class="en">VAT Amt</span>
            </div>
            <img class="sar-symbol" src="assets/images/sar.svg" alt="SAR" />
          </th>
          <th class="sar-header">
            <div class="header-bilingual">
              <span class="arabic">المجموع بعد الضريبة</span>
              <span class="en">Total Incl VAT</span>
            </div>
            <img class="sar-symbol" src="assets/images/sar.svg" alt="SAR" />
          </th>
        </tr>
      </thead>
      <tbody>
        <ng-container *ngIf="invoice.items && invoice.items.length > 0">
          <tr *ngFor="let item of invoice.items">
            <td class="left-align">{{ item.itemCode }}</td>
            <td>
              <span class="desc-arabic" *ngIf="item.product?.nameArabic">{{
                item.product?.nameArabic
              }}</span>
              <span class="desc-english" *ngIf="item.product?.nameEnglish">{{
                item.product?.nameEnglish
              }}</span>
            </td>
            <td>
              <span class="unit-arabic" *ngIf="item.product?.unitName">{{
                item.product?.unitName
              }}</span>
              <span class="unit-english" *ngIf="item.unitName">{{ item.unitName }}</span>
            </td>
            <td class="right-align">{{ item.quantity || '0.0' }}</td>
            <td class="right-align">{{ item.price || '0.00' }}</td>
            <td class="right-align">{{ item.subtotal - item.subTotalVat || '0.00' }}</td>
            <td class="right-align">{{ item.vat || '0.00' }}</td>
            <td class="right-align">{{ item.subTotalVat || '0.00' }}</td>
            <td class="right-align">{{ item.subtotal || '0.00' }}</td>
          </tr>
        </ng-container>
      </tbody>
    </table>
  </div>

  <div class="invoice-footer">
    <table class="footer-table">
      <tr>
        <td class="notes-section">
          <div class="invoice-notes">
            <pre class="notes-pre">{{ invoice?.notes }}</pre>
          </div>
        </td>
        <td class="totals-section">
          <table class="totals-table">
            <tr>
              <td class="label">Total Excluding VAT</td>
              <td class="value">
                <img class="sar-symbol" src="assets/images/sar.svg" alt="SAR" />
                {{ invoice.payments.totalExclVatDiscount }}
              </td>
              <td class="arabic">الإجمالي غير شامل ضريبة القيمة المضافة</td>
            </tr>
            <tr>
              <td class="label">Total Discount</td>
              <td class="value">
                <img class="sar-symbol" src="assets/images/sar.svg" alt="SAR" />
                {{ invoice.payments.totalDiscount }}
              </td>
              <td class="arabic">الخصم</td>
            </tr>
            <tr>
              <td class="label">Total VAT</td>
              <td class="value">
                <img class="sar-symbol" src="assets/images/sar.svg" alt="SAR" />
                {{ invoice.payments.totalVat }}
              </td>
              <td class="arabic">إجمالي ضريبة القيمة المضافة</td>
            </tr>
            <tr>
              <td class="label">Fractional deduction</td>
              <td class="value">
                <img class="sar-symbol" src="assets/images/sar.svg" alt="SAR" />
                {{ invoice.payments.fractionAmount || '0.00' }}
              </td>
              <td class="arabic">خصم الكسور</td>
            </tr>
            <tr class="total-row">
              <td class="label grand-total">Grand Total</td>
              <td class="value grand-total">
                <img class="sar-symbol" src="assets/images/sar.svg" alt="SAR" /> {{ grantTotal() }}
              </td>
              <td class="arabic grand-total">اجمالي المبلغ المستحق شامل قيمة الضريبة المضافة</td>
            </tr>
          </table>
          <div class="total-in-words">
            <div class="arabic-words">{{ arabicTotalInWords }}</div>
            <div class="english-words">
              {{ totalInWords }}
            </div>
          </div>
        </td>
      </tr>
    </table>
  </div>

  <div class="invoice-footer-info">
    <table class="footer-info-table">
      <tr>
        <td class="signature-section" colspan="3">
          <table class="signatures-table">
            <tr>
              <td class="qr-code-cell">
                <div class="footer-qr-container">
                  <!-- Footer QR Code using angularx-qrcode -->
                  <qrcode
                    *ngIf="invoice?.templateStaticData?.qrText"
                    [allowEmptyString]="false"
                    [elementType]="'img'"
                    [errorCorrectionLevel]="'L'"
                    [scale]="1"
                    [width]="75"
                    [qrdata]="invoice.templateStaticData.qrText">
                  </qrcode>
                  <!-- Fallback image if no QR data -->
                  <img
                    class="footer-qr-image"
                    *ngIf="!invoice?.templateStaticData?.qrText"
                    src="assets/images/sawami-qr.png"
                    alt="Footer QR Code"
                    style="width: 75px; height: 75px" />
                </div>
              </td>
              <td class="prepared-by">
                <div class="signature-header">
                  <div class="signature-label">Prepared By:</div>
                  <div class="signature-label-ar">بواسطة</div>
                </div>
                <div class="signature-value">{{ invoice?.issuedBy || '-' }}</div>
              </td>
              <td class="client-rep">
                <div class="signature-header">
                  <div class="signature-label">Client Signature:</div>
                  <div class="signature-label-ar">توقيع العميل</div>
                </div>
                <div class="signature-line"></div>
              </td>
            </tr>
          </table>
        </td>
      </tr>
    </table>
  </div>
</div>
