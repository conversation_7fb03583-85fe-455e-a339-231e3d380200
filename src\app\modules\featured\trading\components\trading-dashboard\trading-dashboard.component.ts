import { Component, OnInit } from '@angular/core';
import { DashboardModulesHolder } from 'src/app/modules/shared/components/dashboard-modules-holder/modulesHolder';
import { navItems } from 'src/app/layouts/full/app-navigation-common-menus';

@Component({
  selector: 'app-trading-dashboard',
  templateUrl: './trading-dashboard.component.html',
  styleUrls: ['./trading-dashboard.component.scss'],
})
export class TradingDashboardComponent implements OnInit {
  productModulesList: DashboardModulesHolder[] = [];

  constructor() {
    // Constructor can be used for dependency injection if needed
  }

  ngOnInit(): void {
    const tradingMenu = navItems.find(item => item.route === 'trading');
    if (tradingMenu && tradingMenu.children) {
      this.productModulesList = tradingMenu.children.map(child => ({
        moduleName: child.displayName,
        moduleDescription: `Manage ${child.displayName}.`,
        modulePermission: child.permissions,
        moduleImage: 'storefront', // Default image, can be customized
        moduleRouterLink: `../${child.route.split('/').pop()}`,
        moduleButtonAction: 'BackOffice',
        moduleType: 'subModule',
      }));
    }
  }
}
