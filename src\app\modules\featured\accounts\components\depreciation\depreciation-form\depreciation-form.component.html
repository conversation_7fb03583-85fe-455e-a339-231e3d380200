<ng-container>
  <form [formGroup]="depreciationCreationForm" autocomplete="off">
    <mat-card appearance="outlined">
      <mat-card-title>{{ formTitle | translate }}</mat-card-title>
      <div class="row no-gutters">
        <div class="p-2 col-lg-3 col-md-3 col-sm-4">
          <app-account-auto-search
            [control]="depreciationCreationForm.get('assetAccount')"
            [disabled]="isViewMode"
            [label]="'depreciation.assetAccountId' | translate"
            accountCategory="ASSETS"
            isDepreciating="true"
            accountType="DETAILED"
            searchStringLength="1"
            placeholder="{{ getAccountName(depreciationCreationForm.value.assetAccount) }}">
          </app-account-auto-search>
        </div>
        <div class="p-2 col-lg-3 col-md-3 col-sm-4">
          <app-account-auto-search
            [control]="depreciationCreationForm.get('cumulativeDepreciationAccount')"
            [disabled]="isViewMode"
            [label]="'depreciation.cumDeprAccount' | translate"
            accountCategory="ASSETS"
            accountType="DETAILED"
            isContraAccount="true"
            searchStringLength="1"
            placeholder="{{
              getAccountName(depreciationCreationForm.value.cumulativeDepreciationAccount)
            }}">
          </app-account-auto-search>
        </div>

        <div class="p-2 col-lg-3 col-md-3 col-sm-4">
          <app-account-auto-search
            [control]="depreciationCreationForm.get('depreciationExpenseAccount')"
            [disabled]="isViewMode"
            [label]="'depreciation.deprExpAccount' | translate"
            accountCategory="EXPENSES"
            accountType="DETAILED"
            searchStringLength="1"
            placeholder="{{
              getAccountName(depreciationCreationForm.value.depreciationExpenseAccount)
            }}">
          </app-account-auto-search>
        </div>
        <div class="p-2 col-lg-3 col-md-3 col-sm-4">
          <mat-label>{{ 'depreciation.deprPercentage' | translate }}</mat-label>

          <mat-form-field class="w-100">
            <input type="number" matInput formControlName="depreciationPct" />
          </mat-form-field>
        </div>
        <div class="p-2 col-lg-3 col-md-3 col-sm-4">
          <mat-label>{{ 'depreciation.purchaseDate' | translate }}</mat-label>

          <mat-form-field class="w-100">
            <input [matDatepicker]="purchaseDate" matInput formControlName="purchaseDate" />
            <mat-datepicker-toggle [for]="purchaseDate" matSuffix> </mat-datepicker-toggle>
            <mat-datepicker #purchaseDate></mat-datepicker>
          </mat-form-field>
        </div>
        <div class="p-2 col-lg-3 col-md-3 col-sm-4">
          <mat-label>{{ 'depreciation.supplierName' | translate }}</mat-label>

          <mat-form-field class="w-100">
            <input type="text" matInput formControlName="supplierName" />
          </mat-form-field>
        </div>
        <div class="p-2 col-lg-3 col-md-3 col-sm-4">
          <mat-label>{{ 'depreciation.purchaseAmount' | translate }}</mat-label>

          <mat-form-field class="w-100">
            <input type="number" matInput formControlName="purchaseAmount" />
          </mat-form-field>
        </div>
        <div class="p-2 col-lg-3 col-md-3 col-sm-4">
          <mat-label>{{ 'depreciation.salvageValue' | translate }}</mat-label>

          <mat-form-field class="w-100">
            <input type="number" matInput formControlName="salvationValue" />
          </mat-form-field>
        </div>
        <div class="p-2 col-lg-3 col-md-3 col-sm-4">
          <mat-label>{{ 'depreciation.assetLocation' | translate }}</mat-label>

          <mat-form-field class="w-100">
            <input type="text" matInput formControlName="assetLocation" />
          </mat-form-field>
        </div>
        <div class="p-2 col-lg-3 col-md-3 col-sm-4">
          <mat-label>{{ 'depreciation.notes' | translate }}</mat-label>

          <mat-form-field class="w-100">
            <input type="text" matInput formControlName="notes" />
          </mat-form-field>
        </div>
        <div class="p-2 col-lg-3 col-md-3 col-sm-4">
          <mat-label>{{ 'depreciation.deprecationMethod' | translate }}</mat-label>

          <mat-form-field class="w-100">
            <mat-select formControlName="depreciationMethod">
              <mat-option *ngFor="let method of depreciationMethods" [value]="method.value">
                {{ method.display | translate }}
              </mat-option>
            </mat-select>
          </mat-form-field>
        </div>
        <div class="p-2 col-lg-3 col-md-3 col-sm-4">
          <mat-label>{{ 'depreciation.lastDepreciationDate' | translate }}</mat-label>

          <mat-form-field class="w-100">
            <input
              [matDatepicker]="lastDepreciationDate"
              matInput
              formControlName="lastDepreciationDate" />
            <mat-datepicker-toggle [for]="lastDepreciationDate" matSuffix> </mat-datepicker-toggle>
            <mat-datepicker #lastDepreciationDate></mat-datepicker>
          </mat-form-field>
        </div>
        <div class="p-2 col-lg-3 col-md-3 col-sm-4">
          <mat-label>{{ 'depreciation.cumDepreciation' | translate }}</mat-label>

          <mat-form-field class="w-100">
            <input type="number" matInput formControlName="depreciatedValue" />
          </mat-form-field>
        </div>
        <div class="p-2 col-lg-3 col-md-3 col-sm-4">
          <mat-label>{{ 'depreciation.costAccountNo' | translate }}</mat-label>

          <mat-form-field class="w-100">
            <input type="text" matInput formControlName="costAccountNumber" />
          </mat-form-field>
        </div>
      </div>
    </mat-card>
  </form>
  <div class="text-center">
    <button
      class="m-l-10"
      *ngIf="isCreateMode"
      (click)="onSubmit($event)"
      mat-stroked-button
      color="primary">
      {{ 'common.submit' | translate }}
    </button>
    <button
      class="m-l-10"
      *ngIf="isEditMode"
      (click)="onSubmit($event)"
      mat-stroked-button
      color="primary">
      {{ 'common.submit' | translate }}
    </button>
    <button
      class="m-l-10"
      *ngIf="isCreateMode"
      [routerLink]="['../']"
      mat-stroked-button
      color="warn">
      {{ 'common.cancel' | translate }}
    </button>
    <button
      class="m-l-10"
      *ngIf="isEditMode"
      [routerLink]="['../../']"
      mat-stroked-button
      color="warn">
      {{ 'common.cancel' | translate }}
    </button>
    <button
      class="m-l-10"
      *ngIf="isViewMode"
      [routerLink]="['../../']"
      type="button"
      mat-stroked-button
      color="primary">
      Back
    </button>
  </div>
</ng-container>
