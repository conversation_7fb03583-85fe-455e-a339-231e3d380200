import { Component, Inject, OnInit, Optional, SkipSelf } from '@angular/core';
import { UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import {
  accountCategorys,
  accountTypes,
  accountSubCategorys,
  financialStatementType,
} from 'src/app/core/configs/dropDownConfig';
import { AuthService } from 'src/app/modules/core/core/services/auth.service';
import { Account } from '../../../models/account';
import { CostCentre } from '../../../models/costCentre';
import { ChartOfAccountsService } from '../../../services/chart-of-accounts.service';
import { CostCentresService } from '../../../services/cost-centres.service';
import { AccountAutoSearchComponent } from '../../accountAutoSearch/account-auto-search.component';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { ICustomerModalData } from 'src/app/core/interfaces/customer';
import { CustomerFormComponent } from 'src/app/modules/featured/trading/components/customers/customer-form/customer-form.component';
import { CookieService } from 'ngx-cookie-service';
import { CommonService } from 'src/app/core/api/common.service';

@Component({
  selector: 'app-chart-of-accounts-form',
  templateUrl: './chart-of-accounts-form.component.html',
  styleUrls: ['./chart-of-accounts-form.component.scss'],
  providers: [
    AccountAutoSearchComponent, // added class in the providers
  ],
})
export class ChartOfAccountsFormComponent implements OnInit {
  chartOfAccountsForm: UntypedFormGroup;
  loading: boolean;
  formTitle: string;
  accountId: string;
  account: Account;
  isArabic: boolean;
  nameArabic: any;
  nameEnglish: any;

  accountCategorys = accountCategorys;

  accountSubCategorys = accountSubCategorys;

  financialStatementTypes = financialStatementType;

  accountTypes = accountTypes;

  parentAccounts: Account[] = [];

  isEditMode = false;
  isViewMode = false;
  isCreateMode = false;

  costCentreList: CostCentre[];

  addOptionalName: boolean;

  constructor(
    @Optional() @SkipSelf() @Inject(MAT_DIALOG_DATA) public modalData: any,
    @Optional() @SkipSelf() public dialogRef: MatDialogRef<ChartOfAccountsFormComponent>,
    private authService: AuthService,
    private toastr: ToastrService,
    private route: ActivatedRoute,
    private router: Router,
    private chartOfAccountsService: ChartOfAccountsService,
    private formBuilder: UntypedFormBuilder,
    private accountSearchComponent: AccountAutoSearchComponent,
    private costCentreService: CostCentresService,
    private cookieService: CookieService,
    private commonService: CommonService
  ) {}

  ngOnInit(): void {
    if (this.cookieService.get('locale').toUpperCase() === 'EN') {
      this.isArabic = false;
    } else {
      this.isArabic = true;
    }
    this.addOptionalName = false;
    this.loading = true;
    this.getAllCostCentre();
    this.formTitle = this.route.snapshot.data['title'];
    this.route.params.subscribe(params => {
      const id = params['id'];
      if (id) {
        if (this.router.url.includes('edit')) {
          this.isEditMode = true;
        } else if (this.router.url.includes('view')) {
          this.isViewMode = true;
        }
        this.accountId = id;
        this.getAllChartOfAccountData();
      } else {
        this.accountId = null;
        this.isCreateMode = true;
        this.isEditMode = false;
        this.isViewMode = false;
        this.initializeForm(null);
      }
    });
    if (this.modalData) {
      this.accountId = this.modalData.accountId;
      this.isCreateMode = true;
      this.getAllChartOfAccountData();
    }
  }

  getAllChartOfAccountData(): void {
    this.chartOfAccountsService
      .getChartOfAccountById(this.accountId)
      .subscribe((response: Account) => {
        this.account = response;
        this.initializeForm(this.account);
      });
  }

  initializeForm(data: Account): void {
    if (this.isArabic) {
      this.nameArabic = ['', Validators.compose([Validators.required])];
      this.nameEnglish = [''];
    } else {
      this.nameArabic = [''];
      this.nameEnglish = ['', Validators.compose([Validators.required])];
    }
    this.chartOfAccountsForm = this.formBuilder.group({
      accountCategory: ['ASSETS', Validators.compose([Validators.required])],
      accountSubCategory: ['GENERAL', Validators.compose([Validators.required])],
      financialStatementType: ['BUDGET'],
      nameArabic: this.nameArabic,
      nameEnglish: this.nameEnglish,
      parentAccountId: [null],
      accountType: ['GENERAL', Validators.compose([Validators.required])],
      isDepreciating: [false, Validators.compose([Validators.required])],
      isHidden: [false, Validators.compose([Validators.required])],
      isFreezed: [false, Validators.compose([Validators.required])],
      isContraAccount: [false, Validators.compose([Validators.required])],
      costCentreId: [null],
      note: [''],
    });

    if (data) {
      this.chartOfAccountsForm.patchValue({
        accountCategory: data.accountCategory,
        accountSubCategory: data.accountSubCategory,
        financialStatementType: data.financialStatementType,
        nameArabic: data.nameArabic,
        nameEnglish: data.nameEnglish,
        parentAccountId: data.parentAccountId,
        accountType: data.accountType,
        isDepreciating: data.isDepreciating,
        isHidden: data.isHidden,
        isContraAccount: data.isContraAccount,
        costAccountNumber: data.costAccountNumber,
        costCentreId: data.costCentreId,
        note: data.note,
      });
    }

    if (this.isViewMode) {
      this.chartOfAccountsForm.disable();
    }

    this.getAllDropDownData(
      this.chartOfAccountsForm.get('accountCategory').value,
      this.chartOfAccountsForm.get('accountSubCategory').value
    );

    this.chartOfAccountsForm.controls['accountCategory'].valueChanges.subscribe(value => {
      this.chartOfAccountsForm.patchValue({
        parentAccountId: null,
      });
      if (value) {
        this.getAllDropDownData(value, this.chartOfAccountsForm.get('accountSubCategory').value);
      }
    });

    this.chartOfAccountsForm.controls['accountSubCategory'].valueChanges.subscribe(value => {
      this.chartOfAccountsForm.patchValue({
        parentAccountId: null,
      });
      if (value) {
        this.getAllDropDownData(this.chartOfAccountsForm.get('accountCategory').value, value);
      }
    });

    if (this.isCreateMode) {
      const creatingFromParentId = this.route.snapshot.queryParamMap.get('parentAccountId');
      if (creatingFromParentId) {
        this.chartOfAccountsService
          .getChartOfAccountById(creatingFromParentId.toString())
          .subscribe((response: Account) => {
            this.chartOfAccountsForm.patchValue({
              accountCategory: response.accountCategory,
              accountSubCategory: response.accountSubCategory,
            });
            this.parentAccounts.push(response);
            this.chartOfAccountsForm.patchValue({
              parentAccountId: response.accountId,
            });
          });
      }
    }
  }

  getAllDropDownData(accountCategory: string, accountSubCategory: string): void {
    this.parentAccounts = [];
    this.chartOfAccountsService
      .getChartOfAccountsByAccountCategoryAndAccountSubCategory(accountCategory, accountSubCategory)
      .subscribe((response: Account[]) => {
        this.parentAccounts = response;
        this.loading = false;
      });
    if (this.isViewMode && this.account.parentAccountId) {
      this.chartOfAccountsService
        .getChartOfAccountById(this.account.parentAccountId.toString())
        .subscribe((response: Account) => {
          this.parentAccounts.push(response);
        });
    }
  }

  accountTypeIsDetailed(): boolean {
    return this.chartOfAccountsForm.get('accountType').value == 'DETAILED';
  }

  onSubmit(event: Event) {
    event.preventDefault();
    this.chartOfAccountsForm.markAllAsTouched();
    if (this.chartOfAccountsForm && this.chartOfAccountsForm?.valid) {
      if (!this.accountTypeIsDetailed()) {
        this.chartOfAccountsForm.patchValue({
          costAccountNumber: null,
        });
      } else {
        const selectedParentAccountId = this.chartOfAccountsForm.get('parentAccountId').value;
        for (const parentAccount of this.parentAccounts) {
          if (selectedParentAccountId == parentAccount.accountId) {
            this.chartOfAccountsForm.patchValue({
              financialStatementType: parentAccount.financialStatementType,
            });
            break;
          }
        }
      }
      if (this.isCreateMode) {
        this.chartOfAccountsService
          .createChartOfAccounts(this.chartOfAccountsForm.value)
          .subscribe(() => {
            //this.toastr.success('Chart Of Accounts Added Successfully');
            this.commonService.playSuccessSound();
            this.router.navigate(['../'], { relativeTo: this.route });
          });
      } else if (this.isEditMode) {
        this.chartOfAccountsService
          .updateChartOfAccounts(this.accountId, this.chartOfAccountsForm.value)
          .subscribe(() => {
            //this.toastr.success('Chart Of Accounts Updated Successfully');
            this.commonService.playSuccessSound();
            this.router.navigate(['../../'], { relativeTo: this.route });
          });
      }
    } else {
      this.commonService.scrollToError();
    }
  }
  getAllCostCentre(): void {
    this.costCentreService.getAllCostCentres().subscribe((response: CostCentre[]) => {
      this.costCentreList = response;
    });
  }

  provideOptionalName() {
    if (this.chartOfAccountsForm.value.nameEnglish === '') {
      this.chartOfAccountsForm.patchValue({
        nameEnglish: this.chartOfAccountsForm.value.nameArabic,
      });
    }

    this.addOptionalName = !this.addOptionalName;
  }
}
