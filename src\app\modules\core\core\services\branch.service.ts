import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { BehaviorSubject, Observable, of } from 'rxjs';
import { catchError, tap } from 'rxjs/operators';
import { ILoginBranches } from '../models/login';
import { environment } from 'src/environments/environment';
import { LocalStorageService } from './local-storage.service';

@Injectable({
  providedIn: 'root',
})
export class BranchService {
  private baseUrl = environment.apiUrl;
  private branchesSubject = new BehaviorSubject<ILoginBranches[]>([]);
  public branches$ = this.branchesSubject.asObservable();

  constructor(private http: HttpClient, private localStorage: LocalStorageService) {
    // Try to load from storage on initialization
    this.loadFromStorage();
  }

  loadBranches(companyId: string): Observable<ILoginBranches[]> {
    return this.http
      .get<ILoginBranches[]>(`${this.baseUrl}auth/users/branches-years?companyId=${companyId}`)
      .pipe(
        tap(branches => {
          this.branchesSubject.next(branches);
          // Store in localStorage for persistence
          this.localStorage.setItem('userBranches', JSON.stringify(branches));
        }),
        catchError(error => {
          console.error('Error loading branches:', error);
          return of([]);
        })
      );
  }

  switchBranch(
    branchId: string,
    yearId: string,
    branchName: string,
    year: string
  ): Observable<any> {
    // Update storage
    this.localStorage.setItem('currentBranchId', branchId);
    this.localStorage.setItem('currentBranch', branchName);
    this.localStorage.setItem('yearId', yearId);
    this.localStorage.setItem('year', year);

    // Create cookie request
    const cookieRequest = [
      { name: 'branchId', value: branchId },
      { name: 'yearId', value: yearId },
    ];

    // Send to server to update cookies
    return this.http.post(`${this.baseUrl}auth/setCookies`, cookieRequest, {
      withCredentials: true,
    });
  }

  private loadFromStorage(): void {
    const branchesStr = this.localStorage.getItem('userBranches');
    if (branchesStr) {
      try {
        const branches = JSON.parse(branchesStr);
        this.branchesSubject.next(branches);
      } catch (e) {
        console.error('Error parsing branches from storage');
      }
    }
  }

  getCurrentBranchAndYear(): { branch: string; year: string } {
    return {
      branch: this.localStorage.getItem('currentBranch') || '',
      year: this.localStorage.getItem('year') || '',
    };
  }

  getUserBranchesYearId(companyId: string) {
    return this.http.get<ILoginBranches[]>(
      this.baseUrl + `auth/users/branches-years?companyId=${companyId}`
    );
  }
}
