import { ArrayDataSource } from '@angular/cdk/collections';
import { NestedTreeControl } from '@angular/cdk/tree';
import { ChangeDetectorRef, Component } from '@angular/core';
import { MatTreeNestedDataSource } from '@angular/material/tree';

interface FoodNode {
  accountId: number;
  parentAccountId: number;
  nameEnglish: string;
  nameArabic: string;
  accountType: string;
  children?: FoodNode[];
  color: string;
}
@Component({
  selector: 'app-chart-of-accounts-tree',
  templateUrl: './chart-of-accounts-tree.component.html',
  styleUrls: ['./chart-of-accounts-tree.component.scss'],
})
export class ChartOfAccountsTreeComponent {
  showDetails = false;
  // Generating the hierarchical structure
  hierarchicalStructure = this.generateHierarchy([
    {
      accountId: 1,
      branchId: 1,
      accountNumber: 1,
      nameArabic: 'Asset Accounts',
      nameEnglish: 'Asset Accounts',
      parentAccountId: 0,
      accountLevel: 1,
      accountType: 'GENERAL',
      accountCategory: 'ASSETS',
      accountNature: 'DEBIT',
      financialStatementType: 'BUDGET',
      accountSubCategory: 'GENERAL',
      isDepreciating: false,
      isHidden: false,
      isFreezed: false,
      isContraAccount: false,
      costCentreId: 0,
      note: null,
    },
    {
      accountId: 6,
      branchId: 1,
      accountNumber: 11,
      nameArabic: 'currentassets',
      nameEnglish: 'currentassets',
      parentAccountId: 1,
      accountLevel: 2,
      accountType: 'GENERAL',
      accountCategory: 'ASSETS',
      accountNature: 'DEBIT',
      financialStatementType: 'BUDGET',
      accountSubCategory: 'GENERAL',
      isDepreciating: false,
      isHidden: false,
      isFreezed: false,
      isContraAccount: false,
      costCentreId: 0,
      note: '',
    },
    {
      accountId: 8,
      branchId: 1,
      accountNumber: 111,
      nameArabic: 'cashaccounts',
      nameEnglish: 'cashaccounts',
      parentAccountId: 6,
      accountLevel: 3,
      accountType: 'GENERAL',
      accountCategory: 'ASSETS',
      accountNature: 'DEBIT',
      financialStatementType: 'BUDGET',
      accountSubCategory: 'CASHIER',
      isDepreciating: false,
      isHidden: false,
      isFreezed: false,
      isContraAccount: false,
      costCentreId: 0,
      note: '',
    },
    {
      accountId: 9,
      branchId: 1,
      accountNumber: ***********,
      nameArabic: 'cashaccount-1',
      nameEnglish: 'cashaccount-1',
      parentAccountId: 8,
      accountLevel: 4,
      accountType: 'DETAILED',
      accountCategory: 'ASSETS',
      accountNature: 'DEBIT',
      financialStatementType: 'BUDGET',
      accountSubCategory: 'CASHIER',
      isDepreciating: false,
      isHidden: false,
      isFreezed: false,
      isContraAccount: false,
      costCentreId: 0,
      note: '',
    },
    {
      accountId: 10,
      branchId: 1,
      accountNumber: 112,
      nameArabic: 'bankaccounts',
      nameEnglish: 'bankaccounts',
      parentAccountId: 6,
      accountLevel: 3,
      accountType: 'GENERAL',
      accountCategory: 'ASSETS',
      accountNature: 'DEBIT',
      financialStatementType: 'BUDGET',
      accountSubCategory: 'BANK',
      isDepreciating: false,
      isHidden: false,
      isFreezed: false,
      isContraAccount: false,
      costCentreId: 0,
      note: '',
    },
    {
      accountId: 11,
      branchId: 1,
      accountNumber: ***********,
      nameArabic: 'bankaccount-1',
      nameEnglish: 'bankaccount-1',
      parentAccountId: 10,
      accountLevel: 4,
      accountType: 'DETAILED',
      accountCategory: 'ASSETS',
      accountNature: 'DEBIT',
      financialStatementType: 'BUDGET',
      accountSubCategory: 'BANK',
      isDepreciating: false,
      isHidden: false,
      isFreezed: false,
      isContraAccount: false,
      costCentreId: 0,
      note: '',
    },
    {
      accountId: 12,
      branchId: 1,
      accountNumber: 113,
      nameArabic: 'customeraccounts',
      nameEnglish: 'customeraccounts',
      parentAccountId: 6,
      accountLevel: 3,
      accountType: 'GENERAL',
      accountCategory: 'ASSETS',
      accountNature: 'DEBIT',
      financialStatementType: 'BUDGET',
      accountSubCategory: 'CUSTOMER',
      isDepreciating: false,
      isHidden: false,
      isFreezed: false,
      isContraAccount: false,
      costCentreId: 0,
      note: '',
    },
    {
      accountId: 13,
      branchId: 1,
      accountNumber: ***********,
      nameArabic: 'customer-1',
      nameEnglish: 'customer-1',
      parentAccountId: 12,
      accountLevel: 4,
      accountType: 'DETAILED',
      accountCategory: 'ASSETS',
      accountNature: 'DEBIT',
      financialStatementType: 'BUDGET',
      accountSubCategory: 'CUSTOMER',
      isDepreciating: false,
      isHidden: false,
      isFreezed: false,
      isContraAccount: false,
      costCentreId: 0,
      note: '',
    },
    {
      accountId: 22,
      branchId: 1,
      accountNumber: 114,
      nameArabic: 'distributoraccounts',
      nameEnglish: 'distributoraccounts',
      parentAccountId: 6,
      accountLevel: 3,
      accountType: 'GENERAL',
      accountCategory: 'ASSETS',
      accountNature: 'DEBIT',
      financialStatementType: 'BUDGET',
      accountSubCategory: 'DISTRIBUTOR',
      isDepreciating: false,
      isHidden: false,
      isFreezed: false,
      isContraAccount: false,
      costCentreId: 0,
      note: '',
    },
    {
      accountId: 23,
      branchId: 1,
      accountNumber: ***********,
      nameArabic: 'dist1',
      nameEnglish: 'dist1',
      parentAccountId: 22,
      accountLevel: 4,
      accountType: 'DETAILED',
      accountCategory: 'ASSETS',
      accountNature: 'DEBIT',
      financialStatementType: null,
      accountSubCategory: 'DISTRIBUTOR',
      isDepreciating: false,
      isHidden: false,
      isFreezed: false,
      isContraAccount: false,
      costCentreId: 0,
      note: null,
    },
    {
      accountId: 7,
      branchId: 1,
      accountNumber: 12,
      nameArabic: 'fixedassets',
      nameEnglish: 'fixedassets',
      parentAccountId: 1,
      accountLevel: 2,
      accountType: 'GENERAL',
      accountCategory: 'ASSETS',
      accountNature: 'DEBIT',
      financialStatementType: 'BUDGET',
      accountSubCategory: 'GENERAL',
      isDepreciating: false,
      isHidden: false,
      isFreezed: false,
      isContraAccount: false,
      costCentreId: 0,
      note: '',
    },
    {
      accountId: 2,
      branchId: 1,
      accountNumber: 2,
      nameArabic: 'Liability Accounts',
      nameEnglish: 'Liability Accounts',
      parentAccountId: 0,
      accountLevel: 1,
      accountType: 'GENERAL',
      accountCategory: 'LIABILITIES',
      accountNature: 'CREDIT',
      financialStatementType: 'BUDGET',
      accountSubCategory: 'GENERAL',
      isDepreciating: false,
      isHidden: false,
      isFreezed: false,
      isContraAccount: false,
      costCentreId: 0,
      note: null,
    },
    {
      accountId: 14,
      branchId: 1,
      accountNumber: 21,
      nameArabic: 'vataccounts',
      nameEnglish: 'vataccounts',
      parentAccountId: 2,
      accountLevel: 2,
      accountType: 'GENERAL',
      accountCategory: 'LIABILITIES',
      accountNature: 'DEBIT',
      financialStatementType: 'BUDGET',
      accountSubCategory: 'GENERAL',
      isDepreciating: false,
      isHidden: false,
      isFreezed: false,
      isContraAccount: false,
      costCentreId: 0,
      note: '',
    },
    {
      accountId: 15,
      branchId: 1,
      accountNumber: ***********,
      nameArabic: 'vataccount-1',
      nameEnglish: 'vataccount-1',
      parentAccountId: 14,
      accountLevel: 3,
      accountType: 'DETAILED',
      accountCategory: 'LIABILITIES',
      accountNature: 'DEBIT',
      financialStatementType: 'BUDGET',
      accountSubCategory: 'GENERAL',
      isDepreciating: false,
      isHidden: false,
      isFreezed: false,
      isContraAccount: false,
      costCentreId: 0,
      note: '',
    },
    {
      accountId: 3,
      branchId: 1,
      accountNumber: 3,
      nameArabic: 'Capital Accounts',
      nameEnglish: 'Capital Accounts',
      parentAccountId: 0,
      accountLevel: 1,
      accountType: 'GENERAL',
      accountCategory: 'EQUITY',
      accountNature: 'CREDIT',
      financialStatementType: 'BUDGET',
      accountSubCategory: 'GENERAL',
      isDepreciating: false,
      isHidden: false,
      isFreezed: false,
      isContraAccount: false,
      costCentreId: 0,
      note: null,
    },
    {
      accountId: 4,
      branchId: 1,
      accountNumber: 4,
      nameArabic: 'Revenue Accounts',
      nameEnglish: 'Revenue Accounts',
      parentAccountId: 0,
      accountLevel: 1,
      accountType: 'GENERAL',
      accountCategory: 'REVENUES',
      accountNature: 'CREDIT',
      financialStatementType: 'BUDGET',
      accountSubCategory: 'GENERAL',
      isDepreciating: false,
      isHidden: false,
      isFreezed: false,
      isContraAccount: false,
      costCentreId: 0,
      note: null,
    },
    {
      accountId: 21,
      branchId: 1,
      accountNumber: ***********,
      nameArabic: 'discountaccount-1',
      nameEnglish: 'discountaccount-1',
      parentAccountId: 4,
      accountLevel: 2,
      accountType: 'DETAILED',
      accountCategory: 'REVENUES',
      accountNature: 'DEBIT',
      financialStatementType: 'BUDGET',
      accountSubCategory: 'GENERAL',
      isDepreciating: false,
      isHidden: false,
      isFreezed: false,
      isContraAccount: true,
      costCentreId: 0,
      note: '',
    },
    {
      accountId: 16,
      branchId: 1,
      accountNumber: 41,
      nameArabic: 'salesaccounts',
      nameEnglish: 'salesaccounts',
      parentAccountId: 4,
      accountLevel: 2,
      accountType: 'GENERAL',
      accountCategory: 'REVENUES',
      accountNature: 'CREDIT',
      financialStatementType: 'BUDGET',
      accountSubCategory: 'GENERAL',
      isDepreciating: false,
      isHidden: false,
      isFreezed: false,
      isContraAccount: false,
      costCentreId: 0,
      note: '',
    },
    {
      accountId: 17,
      branchId: 1,
      accountNumber: ***********,
      nameArabic: 'cashsalesacoount-1',
      nameEnglish: 'cashsalesacoount-1',
      parentAccountId: 16,
      accountLevel: 3,
      accountType: 'DETAILED',
      accountCategory: 'REVENUES',
      accountNature: 'CREDIT',
      financialStatementType: 'BUDGET',
      accountSubCategory: 'GENERAL',
      isDepreciating: false,
      isHidden: false,
      isFreezed: false,
      isContraAccount: false,
      costCentreId: 0,
      note: '',
    },
    {
      accountId: 18,
      branchId: 1,
      accountNumber: ***********,
      nameArabic: 'creditsalesaccounts-1',
      nameEnglish: 'creditsalesaccounts-1',
      parentAccountId: 16,
      accountLevel: 3,
      accountType: 'DETAILED',
      accountCategory: 'REVENUES',
      accountNature: 'CREDIT',
      financialStatementType: 'BUDGET',
      accountSubCategory: 'GENERAL',
      isDepreciating: false,
      isHidden: false,
      isFreezed: false,
      isContraAccount: false,
      costCentreId: 0,
      note: '',
    },
    {
      accountId: 19,
      branchId: 1,
      accountNumber: ***********,
      nameArabic: 'cardsalesaccounts-1',
      nameEnglish: 'cardsalesaccounts-1',
      parentAccountId: 16,
      accountLevel: 3,
      accountType: 'DETAILED',
      accountCategory: 'REVENUES',
      accountNature: 'CREDIT',
      financialStatementType: 'BUDGET',
      accountSubCategory: 'GENERAL',
      isDepreciating: false,
      isHidden: false,
      isFreezed: false,
      isContraAccount: false,
      costCentreId: 0,
      note: '',
    },
    {
      accountId: 20,
      branchId: 1,
      accountNumber: ***********,
      nameArabic: 'banksalesaccount-1',
      nameEnglish: 'banksalesaccount-1',
      parentAccountId: 16,
      accountLevel: 3,
      accountType: 'DETAILED',
      accountCategory: 'REVENUES',
      accountNature: 'CREDIT',
      financialStatementType: 'BUDGET',
      accountSubCategory: 'GENERAL',
      isDepreciating: false,
      isHidden: false,
      isFreezed: false,
      isContraAccount: false,
      costCentreId: 0,
      note: '',
    },
    {
      accountId: 5,
      branchId: 1,
      accountNumber: 5,
      nameArabic: 'Expense Accounts',
      nameEnglish: 'Expense Accounts',
      parentAccountId: 0,
      accountLevel: 1,
      accountType: 'GENERAL',
      accountCategory: 'EXPENSES',
      accountNature: 'DEBIT',
      financialStatementType: 'BUDGET',
      accountSubCategory: 'GENERAL',
      isDepreciating: false,
      isHidden: false,
      isFreezed: false,
      isContraAccount: false,
      costCentreId: 0,
      note: null,
    },
  ]);
  TREE_DATA: FoodNode[] = this.hierarchicalStructure;

  newChildName = '';
  selectedNode: FoodNode;
  treeControl = new NestedTreeControl<FoodNode>(node => node.children);
  dataSource = new ArrayDataSource(this.TREE_DATA);

  constructor(private cdr: ChangeDetectorRef) {}
  ngOnInit(): void {
    console.log(JSON.stringify(this.hierarchicalStructure, null, 2));
  }

  onNodeClick(node: FoodNode): void {
    this.selectedNode = node;
  }

  addChildToActiveNode(childName: string): void {
    // if (this.selectedNode) {
    //   // Find the selected node in the data source
    //   const foundNode = this.findNode(this.dataSource.data, this.selectedNode);
    //   if (foundNode) {
    //     // Add a new child to the found node
    //     const newChild: FoodNode = { name: childName };
    //     if (!foundNode.children) {
    //       foundNode.children = [];
    //     }
    //     foundNode.children.push(newChild);
    //     console.log('foundNode', foundNode);
    //     // Update the tree data source
    //     this.dataSource.data = [...this.dataSource.data];
    //     this.treeControl.dataNodes = [...this.dataSource.data];
    //     this.treeControl.collapseAll(); // Optionally collapse all nodes first
    //     setTimeout(() => this.treeControl.expandAll());
    //     // Clear the input field after adding the child
    //     this.newChildName = '';
    //     this.cdr.detectChanges();
    //   }
    // }
  }

  private findNode(nodes: FoodNode[], targetNode: FoodNode): FoodNode | undefined {
    for (const node of nodes) {
      if (node === targetNode) {
        return node;
      }
      if (node.children) {
        const foundNode = this.findNode(node.children, targetNode);
        if (foundNode) {
          return foundNode;
        }
      }
    }
    return undefined;
  }

  private expandNode(node: FoodNode): void {
    if (node) {
      if (!this.treeControl.isExpanded(node)) {
        this.treeControl.expand(node);
      }
      if (node.children) {
        node.children.forEach(child => {
          this.expandNode(child);
        });
      }
    }
  }

  hasChild = (_: number, node: FoodNode) => {
    console.log('nodes', node, !!node.children && node.children.length > 0);
    return !!node.children && node.children.length > 0;
  };

  hasNoContent = (_: number, _nodeData: FoodNode) => _nodeData.nameEnglish === '';

  // Function to generate hierarchical structure
  generateHierarchy(data: any[]): FoodNode[] {
    const nodes: { [key: number]: FoodNode } = {};

    data.forEach(item => {
      nodes[item.accountId] = {
        accountId: item.accountId,
        parentAccountId: item.parentAccountId,
        nameEnglish: item.nameEnglish,
        nameArabic: item.nameArabic,
        accountType: item.accountType,
        children: [],
        color: '',
      };
    });

    const roots: FoodNode[] = [];

    data.forEach(item => {
      if (item.parentAccountId !== 0) {
        nodes[item.parentAccountId].children?.push(nodes[item.accountId]);
      } else {
        roots.push(nodes[item.accountId]);
        nodes[item.accountId].color = 'bg-light-primary';
      }
    });
    data.forEach(item => {
      if (
        item.parentAccountId !== 0 &&
        !nodes[item.accountId].color &&
        nodes[item.accountId].accountType === 'DETAILED'
      ) {
        nodes[item.accountId].color = 'bg-light-warning'; // Non-top-level parents
      }
    });
    data.forEach(item => {
      if (
        item.parentAccountId !== 0 &&
        !nodes[item.accountId].color &&
        nodes[item.accountId].accountType === 'GENERAL'
      ) {
        nodes[item.accountId].color = 'bg-light-success'; // Non-top-level parents
      }
    });
    roots.forEach(root => {
      root.children?.forEach(child => {
        child.color = 'bg-light-error'; // Immediate children of top-level parent
      });
    });

    return this.sortNodes(roots);
  }

  sortNodes(data: FoodNode[]): FoodNode[] {
    const sortedNodes: FoodNode[] = data.filter(
      node => node.parentAccountId === 0 && node.children && node.children.length > 0
    );
    const remainingNodes: FoodNode[] = data.filter(
      node =>
        node.parentAccountId !== 0 ||
        (node.parentAccountId === 0 && (!node.children || node.children.length === 0))
    );

    return [...sortedNodes, ...remainingNodes];
  }

  filtertree(val: string) {
    const data = this.filterNodes(this.hierarchicalStructure, val);
    console.log(data);
    this.dataSource = new ArrayDataSource(data);
    this.treeControl.dataNodes = data;
    if (data) {
      this.treeControl.expandAll();
    } else {
      this.treeControl.collapseAll();
    }
  }

  private filterNodes(nodes: FoodNode[], filterText: string): FoodNode[] {
    return nodes
      .map(node => {
        const matches = node.nameEnglish.toLowerCase().includes(filterText);
        if (node.children && node.children.length > 0) {
          node.children = this.filterNodes(node.children, filterText);
        }
        return matches ? { ...node } : null;
      })
      .filter(Boolean); // Remove null values

    // return nodes.filter(node => {
    //   const containsFilterText = node.nameEnglish.toLowerCase().includes(filterText);
    //   if (containsFilterText) {
    //     if (node.children && node.children.length > 0) {
    //       node.children = this.filterNodes(node.children, filterText);
    //     }
    //   }
    //   return containsFilterText || (node.children && node.children.length > 0);
    // });
  }

  showDetailsForNode(node: FoodNode): void {
    this.selectedNode = node;
    this.showDetails = true;
  }

  hideDetails(): void {
    this.showDetails = false;
  }
}
