import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { PermissionGuard } from '../../core/core/guards/permission.guard';
import { ZatcaInvoiceStatusComponent } from '../../shared/components/zatca-invoice-status/zatca-invoice-status.component';
import { AccountListReportsComponent } from '../accounts/components/account-list-reports/account-list-reports.component';
import { AccountsReportsComponent } from '../accounts/components/account-reports/accounts-reports.component';
import { AccountStatementReportsComponent } from '../accounts/components/account-statement-reports/account-statement-reports.component';
import { ReportDashboardComponent } from './components/dashboard/report-dashboard.component';
import { InventoryReportComponent } from './components/inventory-report/inventory-report.component';
import { ReportSetupComponent } from './components/report-setup/report-setup.component';

const routes: Routes = [
  {
    path: '',
    redirectTo: 'searchReports',
    pathMatch: 'full',
  },
  {
    path: 'inventoryReports',
    component: InventoryReportComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'Inventory Reports',
      urls: [{ title: 'Reports Dashboard', url: '/reports' }],
      allowedPermissions: ['Accounting.Accounts', 'AllPermissions'],
    },
  },
  {
    path: 'reportsSetup',
    component: ReportSetupComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'Reports Setup',
      urls: [{ title: 'Reports Dashboard', url: '/reports' }],
      allowedPermissions: ['Accounting.Accounts', 'AllPermissions'],
    },
  },

  {
    path: 'zatcaReports',
    component: ZatcaInvoiceStatusComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'Zatca einvoice status reports',
      urls: [{ title: 'Zatca reports', url: '/reports/zatcaReports' }],
      allowedPermissions: [],
    },
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class ReportingRoutingModule {}
