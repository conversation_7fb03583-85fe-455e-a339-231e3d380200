import {
  Component,
  Input,
  Output,
  EventEmitter,
  ViewChildren,
  QueryList,
  ViewChild,
} from '@angular/core';
import { FormGroup } from '@angular/forms';
import { MatDatepicker } from '@angular/material/datepicker';
import { MatSelect } from '@angular/material/select';
import { AccountsProsearchBoxComponent } from '../../../accounts/components/accounts-prosearch-box/accounts-prosearch-box.component';
import { SearchboxComponent } from '../../../../shared/components/searchbox/searchbox.component';
import { ISearchConfig } from '../../models/search-config.interface';

@Component({
  selector: 'app-filter-form',
  templateUrl: './filter-form.component.html',
  styleUrls: ['./filter-form.component.scss'],
})
export class FilterFormComponent {
  @Input() searchConfigs: ISearchConfig[];
  @Input() filtersForm: FormGroup;
  @Input() isFullScreen = false;
  @Input() isAdvancedSearchVisible = false;
  @Input() getListForField: (field: string) => any[];
  @Input() getReportData: (event: Event) => void;
  @Input() onSearchInput: (input: string) => void;
  @Input() onItemCodeSelected: (input: any) => void;
  @Input() formName: string;
  @Input() onBranchChange?: () => void; // For handling branch change events
  @ViewChildren(MatSelect) matSelects!: QueryList<MatSelect>;
  @ViewChildren(MatDatepicker) datepickers: QueryList<MatDatepicker<any>>;
  @ViewChildren(SearchboxComponent) searchBoxComponents!: QueryList<SearchboxComponent>;

  openDatepicker(backendParam: string) {
    const datepicker = this.datepickers.find(
      picker =>
        picker.datepickerInput
          ?.getConnectedOverlayOrigin()
          .nativeElement.getAttribute('formcontrolname') === backendParam
    );
    if (datepicker) {
      datepicker.open();
    }
  }
  getSelectForField(field: string): MatSelect | undefined {
    return this.matSelects?.toArray().find(matSelect => matSelect.ngControl?.name === field);
  }
  toggleAdvancedSearch() {
    this.isAdvancedSearchVisible = !this.isAdvancedSearchVisible;
  }
  datePickerId(param: string): string {
    return param;
  }
  clearFilters(event: Event) {
    event.preventDefault();
    this.filtersForm.markAsUntouched();
    this.filtersForm.markAsPristine();
    this.filtersForm.reset();

    // Reset searchbox components if they exist
    this.searchBoxComponents?.forEach(searchBox => {
      if (searchBox && searchBox.resetSearchBox) {
        searchBox.resetSearchBox();
      }
    });
  }

  // Handle branch selection change for dependent fields
  onBranchSelectionChange(config: ISearchConfig) {
    if (this.onBranchChange && config.backendParam === 'branchId') {
      this.onBranchChange();
    }
  }

  // Get searchbox component for validation
  getSearchBoxComponent(index: number): SearchboxComponent | undefined {
    return this.searchBoxComponents?.toArray()[index];
  }
}
