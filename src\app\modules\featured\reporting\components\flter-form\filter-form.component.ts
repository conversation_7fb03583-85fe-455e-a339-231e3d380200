import { Component, EventEmitter, Input, Output } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { Report, ReportData, SearchConfig } from '../../models/report-template';
import { ActiveFilter } from '../filter-summary/filter-summary.component';

@Component({
  selector: 'app-filter-form',
  templateUrl: './filter-form.component.html',
  styleUrls: ['./filter-form.component.scss'],
})
export class FilterFormComponent {
  @Input() searchConfigs: SearchConfig[] = [];
  @Input() filtersForm: FormGroup;
  @Input() reportTypeList: ReportData = [];
  @Input() selectedReportType: Report | null = null;
  @Input() showReportTypeSelector = true;
  @Input() getListForField: (field: string) => unknown[];

  @Output() reportTypeChange = new EventEmitter<Report | null>();
  @Output() clear = new EventEmitter<void>();
  @Output() submit = new EventEmitter<Event>();
  @Output() itemCodeSelected = new EventEmitter<unknown>();
  @Output() branchChange = new EventEmitter<void>();
  @Output() clearFilters = new EventEmitter<void>();
  @Output() filterRemoved = new EventEmitter<string>();
  @Output() filterClicked = new EventEmitter<ActiveFilter>();
  @Output() filtersCleared = new EventEmitter<void>();

  selectedParentGroup: string | null = null;

  // Get filtered report types based on selected parent group
  get filteredReportTypes(): Report[] {
    if (!this.selectedParentGroup) return [];

    const reportGroup = this.reportTypeList.find(
      group => group.parentReportGroup === this.selectedParentGroup
    );
    return reportGroup ? reportGroup.reports : [];
  }

  // Handle parent group selection change
  onParentGroupChange(parentGroup: string | null): void {
    this.selectedParentGroup = parentGroup;
    if (this.selectedReportType) {
      this.selectedReportType = null;
      this.reportTypeChange.emit(null);
    }
  }

  // Handle report type selection change
  onReportTypeSelectionChange(reportType: Report | null): void {
    this.reportTypeChange.emit(reportType);
  }

  // Handle clear button click
  onClearFilters(event: Event): void {
    event.preventDefault();
    this.selectedParentGroup = null;
    this.selectedReportType = null;
    this.clear.emit();
  }

  // Handle reset filters button click
  onResetFilters(event: Event): void {
    this.clearFilters.emit();
  }

  // Handle form submission
  onSubmit(event: Event): void {
    event.preventDefault();
    this.submit.emit(event);
  }

  // Handle item code selection
  onItemCodeSelect(item: unknown): void {
    this.itemCodeSelected.emit(item);
  }

  // Handle branch selection change
  onBranchSelectionChange(config: SearchConfig): void {
    if (config.backendParam === 'branchId') {
      this.branchChange.emit();
    }
  }

  // Handle filter removal from summary
  onFilterRemoved(filterId: string): void {
    this.filterRemoved.emit(filterId);
  }

  // Handle filter click from summary (for editing)
  onFilterClicked(filter: ActiveFilter): void {
    this.filterClicked.emit(filter);
  }

  // Handle clear all filters from summary
  onClearAllFilters(): void {
    this.filtersCleared.emit();
  }

  // Handle filter summary toggle
  onFilterSummaryToggled(collapsed: boolean): void {
    // Can be used to store user preference or trigger other actions
    console.log('Filter summary toggled:', collapsed);
  }
}
