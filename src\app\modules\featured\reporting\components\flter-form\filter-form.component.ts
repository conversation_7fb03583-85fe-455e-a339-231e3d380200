import { Component, EventEmitter, Input, Output } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { Report, ReportData, SearchConfig } from '../../models/report-template';

@Component({
  selector: 'app-filter-form',
  templateUrl: './filter-form.component.html',
  styleUrls: ['./filter-form.component.scss'],
})
export class FilterFormComponent {
  @Input() searchConfigs: SearchConfig[] = [];
  @Input() filtersForm: FormGroup;
  @Input() reportTypeList: ReportData = [];
  @Input() selectedReportType: Report | null = null;
  @Input() showReportTypeSelector = true;
  @Input() getListForField: (field: string) => unknown[];

  @Output() reportTypeChange = new EventEmitter<Report | null>();
  @Output() clear = new EventEmitter<void>();
  @Output() submit = new EventEmitter<Event>();
  @Output() itemCodeSelected = new EventEmitter<unknown>();
  @Output() branchChange = new EventEmitter<void>();
  @Output() clearFilters = new EventEmitter<void>();

  selectedParentGroup: string | null = null;

  // Get filtered report types based on selected parent group
  get filteredReportTypes(): Report[] {
    if (!this.selectedParentGroup) return [];

    const reportGroup = this.reportTypeList.find(
      group => group.parentReportGroup === this.selectedParentGroup
    );
    return reportGroup ? reportGroup.reports : [];
  }

  // Handle parent group selection change
  onParentGroupChange(parentGroup: string | null): void {
    this.selectedParentGroup = parentGroup;
    if (this.selectedReportType) {
      this.selectedReportType = null;
      this.reportTypeChange.emit(null);
    }
  }

  // Handle report type selection change
  onReportTypeSelectionChange(reportType: Report | null): void {
    this.reportTypeChange.emit(reportType);
  }

  // Handle clear button click
  onClearFilters(event: Event): void {
    event.preventDefault();
    this.selectedParentGroup = null;
    this.selectedReportType = null;
    this.clear.emit();
  }

  // Handle reset filters button click
  onResetFilters(): void {
    this.clearFilters.emit();
  }

  // Handle form submission
  onSubmit(event: Event): void {
    event.preventDefault();
    this.submit.emit(event);
  }

  // Handle item code selection
  onItemCodeSelect(item: unknown): void {
    this.itemCodeSelected.emit(item);
  }

  // Handle branch selection change
  onBranchSelectionChange(config: SearchConfig): void {
    if (config.backendParam === 'branchId') {
      this.branchChange.emit();
    }
  }

  // ===== SIMPLE CSS CLASS METHOD =====

  /**
   * Get CSS class for a form control - directly from config or fallback
   */
  getControlClass(config: SearchConfig): string {
    // Use backend-provided CSS class if available
    if (config.cssClass) {
      return config.cssClass;
    }

    // Simple fallback based on type
    const fallbackClasses: Record<string, string> = {
      dropdown: 'small-control',
      multiSelectDropdown: 'medium-control',
      dateRange: 'medium-control',
      input: 'big-control',
      accountSearch: 'full-control',
    };

    return fallbackClasses[config.type] || 'medium-control';
  }
}
