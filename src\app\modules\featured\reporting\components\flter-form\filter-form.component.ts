import { Component, EventEmitter, Input, Output } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { Report, ReportData, SearchConfig, ControlCssConfig } from '../../models/report-template';

@Component({
  selector: 'app-filter-form',
  templateUrl: './filter-form.component.html',
  styleUrls: ['./filter-form.component.scss'],
})
export class FilterFormComponent {
  @Input() searchConfigs: SearchConfig[] = [];
  @Input() filtersForm: FormGroup;
  @Input() reportTypeList: ReportData = [];
  @Input() selectedReportType: Report | null = null;
  @Input() showReportTypeSelector = true;
  @Input() getListForField: (field: string) => unknown[];

  @Output() reportTypeChange = new EventEmitter<Report | null>();
  @Output() clear = new EventEmitter<void>();
  @Output() submit = new EventEmitter<Event>();
  @Output() itemCodeSelected = new EventEmitter<unknown>();
  @Output() branchChange = new EventEmitter<void>();
  @Output() clearFilters = new EventEmitter<void>();

  selectedParentGroup: string | null = null;

  // Get filtered report types based on selected parent group
  get filteredReportTypes(): Report[] {
    if (!this.selectedParentGroup) return [];

    const reportGroup = this.reportTypeList.find(
      group => group.parentReportGroup === this.selectedParentGroup
    );
    return reportGroup ? reportGroup.reports : [];
  }

  // Handle parent group selection change
  onParentGroupChange(parentGroup: string | null): void {
    this.selectedParentGroup = parentGroup;
    if (this.selectedReportType) {
      this.selectedReportType = null;
      this.reportTypeChange.emit(null);
    }
  }

  // Handle report type selection change
  onReportTypeSelectionChange(reportType: Report | null): void {
    this.reportTypeChange.emit(reportType);
  }

  // Handle clear button click
  onClearFilters(event: Event): void {
    event.preventDefault();
    this.selectedParentGroup = null;
    this.selectedReportType = null;
    this.clear.emit();
  }

  // Handle reset filters button click
  onResetFilters(event: Event): void {
    this.clearFilters.emit();
  }

  // Handle form submission
  onSubmit(event: Event): void {
    event.preventDefault();
    this.submit.emit(event);
  }

  // Handle item code selection
  onItemCodeSelect(item: unknown): void {
    this.itemCodeSelected.emit(item);
  }

  // Handle branch selection change
  onBranchSelectionChange(config: SearchConfig): void {
    if (config.backendParam === 'branchId') {
      this.branchChange.emit();
    }
  }

  // ===== CSS CLASS GENERATION METHODS =====

  /**
   * Get CSS classes for a form control based on config
   */
  getControlClasses(config: SearchConfig): string {
    const classes: string[] = [];

    // Add size class (from config or fallback)
    const sizeClass = this.getControlSizeClass(config);
    classes.push(sizeClass);

    // Add modifier classes from config
    if (config.cssClasses?.modifiers) {
      classes.push(...config.cssClasses.modifiers);
    }

    // Add custom classes from config
    if (config.cssClasses?.custom) {
      classes.push(...config.cssClasses.custom);
    }

    // Add mandatory class if required
    if (config.mandatory) {
      classes.push('control-mandatory');
    }

    // Add advanced class if applicable
    if (config.isAdvanced) {
      classes.push('control-advanced');
    }

    return classes.join(' ');
  }

  /**
   * Get the primary size class for a control
   */
  private getControlSizeClass(config: SearchConfig): string {
    // Use config-specified size class if available
    if (config.cssClasses?.size) {
      return config.cssClasses.size;
    }

    // Fallback to type-based sizing for backward compatibility
    return this.getDefaultSizeClassByType(config.type);
  }

  /**
   * Get default size class based on field type (fallback)
   */
  private getDefaultSizeClassByType(type: string): string {
    const typeToSizeMap: Record<string, string> = {
      dropdown: 'control-small',
      multiSelectDropdown: 'control-medium',
      dateRange: 'control-medium',
      input: 'control-large',
      accountSearch: 'control-full',
    };

    return typeToSizeMap[type] || 'control-medium';
  }

  /**
   * Get responsive classes for a control
   */
  getResponsiveClasses(config: SearchConfig): Record<string, boolean> {
    const responsive = config.cssClasses?.responsive;
    if (!responsive) {
      return {};
    }

    const classes: Record<string, boolean> = {};

    if (responsive.mobile) {
      classes[`mobile-${responsive.mobile}`] = true;
    }
    if (responsive.tablet) {
      classes[`tablet-${responsive.tablet}`] = true;
    }
    if (responsive.desktop) {
      classes[`desktop-${responsive.desktop}`] = true;
    }

    return classes;
  }

  /**
   * Check if control has custom styling
   */
  hasCustomStyling(config: SearchConfig): boolean {
    return !!(config.cssClasses?.custom?.length || config.cssClasses?.modifiers?.length);
  }
}
