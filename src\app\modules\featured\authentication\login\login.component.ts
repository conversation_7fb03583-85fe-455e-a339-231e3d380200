import { DOCUMENT } from '@angular/common';
import { Component, Inject, OnInit, ViewChild } from '@angular/core';
import { FormControl, FormGroup, UntypedFormControl, Validators } from '@angular/forms';
import { MatInput } from '@angular/material/input';
import { ActivatedRoute, Router } from '@angular/router';
import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';
import { TranslateService } from '@ngx-translate/core';
import { CookieService } from 'ngx-cookie-service';
import { CommonService } from 'src/app/core/api/common.service';
import { Token } from 'src/app/core/models/identity/token';
import { ILoginBranches, IYear } from 'src/app/modules/core/core/models/login';
import { AuthService } from 'src/app/modules/core/core/services/auth.service';
import { BranchService } from 'src/app/modules/core/core/services/branch.service';
import { LocalStorageService } from 'src/app/modules/core/core/services/local-storage.service';
import { MultilingualService } from 'src/app/modules/core/core/services/multilingual.service';
import { TokenService } from 'src/app/modules/core/core/services/token.service';

export interface LoginForm {
  username: FormControl<string>;
  password: FormControl<string>;
  tenantId: FormControl<string | null>;
  userType: FormControl<boolean | null>;
}

@UntilDestroy()
@Component({
  selector: 'app-login',
  templateUrl: './login.component.html',
  styleUrls: ['./login.component.scss'],
})
export class LoginComponent implements OnInit {
  @ViewChild('username', { static: true }) username: MatInput;
  @ViewChild('branch', { static: true }) branch: MatInput;
  showBranchSelectionForm = false;
  form: FormGroup<LoginForm>;
  branchId: UntypedFormControl = new UntypedFormControl(null, Validators.required);
  yearId: UntypedFormControl = new UntypedFormControl(null, Validators.required);
  returnUrl: string;
  isBeingLoggedIn = false;
  passwordhide = true;
  confirmPasswordhide = true;
  token: Token;
  branchList: ILoginBranches[];
  yearList: IYear[];
  preferredLanguage = 'en'; // Default to English

  constructor(
    private authService: AuthService,
    private router: Router,
    private activatedRoute: ActivatedRoute,
    private localStorage: LocalStorageService,
    private cookieService: CookieService,
    private commonService: CommonService,
    private multilingualService: MultilingualService,
    private translateService: TranslateService,
    private tokenService: TokenService,
    private branchService: BranchService,
    @Inject(DOCUMENT) private document: Document
  ) {}

  ngOnInit(): void {
    // SIMPLE: Set body direction to LTR when login component loads
    this.document.body.setAttribute('dir', 'ltr');

    this.initializeForm();
    this.returnUrl = this.activatedRoute.snapshot.queryParams['returnUrl'] || '/dashboard';
    this.localStorage.clear();
    this.branchId.valueChanges.subscribe(branchChange => {
      this.yearList = this.branchList
        .filter((data: ILoginBranches) => data.branchId === branchChange)
        .map((data2: ILoginBranches) => data2.years)[0];
      this.yearId.setValue(null);
    });

    // Check for saved language preference
    const savedLanguage = this.cookieService.get('login_language');
    if (savedLanguage) {
      this.setPreferredLanguage(savedLanguage);
    }
  }

  /**
   * Sets the preferred language for the login screen
   * This only affects the login screen, not the entire application
   */
  setPreferredLanguage(lang: string): void {
    // SIMPLE: Always keep body direction as LTR for login
    this.document.body.setAttribute('dir', 'ltr');
  }

  initializeForm() {
    this.form = new FormGroup<LoginForm>({
      username: new FormControl<string>('', Validators.required),
      password: new FormControl<string>('', Validators.required),
      tenantId: new FormControl<string | null>(null, Validators.required),
      userType: new FormControl<boolean>(true),
    });
    this.subscribeToFormChanges();
  }

  subscribeToFormChanges(): void {
    // userType toggle changes
    this.form.controls.userType.valueChanges.subscribe(value => {
      const tenantIdControl = this.form.controls.tenantId;
      if (value) {
        tenantIdControl.setValidators(Validators.required);
      } else {
        this.form.markAsUntouched();
        tenantIdControl.setValue(null);
        tenantIdControl.clearValidators();
      }
      tenantIdControl.updateValueAndValidity();
    });
  }

  onSubmit() {
    this.form.markAllAsTouched();
    if (this.form.valid) {
      this.isBeingLoggedIn = true;
      this.form.disable();
      // decide the api call
      this.loginforTenant();
    } else {
      this.commonService.playErrorSound();
    }
  }

  loginforTenant(): void {
    this.authService
      .login(this.form.getRawValue())
      .pipe(untilDestroyed(this))
      .subscribe(
        result => {
          this.getUserBranchesYearId();
          this.token = result;
          this.isBeingLoggedIn = true;
          this.translateService.use(this.tokenService.getLanguage());
        },
        error => {
          this.form.enable();
        }
      )
      .add(() => (this.isBeingLoggedIn = false));
  }

  getUserBranchesYearId(): void {
    this.branchService.getUserBranchesYearId(this.authService.getCompanyID).subscribe({
      next: (branches: ILoginBranches[]) => {
        if (branches.length === 1) {
          this.branchList = branches;
          // check if years are multiple
          if (branches[0].years.length > 1) {
            this.showBranchSelection(branches);
          } else {
            this.showBranchSelectionForm = false;
            this.branchId.setValue(branches[0].branchId);
            this.yearId.setValue(branches[0].years[0]);
            this.onSubmitBranchSelection();
          }
        } else {
          this.branchList = branches;
          this.showBranchSelection(branches);
        }
        this.isBeingLoggedIn = false;

        // Store branches data for later use
        this.localStorage.setItem('userBranches', JSON.stringify(branches));
      },
      error: (error: unknown) => {
        console.log(error);
      },
    });
  }

  showBranchSelection(result): void {
    this.branchList = result;
    this.branchId.setValue(result[0].branchId);
    this.yearId.setValue(result[0].years[0]);
    this.yearList = this.branchList
      .filter((data: ILoginBranches) => data.branchId === this.branchId.value)
      .map((data2: ILoginBranches) => data2.years)[0];
    this.showBranchSelectionForm = true;
  }

  onSubmitBranchSelection(event?: Event) {
    event?.stopPropagation();

    const branchId = this.branchId.value;
    const yearId = this.yearId.value['id'];
    const year = this.yearId.value['year'];
    const branchName = this.branchList
      .filter((data: ILoginBranches) => data.branchId === branchId)
      .map((data2: ILoginBranches) => data2.branchName)
      .toString();
    this.branchService
      .switchBranch(branchId, yearId, branchName, year)
      .pipe(untilDestroyed(this))
      .subscribe({
        next: () => {
          this.multilingualService
            .loadDefaultLanguage()
            .pipe(untilDestroyed(this))
            .subscribe(() => {
              this.completeLogin();
            });
        },
        error: error => {
          console.log(error);
        },
      });
  }

  private completeLogin(): void {
    this.commonService.playSuccessSound();
    this.router.navigateByUrl(this.returnUrl);
  }
}
