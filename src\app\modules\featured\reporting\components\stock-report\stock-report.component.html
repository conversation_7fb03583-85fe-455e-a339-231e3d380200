<!-- Filter Place Holders -->
<mat-card class="filter-card" *ngIf="filtersForm && searchConfigs" appearance="outlined">
  <app-filter-form
    [formName]="formName"
    [searchConfigs]="searchConfigs"
    [filtersForm]="filtersForm"
    [isFullScreen]="false"
    [isAdvancedSearchVisible]="isAdvancedSearchVisible"
    [getListForField]="getListForField.bind(this)"
    [getReportData]="getReportData.bind(this)"
    [onBranchChange]="onBranchChange.bind(this)">
  </app-filter-form>
</mat-card>
<!-- Filter Place Holders -->
