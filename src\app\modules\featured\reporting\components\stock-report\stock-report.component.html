<!-- Enhanced Stock Reports with Dynamic Report Type Selection -->
<div class="table-container">
  <div class="loading-container" *ngIf="loading">
    <mat-spinner></mat-spinner>
    <p>{{ 'common.loading' | translate }}</p>
  </div>

  <div *ngIf="!loading">
    <app-filter-form
      [searchConfigs]="searchConfigs"
      [filtersForm]="filtersForm"
      [isAdvancedSearchVisible]="isAdvancedSearchVisible"
      [getListForField]="getListForField"
      [getReportData]="getReportData"
      [onSearchInput]="onSearchInput"
      [onItemCodeSelected]="onItemCodeSelected"
      [formName]="formName"
      [onBranchChange]="onBranchChange"
      [reportTypeList]="reportTypeList"
      [showReportTypeSelector]="showReportTypeSelector"
      [selectedReportType]="selectedReportType"
      [onReportTypeChange]="onReportTypeChange">
    </app-filter-form>
  </div>
</div>
