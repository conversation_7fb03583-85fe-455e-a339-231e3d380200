<form class="mat-typography" [formGroup]="autoCompleteInput" autocomplete="off">
  <div class="row no-gutters">
    <div class="col-12 p-2">
      <mat-label *ngIf="!customSearch">{{ 'productSearch.productSearch' | translate }}</mat-label>
      <mat-form-field class="w-100">
        <div class="input-with-icons">
          <input
            class="text-primary"
            #userInput
            [matAutocomplete]="auto"
            type="text"
            placeholder="{{ 'productSearch.placeHolder' | translate }}"
            matInput
            formControlName="userInput" />
          <div class="icon-container">
            <a
              class="cursor-pointer"
              *ngIf="!isLoading"
              (click)="getAllProducts(); $event.preventDefault(); $event.stopPropagation()"
              matSuffix>
              <i-tabler class="icon-16" name="database-search"></i-tabler>
            </a>
            <a
              class="cursor-pointer"
              *ngIf="!isLoading"
              (click)="clearSelection(); $event.preventDefault(); $event.stopPropagation()"
              matSuffix>
              <i-tabler class="icon-16 text-error" name="X"></i-tabler>
            </a>
          </div>
        </div>
        <mat-autocomplete
          class="new"
          #auto="matAutocomplete"
          (opened)="keyboardNavDirective?.onAutocompleteOpened()">
          <mat-option
            *ngIf="itemSource?.data?.length > 0; else noData"
            [hideSingleSelectionIndicator]="true">
            <div class="table-responsive" *ngIf="!isLoading">
              <table class="w-100" #table [dataSource]="itemSource" mat-table>
                <ng-container *ngFor="let column of displayedSearchColumns" [matColumnDef]="column">
                  <th *matHeaderCellDef mat-header-cell>
                    {{ 'productSearch.' + column | translate }}
                  </th>
                  <td class="f-w-600" *matCellDef="let product" mat-cell>
                    <ng-container [ngSwitch]="column">
                      <ng-container *ngSwitchCase="'discount'">
                        <div class="d-flex">
                          <i-tabler
                            class="icon-14"
                            *ngIf="product.isGeneralDscntMethod"
                            name="percentage"></i-tabler>
                          <i-tabler
                            class="icon-14"
                            *ngIf="!product.isGeneralDscntMethod"
                            name="cash"></i-tabler>
                          {{ product.discount }}
                        </div>
                      </ng-container>
                      <ng-container *ngSwitchDefault>
                        {{ product[column] }}
                      </ng-container>
                    </ng-container>
                  </td>
                </ng-container>

                <tr *matHeaderRowDef="displayedSearchColumns; sticky: true" mat-header-row></tr>
                <tr
                  *matRowDef="let row; columns: displayedSearchColumns; let i = index"
                  [appMatTableKeyboardNavigation]="selection"
                  [rowModel]="row"
                  [matTable]="table"
                  [focusFirstOption]="true"
                  [selectOnFocus]="true"
                  [ngClass]="{ selected: selection.isSelected(row) }"
                  (rowSelected)="onSelection($event)"
                  (click)="onSelection(row)"
                  mat-row></tr>
              </table>
            </div>
          </mat-option>
          <ng-template #noData>
            <mat-option *ngIf="resultNotFound" disabled>
              <div class="text-center-no-data bg-light-error" *ngIf="resultNotFound">
                {{ 'common.searchnodata' | translate }}
              </div>
            </mat-option>
          </ng-template>
        </mat-autocomplete>
      </mat-form-field>
      <mat-progress-bar *ngIf="isLoading" mode="query"></mat-progress-bar>
      <!-- </mat-form-field> -->
    </div>
  </div>
</form>
