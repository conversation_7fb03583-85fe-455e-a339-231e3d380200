import { animate, state, style, transition, trigger } from '@angular/animations';
import { Directionality } from '@angular/cdk/bidi';
import { ChangeDetectorRef, Component, OnInit, ViewChild } from '@angular/core';
import {
  AbstractControl,
  UntypedFormArray,
  UntypedFormBuilder,
  UntypedFormControl,
  UntypedFormGroup,
  ValidationErrors,
  Validators,
} from '@angular/forms';
import { MatDialog, MatDialogConfig } from '@angular/material/dialog';
import { MatSelectChange } from '@angular/material/select';
import { MatTable } from '@angular/material/table';
import { ActivatedRoute, Router } from '@angular/router';
import { CustomValidators } from 'ngx-custom-validators';
import { ToastrService } from 'ngx-toastr';
import { BehaviorSubject, forkJoin, of } from 'rxjs';
import { switchMap } from 'rxjs/operators';
import { CostCentresApiService } from 'src/app/core/api/accounts/cost-centres-api.service';
import { CommonService } from 'src/app/core/api/common.service';
import { StaticDataService } from 'src/app/core/api/static-data.service';
import { CustomerService } from 'src/app/core/api/trading/customer.service';
import { DistributorService } from 'src/app/core/api/trading/distributor.service';
import { SalesAdjustmentService } from 'src/app/core/api/trading/sales-adjustments.service';
import { SalesService } from 'src/app/core/api/trading/sales.service';
import { ZatcaService } from 'src/app/core/api/trading/zatca.service';
import { sections } from 'src/app/core/configs/dropDownConfig';
import { ActionType } from 'src/app/core/enums/actionType';
import { IActionEventType } from 'src/app/core/interfaces/actionEventType';
import { ICustomer, ICustomerView } from 'src/app/core/interfaces/customer';
import { IDistributor } from 'src/app/core/interfaces/distributor';
import { InvalidQuantity } from 'src/app/core/interfaces/error';
import { IPaymentDetails, IPaymentViewData } from 'src/app/core/interfaces/payment';
import {
  DeleteItemRows,
  ISaleDetails,
  ISalesItem,
  SalesCreateResponse,
  SalesIntegratedCreateResponse,
  SaletransactionTypes,
  ZatcaExceptionResponse,
} from 'src/app/core/interfaces/sales';
import { DistributorParams } from 'src/app/core/models/params/distributorParams';
import { SalesIntegeratedParams, SalesParams } from 'src/app/core/models/params/salesParams';
import { displayValue, miscOptions } from 'src/app/core/models/wrappers/displayValue';
import { convertDateForBE } from 'src/app/core/utils/date-utils';
import { CostCentre } from 'src/app/modules/featured/accounts/models/costCentre';
import { IInventory } from 'src/app/modules/featured/catalog/models/product';
import { ConfirmDialogComponent } from 'src/app/modules/shared/components/confirm-dialog/confirm-dialog.component';
import { DeleteConfirmationComponent } from 'src/app/modules/shared/components/delete-confirmation/delete-confirmation.component';
import { PaymentsPostSaleComponent } from 'src/app/modules/shared/components/payments-post-sale/payments-post-sale.component';
import {
  PrintDialogComponent,
  PrintDialogData,
} from 'src/app/modules/shared/components/print-dialog/print-dialog.component';
import { ProductSearchSelectionComponent } from 'src/app/modules/shared/components/product-search/product-search-selection/product-search-selection.component';
import { StandardPaymentsComponent } from 'src/app/modules/shared/components/standard-payments/standard-payments.component';
import { CustomerSelectionComponent } from '../../customers/customer-selection/customer-selection.component';
import { QuotationTermsComponent } from '../../quotation-terms/quotation-terms.component';
import { SalesCalculation } from '../../sales/sales-calculation';

@Component({
  selector: 'app-quotation-form',
  templateUrl: './quotation-form.component.html',
  styleUrls: ['./quotation-form.component.scss'],
  animations: [
    trigger('detailExpand', [
      state('collapsed', style({ height: '0px', minHeight: '0' })),
      state('expanded', style({ height: '*' })),
      transition('expanded <=> collapsed', animate('225ms cubic-bezier(0.4, 0.0, 0.2, 1)')),
    ]),
  ],
})
export class QuotationFormComponent extends SalesCalculation implements OnInit {
  @ViewChild(MatTable) table: MatTable<AbstractControl[]>;
  @ViewChild('paymentForm') private paymentForm: StandardPaymentsComponent;
  @ViewChild('customerSelection') private customerSelection: CustomerSelectionComponent;
  @ViewChild('paymentpostsales') private paymentpostsales: PaymentsPostSaleComponent;
  @ViewChild('productSearch') private productSearch: ProductSearchSelectionComponent;
  @ViewChild('quotations') private quotations: QuotationTermsComponent;
  // Matatable
  dataSource = new BehaviorSubject<AbstractControl[]>([]);
  displayedColumns: string[] = [
    'itemCode',
    'itemName',
    'unitName',
    'priceType',
    'quantity',
    'price',
    'discount',
    'vatAmount',
    'subtotal',
    'notes',
    'action',
  ];
  expandedFieldNames: Array<displayValue | miscOptions>;
  columnsToDisplayWithExpand = [...this.displayedColumns, 'expand'];
  // Holders

  priceTypes: displayValue[];
  distributorAccounts: IDistributor[] = [];
  costCentreAccounts: CostCentre[] = [];
  salesByIdResult: ISaleDetails;
  currentSalesData: ISaleDetails = <ISaleDetails>{};
  saleDetails: ISaleDetails;
  products: IInventory[];
  salesId: string;
  customerViewData: ICustomerView = <ICustomerView>{};
  paymentViewData: IPaymentViewData = <IPaymentViewData>{};
  // Main Form
  itemRows: UntypedFormArray = this.formBuilder.array([]);
  salesForm: UntypedFormGroup = this.formBuilder.group({
    issueDate: new UntypedFormControl(new Date(), Validators.required),
    orderNumber: new UntypedFormControl(null),
    orderDate: new UntypedFormControl(new Date(), Validators.required),
    costCentreId: new UntypedFormControl(null),
    distributorAccountId: new UntypedFormControl(null),
    invoiceDiscount: new UntypedFormControl(0),
    notes: new UntypedFormControl(null),
    items: this.itemRows,
  });
  loading = true;
  mode: ActionType;
  formTitle: string;
  expandedRowIndex: number | null = null;
  constructor(
    private formBuilder: UntypedFormBuilder,
    private dialog: MatDialog,
    private costCentresApiService: CostCentresApiService,
    private distributorService: DistributorService,
    private salesService: SalesService,
    private toastr: ToastrService,
    private router: Router,
    private route: ActivatedRoute,
    private staticDataService: StaticDataService,
    private commonService: CommonService,
    private salesAdjustmentService: SalesAdjustmentService,
    private zatcaService: ZatcaService,
    private direction: Directionality,
    private customerService: CustomerService,
    private changeDetectorRef: ChangeDetectorRef
  ) {
    super();
  }

  ngOnInit(): void {
    this.loading = true;
    this.mode = this.route.snapshot.data['mode'];
    this.formTitle = this.route.snapshot.data['title'];
    this.route.params.subscribe(params => {
      this.salesId = params['id'];
      this.getAllDropDownData();
    });
    this.setPageDisabled();
  }

  get IsReadonlyMode() {
    return this.IsPostMode || this.IsViewMode || this.isDeleteMode;
  }

  get canSelectProduct() {
    return !(
      this.mode === ActionType.view ||
      this.mode === ActionType.post ||
      this.mode === ActionType.delete
    );
  }

  get IsFullCreditMode(): boolean {
    return this.mode === ActionType.fullCreditNote;
  }

  get isEditMode(): boolean {
    return this.mode === ActionType.edit;
  }

  get isDeleteMode(): boolean {
    return this.mode === ActionType.delete;
  }
  get IsViewMode(): boolean {
    return this.mode === ActionType.view;
  }

  get IsCreateMode(): boolean {
    return this.mode === ActionType.create;
  }

  get IsPostMode(): boolean {
    return this.mode === ActionType.post;
  }

  get canDeleteProduct() {
    return this.canSelectProduct;
  }

  setPageDisabled(): void {
    if (this.mode === ActionType.view || this.mode === ActionType.delete) {
      this.salesForm.disable();
    }
  }

  patchData(data: ISaleDetails): void {
    const viewData: ICustomerView = {};
    viewData.isViewMode = true;
    viewData.customer = data.customer;
    viewData.existingClient = data.existingClient;
    this.customerViewData = viewData;
    //
    this.paymentViewData = data.payments;
    this.paymentViewData.isViewMode = this.IsViewMode ? true : false;
    this.saleDetails = data;
    if (data?.items && data.items.length) {
      data.items.forEach(element => {
        this.addSaleDetailForView(element);
      });
    }
    this.salesForm.patchValue({
      issueDate: data.issueDate,
      orderNumber: data.orderNumber,
      orderDate: data.orderDate,
      costCentreId: data.costCentreId,
      distributorAccountId: data.distributorAccountId,
      invoiceDiscount: 0,
      notes: data.notes,
    });
    this.setPageDisabled();
  }

  getAllDropDownData(): void {
    const salesParams = new SalesParams();
    salesParams.transactionType = SaletransactionTypes.quotation;
    const salesById = this.salesId
      ? this.salesService.getSalesById(this.salesId, salesParams)
      : of(null);
    const distributorAccounts = this.distributorService.getAllDistributors(new DistributorParams());
    const costCentreAccounts = this.costCentresApiService.getAllCostCentres();
    forkJoin([distributorAccounts, costCentreAccounts, salesById])
      .pipe(
        switchMap(results => {
          this.distributorAccounts = results[0]?.distributors;
          this.costCentreAccounts = results[1];
          if (results[2]) {
            this.currentSalesData = results[2];
          }
          // in create mode we want dummy data so user can fill it: till backend is fixed
          if (this.IsCreateMode) {
            this.currentSalesData.provisionList = sections;
          }
          if (this.currentSalesData && this.currentSalesData.existingClient) {
            return this.customerService.getCustomerById(
              this.currentSalesData.customer.customerId.toString()
            );
          } else {
            return of(null); // Return a placeholder observable if no additional call needed
          }
        })
      )
      .subscribe(
        additionalData => {
          if (additionalData !== null) {
            this.currentSalesData.customer = additionalData;
            this.patchData(this.currentSalesData);
          } else {
            // is walking customer
            if (this.salesId) {
              this.patchData(this.currentSalesData);
            }
          }
          this.loading = false;
        },
        error => {
          console.log(error);
        }
      );
    this.expandedFieldNames = this.staticDataService.getSalesExpandedColumns;
    this.priceTypes = this.staticDataService.getSalesPriceTypes;
  }

  addSaleDetailForView(product: ISalesItem): void {
    const saleDetail: ISalesItem = {
      id: product?.id,
      returnableQty: product.returnableQty,
      transactionItemId: product.transactionItemId,
      itemId: product.itemId,
      itemCode: product.itemCode,
      quantity: product.quantity,
      itemUnitId: product.itemUnitId,
      price: product.price,
      subtotal: product.subtotal,
      vat: product.product.vat,
      vatAmount: product.vatAmount,
      product: product.product,
      discount: product.discount,
      isGeneralDscntMethod: product.product.isGeneralDscntMethod,
      priceType: product.priceType,
      warehouseName: product.warehouseName,
      warehouseId: product.warehouseId,
      itemName: product.itemName,
      unitName: product.unitName,
      subTotalVat: product.subTotalVat,
    };
    this.onAddNewItem(saleDetail);
    this.dataSource.next(this.itemRows.controls);
    this.formItems(this.itemRows);
    this.updateAllRelevantCounts();
  }

  addSaleDetail(product: IInventory) {
    console.log('addSaleDetail ->', product);
    if (!this.checkProductExist(product)) {
      const saleDetail: ISalesItem = {
        itemId: product.itemId,
        itemCode: product.itemCode,
        quantity: 1,
        itemUnitId: product.itemUnitId,
        price: product.retailPrice,
        subtotal: this.countSubTotal(
          1,
          product.retailPrice,
          product.discount,
          product.isGeneralDscntMethod,
          this.subTotalVat(
            product.retailPrice,
            product.vat,
            product.discount,
            product.isGeneralDscntMethod
          )
        ),
        vat: product.vat,
        vatAmount: this.vatAmount(
          product.retailPrice,
          product.discount,
          product.isGeneralDscntMethod,
          product.vat
        ),
        product: product,
        discount: product.discount,
        isGeneralDscntMethod: product.isGeneralDscntMethod,
        priceType: 0,
        warehouseName: product.warehouseName,
        warehouseId: product.warehouseId,
        itemName: product.itemName,
        unitName: product.unitName,
        subTotalVat: this.subTotalVat(
          product.retailPrice,
          product.vat,
          product.discount,
          product.isGeneralDscntMethod
        ),
      };
      this.onAddNewItem(saleDetail);
      this.dataSource.next(this.itemRows.controls);
      this.formItems(this.itemRows);
      this.updateAllRelevantCounts();
      const field = document.querySelectorAll('.next')[0] as HTMLInputElement;
      setTimeout(() => field.focus());
    } else {
      console.log('product was found so update ->', product.itemCode);
      const index = this.itemsArray.controls.findIndex(
        products =>
          products.value.itemCode === product.itemCode &&
          products.value.warehouseId === product.warehouseId &&
          products.value.unitName === product.unitName
      );
      const data = this.getSpecificFormArray(index);
      data.patchValue({
        quantity: data.get('quantity').value + 1,
        subtotal: this.countSubTotal(
          data.get('quantity').value + 1,
          data.get('price').value,
          data.get('discount').value,
          data.get('isGeneralDscntMethod').value,
          this.subTotalVat(
            data.get('price').value,
            data.get('vat').value,
            data.get('discount').value,
            data.get('isGeneralDscntMethod').value
          )
        ),
        vatAmount:
          this.vatAmount(
            data.get('price').value,
            data.get('discount').value,
            data.get('isGeneralDscntMethod').value,
            data.get('vat').value
          ) *
          (data.get('quantity').value + 1),
      });
      this.dataSource.next(this.itemRows.controls);
      this.formItems(this.itemRows);
      this.updateAllRelevantCounts();
    }
  }

  removeSaleDetail(data: DeleteItemRows) {
    console.log('removeSaleDetail ->', data);
    const index = this.itemsArray.controls.findIndex(product => {
      console.log(product);
      return (
        product.value.itemCode === data.itemCode &&
        product.value.warehouseId === data.warehouseId &&
        product.value.unitName === data.unitName
      );
    });
    this.itemRows.removeAt(index);
    this.dataSource.next(this.itemRows.controls);
    this.formItems(this.itemRows);
    this.updateAllRelevantCounts();
  }

  checkProductExist(products: IInventory) {
    console.log('checkProductExist -> ', products);
    return this.itemsArray.controls.find(product => {
      console.log(product);
      return (
        product.value.itemCode === products.itemCode &&
        product.value.warehouseId === products.warehouseId &&
        product.value.unitName === products.unitName
      );
    });
  }

  getControlsIndexFromArray(controlErrors: InvalidQuantity) {
    return this.itemsArray.controls.findIndex(product => {
      console.log(product);
      return (
        product.value.product.itemCode === controlErrors.itemCode &&
        product.value.product.warehouseId === controlErrors.warehouseId &&
        product.value.product.unitName === controlErrors.unitName
      );
    });
  }

  onSelection(selectedProduct: IInventory) {
    this.addSaleDetail(selectedProduct);
  }

  onPriceTypeChange(event: MatSelectChange, index: number) {
    console.log('onPriceTypeChange -> ', event, event.source.triggerValue, index);
    const data = this.getSpecificFormArray(index);
    const priceType = this.priceTypes.find(data => data.value === event.value);
    const productData = data.get('product').value;
    data.patchValue({
      price: productData[priceType.display],
      priceType: event.value,
      subtotal: this.countSubTotal(
        data.get('quantity').value,
        data.get('price').value,
        data.get('discount').value,
        data.get('isGeneralDscntMethod').value,
        this.subTotalVat(
          data.get('price').value,
          data.get('vat').value,
          data.get('discount').value,
          data.get('isGeneralDscntMethod').value
        )
      ),
      subTotalVat:
        this.subTotalVat(
          data.get('price').value,
          data.get('vat').value,
          data.get('discount').value,
          data.get('isGeneralDscntMethod').value
        ) * data.get('quantity').value,
      vatAmount:
        this.vatAmount(
          data.get('price').value,
          data.get('discount').value,
          data.get('isGeneralDscntMethod').value,
          data.get('vat').value
        ) * data.get('quantity').value,
    });
    this.dataSource.next(this.itemRows.controls);
    this.formItems(this.itemRows);
    this.updateAllRelevantCounts();
  }

  onQuantityChange(event, index: number) {
    console.log('onQuantityChange -> ', event.srcElement.value, index);
    const data = this.getSpecificFormArray(index);
    data.patchValue({
      quantity: event.srcElement.value || 0,
      subtotal: this.countSubTotal(
        event.srcElement.value,
        data.get('price').value,
        data.get('discount').value,
        data.get('isGeneralDscntMethod').value,
        this.subTotalVat(
          data.get('price').value,
          data.get('vat').value,
          data.get('discount').value,
          data.get('isGeneralDscntMethod').value
        )
      ),
      subTotalVat:
        this.subTotalVat(
          data.get('price').value,
          data.get('vat').value,
          data.get('discount').value,
          data.get('isGeneralDscntMethod').value
        ) * event.srcElement.value,
      vatAmount:
        this.vatAmount(
          data.get('price').value,
          data.get('discount').value,
          data.get('isGeneralDscntMethod').value,
          data.get('vat').value
        ) * event.srcElement.value,
    });
    this.dataSource.next(this.itemRows.controls);
    this.formItems(this.itemRows);
    this.updateAllRelevantCounts();
  }

  onPriceChange(event, index: number) {
    console.log('onPriceChange -> ', event, index);
    const data = this.getSpecificFormArray(index);
    data.patchValue({
      price: event.srcElement.value || 0,
      subtotal: this.countSubTotal(
        data.get('quantity').value,
        event.srcElement.value,
        data.get('discount').value,
        data.get('isGeneralDscntMethod').value,
        this.subTotalVat(
          event.srcElement.value,
          data.get('vat').value,
          data.get('discount').value,
          data.get('isGeneralDscntMethod').value
        )
      ),
      subTotalVat:
        this.subTotalVat(
          event.srcElement.value,
          data.get('vat').value,
          data.get('discount').value,
          data.get('isGeneralDscntMethod').value
        ) * data.get('quantity').value,
      vatAmount:
        this.vatAmount(
          event.srcElement.value,
          data.get('discount').value,
          data.get('isGeneralDscntMethod').value,
          data.get('vat').value
        ) * data.get('quantity').value,
    });
    this.dataSource.next(this.itemRows.controls);
    this.formItems(this.itemRows);
    this.updateAllRelevantCounts();
  }

  onDiscountChange(event, index: number) {
    console.log('onDiscountChange -> ', event.srcElement.value, index);
    const data = this.getSpecificFormArray(index);
    data.patchValue({
      discount: event.srcElement.value || 0,
      subtotal: this.countSubTotal(
        data.get('quantity').value,
        data.get('price').value,
        event.srcElement.value,
        data.get('isGeneralDscntMethod').value,
        this.subTotalVat(
          data.get('price').value,
          data.get('vat').value,
          event.srcElement.value,
          data.get('isGeneralDscntMethod').value
        )
      ),
      subTotalVat:
        this.subTotalVat(
          data.get('price').value,
          data.get('vat').value,
          event.srcElement.value,
          data.get('isGeneralDscntMethod').value
        ) * data.get('quantity').value,
      vatAmount:
        this.vatAmount(
          data.get('price').value,
          event.srcElement.value,
          data.get('isGeneralDscntMethod').value,
          data.get('vat').value
        ) * data.get('quantity').value,
    });
    this.dataSource.next(this.itemRows.controls);
    this.formItems(this.itemRows);
    this.updateAllRelevantCounts();
  }

  getSpecificFormArray(index: number): AbstractControl {
    return (<UntypedFormArray>this.salesForm.get('items')).at(index);
  }

  get itemsArray(): UntypedFormArray {
    return this.salesForm.get('items') as UntypedFormArray;
  }

  openDeleteConfirmationDialog(itemCode: number, warehouseId: number, unitName: string) {
    const dialogConfig = new MatDialogConfig();
    dialogConfig.minWidth = '600px';
    dialogConfig.panelClass = 'green_theme';
    dialogConfig.hasBackdrop = true;
    dialogConfig.disableClose = false;
    dialogConfig.autoFocus = false;
    dialogConfig.direction = this.direction.value;
    const dialogRef = this.dialog.open(DeleteConfirmationComponent, dialogConfig);
    dialogRef.componentInstance.accepted.subscribe(result => {
      console.log('delete itemcode', result);
      const data: DeleteItemRows = { itemCode, warehouseId, unitName };
      this.removeSaleDetail(data);
    });
  }

  deleteEntry(id: string) {
    const dialogConfig = new MatDialogConfig();
    dialogConfig.minWidth = '600px';
    dialogConfig.panelClass = 'dark';
    dialogConfig.hasBackdrop = true;
    dialogConfig.disableClose = false;
    dialogConfig.autoFocus = false;
    dialogConfig.direction = this.direction.value;
    const dialogRef = this.dialog.open(DeleteConfirmationComponent, dialogConfig);
    dialogRef.componentInstance.accepted.subscribe(result => {
      console.log('delete quotations', result);
      const salesParams = new SalesParams();
      salesParams.transactionType = SaletransactionTypes.quotation;
      this.salesService.deleteSalesQuotation(this.salesId).subscribe(result => {
        console.log(result);
        if (result) {
          console.log('sales service response', result);
          //this.toastr.success('Quotation deleted successfully');
          this.commonService.playSuccessSound();
          this.router.navigate(['../../'], { relativeTo: this.route });
        }
      });
    });
  }

  jumpToNext(event: Event, index: number) {
    console.log('jump to next field  -> ', event, index);
    event.preventDefault();
    const nextField = document.querySelectorAll('.next');
    nextField.forEach((element, index) => {
      if (element === event.target) {
        const nextfield = nextField[index + 1] as HTMLInputElement;
        nextfield.focus();
      }
    });
  }

  onAddNewItem(product: ISalesItem) {
    console.log('onAddNewItem -> ', product);
    if (this.itemsArray && this.itemsArray?.length > 0) {
      this.itemsArray.insert(0, this.addnewFormGroup(product));
    } else {
      this.itemsArray.push(this.addnewFormGroup(product));
    }
  }

  addnewFormGroup(saleData: ISalesItem): UntypedFormGroup {
    console.log('addnewFormGroup -> ', saleData);
    const row = this.formBuilder.group({
      itemId: saleData.itemId,
      itemCode: saleData.itemCode,
      quantity: [
        saleData.quantity,
        Validators.compose([Validators.required, CustomValidators.gt(0)]),
      ],
      itemUnitId: saleData.itemUnitId,
      price: saleData.price,
      subtotal: [saleData.subtotal, Validators.compose([CustomValidators.gt(0)])],
      vat: saleData.vat,
      product: saleData.product,
      discount: [saleData.discount, this.discountValueValid],
      isGeneralDscntMethod: saleData.isGeneralDscntMethod,
      priceType: saleData.priceType,
      warehouseName: saleData.warehouseName,
      itemName: saleData.itemName,
      unitName: saleData.unitName,
      notes: saleData.notes,
      subTotalVat: saleData.subTotalVat,
      warehouseId: saleData.warehouseId,
      vatAmount: [saleData.vatAmount, Validators.compose([CustomValidators.gte(0)])],
      transactionItemId: saleData.transactionItemId,
      returnableQty: saleData.returnableQty,
    });
    return row;
  }

  submitSales(event: IActionEventType): void {
    event.event.preventDefault();
    if (event.actionType === ActionType.create) {
      this.salesForm.markAllAsTouched();
      this.processSalesCreation();
    }
    if (event.actionType === ActionType.edit) {
      this.salesForm.markAllAsTouched();
      this.processSalesCreation();
    }
    if (event.actionType === ActionType.post) {
      this.salesForm.markAllAsTouched();
      if (this.isFormValid()) {
        this.onPostClick();
      } else {
        this.commonService.playErrorSound();
      }
    }
    if (event.actionType === ActionType.delete) {
      this.deleteEntry(this.salesId);
    }
  }

  onPostClick() {
    const dialogConfig = new MatDialogConfig();
    dialogConfig.panelClass = 'green_theme';
    dialogConfig.minWidth = '600px';
    dialogConfig.hasBackdrop = true;
    dialogConfig.disableClose = false;
    dialogConfig.autoFocus = false;
    dialogConfig.direction = this.direction.value;
    const dialogRef = this.dialog.open(ConfirmDialogComponent, dialogConfig);

    dialogRef.afterClosed().subscribe(result => {
      console.log('result', result);
      if (result) {
        this.processSalesCreation();
      }
    });
  }

  isFormValid() {
    const isAllValid =
      (this.IsPostMode ? true : this.salesForm.valid) &&
      (this.IsPostMode ? this.paymentForm?.paymentFormIsAllValid() : true) &&
      this.customerSelection.customerSelectionFormIsAllValid() &&
      this.itemsArray.length > 0;
    return isAllValid;
  }

  processSalesCreation(zatcaAccept = false): void {
    console.log(
      this.salesForm,
      this.salesForm.valid,
      this.paymentForm?.paymentFormIsAllValid(),
      this.customerSelection.customerSelectionFormIsAllValid(),
      this.itemsArray
    );
    const isAllValid = this.isFormValid();
    console.log('is all valid', isAllValid);
    if (isAllValid) {
      const data: ISaleDetails = {
        ...this.salesForm.getRawValue(),
        ...this.customerSelection.customerSelectionForm.getRawValue(),
        payments: this.IsPostMode
          ? this.paymentForm.getPaymentsFormValue()
          : this.paymentDummyObject(),
        orderDate: convertDateForBE(this.salesForm.get('orderDate')?.value),
        referenceDocumentId: this.currentSalesData?.referenceDocumentId ?? null,
        provisionList: this.quotations.getTermsandCondtions(),
        isPosted: this.IsPostMode ? true : false,
        transactionType: SaletransactionTypes.quotation,
        issueDate: convertDateForBE(this.salesForm.get('issueDate')?.value),
        id: this.salesId ?? null,
        documentNumber: this.currentSalesData?.documentNumber ?? null,
      };
      console.log('Final Offers structure -> ', data);
      // check of mode an dmake calls accordingly
      if (this.isEditMode) {
        this.salesService.editSalesQuotation(data, this.salesId).subscribe(
          result => {
            console.log('sales service response', result);
            //this.toastr.success('Quotation edited successfully');
            this.commonService.playSuccessSound();
            this.router.navigate(['../../'], { relativeTo: this.route });
          },
          error => {
            console.log('errors =>', error);
            if (error.status === 422) {
              this.setFormErrors(error);
            }
          }
        );
      }

      if (this.IsCreateMode) {
        this.salesService.createSales(data).subscribe(
          (result: SalesCreateResponse) => {
            console.log('sales service response', result);
            //this.toastr.success(`${result?.documentNumber} added successfully`);
            this.showPrintDialog(result);
            this.commonService.playSuccessSound();
            this.resetPageForNewCreation();
            // this.router.navigate(['../../'], { relativeTo: this.route });
          },
          error => {
            console.log('errors =>', error);
            if (error.status === 422) {
              this.setFormErrors(error);
            }
          }
        );
      }

      if (this.IsPostMode) {
        const salesParams = new SalesIntegeratedParams();
        salesParams.areWarningsAccepted = zatcaAccept;
        salesParams.originatingSystem = SaletransactionTypes.quotation.toUpperCase();
        this.salesService.createSalesIntegrated(data, salesParams).subscribe(
          (result: SalesIntegratedCreateResponse) => {
            console.log('sales submit has zatca response', result);
            this.salesDebitResponse(result);
          },
          error => {
            this.loading = false;
            console.log('errors =>', error);
            if (error.status === 422) {
              this.setFormErrors(error);
            }
            this.zatcaErrorResponse(error);
          }
        );
      }
    } else {
      this.commonService.playErrorSound();
    }
  }

  zatcaErrorResponse(error) {
    if (
      error &&
      error?.details?.zatcaResponse &&
      (error?.details?.zatcaResponse !== null || error?.details?.zatcaResponse !== undefined)
    ) {
      this.openZatcaStatusModal(error?.details?.zatcaResponse);
    }
  }

  salesDebitResponse(result: SalesIntegratedCreateResponse) {
    if (result?.zatcaExceptionResponse) {
      console.log('sales submit has zatca response', result);
      this.openZatcaStatusModal(result.zatcaExceptionResponse);
    } else {
      console.log('sales service response', result);
      //this.toastr.success(`${result.documentNumber} created successfully`);
      this.commonService.playSuccessSound();
      this.router.navigate(['../../'], { relativeTo: this.route });
    }
  }

  openZatcaStatusModal(zatcaResponse: ZatcaExceptionResponse): void {
    const dialog = this.zatcaService.openZatcaStatusModal(zatcaResponse, this.direction.value);
    dialog.componentInstance.accepted.subscribe(() => {
      dialog.close();
      this.salesForm.markAllAsTouched();
      this.processSalesCreation(true);
    });
  }

  setFormErrors(validationErrors: any): void {
    const serverErrors = validationErrors.details.invalidQuantity;
    serverErrors.forEach((element: InvalidQuantity) => {
      const controlIndex = this.getControlsIndexFromArray(element);
      console.log('index => ', controlIndex);
      const formArray = this.getSpecificFormArray(controlIndex);
      console.log('formArray => ', formArray);
      formArray.get('quantity').setErrors({ serverSideError: element.error });
      formArray.updateValueAndValidity();
    });
  }

  customerTypeSelection(isExistingCustomer: boolean): void {
    if (!isExistingCustomer) {
      this.salesForm.get('distributorAccountId').setValue(null);
    }
  }

  resetPageForNewCreation(): void {
    this.resetSalesForm();
    this.customerSelection.resetForm();
    this.resetCounts();
    this.changeDetectorRef.detectChanges();
  }

  resetSalesForm(): void {
    this.salesForm.reset();
    this.itemRows.clear();
    this.salesForm?.get('issueDate').setValue(new Date());
    this.salesForm?.get('orderDate').setValue(new Date());
    this.dataSource = new BehaviorSubject<AbstractControl[]>([]);
    this.salesForm.markAsUntouched();
  }

  discountValueValid(c: AbstractControl): ValidationErrors | null {
    //     - Show ItemLineDiscount field on Grid
    // - Fill ItemLineDiscount field with Item data(% or $)
    // - Allow edit
    // - If $ then value entered should not exceed Price
    // - If % then value entered should not exceed 100

    if (c && c?.parent) {
      if (c.parent?.controls['isGeneralDscntMethod'].value) {
        // discount is % and should not be more than 100
        if (c.value > 100) {
          return { percentageError: true };
        }
      } else {
        //If $ then value entered should not exceed Price
        if (c.value > c.parent?.controls['price'].value) {
          return { priceError: true };
        }
      }
    }
  }

  customerProfileSelection(customer: ICustomer): void {
    console.log('customer profile selected ->', customer);
    if (customer) {
      this.patchCustomerProfileData(customer);
    }
  }

  patchCustomerProfileData(customer: ICustomer): void {
    this.salesForm.get('costCentreId').setValue(customer.costCentreId);
    this.salesForm.get('distributorAccountId').setValue(customer.distributorAccountId);
  }

  focusOnSearch(): void {
    this.productSearch.setFocus();
  }

  toggleExpansion(rowIndex: number): void {
    this.expandedRowIndex = this.expandedRowIndex === rowIndex ? null : rowIndex;
  }

  isRowExpanded(index: number): boolean {
    return this.expandedRowIndex === index;
  }

  paymentDummyObject(): IPaymentDetails {
    const payment: IPaymentDetails = {
      bankType: false,
      bankAccountId: null,
      bankAmount: null,
      cashType: true,
      cashAccountId: null,
      cashAmount: null,
      cardType: false,
      cardAccountId: null,
      cardAmount: null,
      creditType: false,
      creditDueDate: null,
      creditAmount: null,
      grandTotal: this.grandTotal,
      halala: false,
      fractionAmount: 0,
      totalExclVatDiscount: this.totalExcVatDisc,
      totalVat: this.totalVat,
      totalDiscount: this.discount,
      balanceAmount: 0,
      changeAmount: 0,
    };
    return payment;
  }

  /**
   * Shows the print dialog after successful sales creation
   */
  private showPrintDialog(result: SalesIntegratedCreateResponse): void {
    const dialogConfig = new MatDialogConfig();
    dialogConfig.minWidth = '400px';
    dialogConfig.maxWidth = '500px';
    dialogConfig.panelClass = 'green_theme';
    dialogConfig.hasBackdrop = true;
    dialogConfig.disableClose = false;
    dialogConfig.autoFocus = true;
    dialogConfig.direction = this.direction.value;

    const dialogData: PrintDialogData = {
      documentResponse: result,
      title: 'Sales Document Created Successfully',
      message: `Your sales document has been created successfully!`,
    };

    dialogConfig.data = dialogData;

    const dialogRef = this.dialog.open(PrintDialogComponent, dialogConfig);

    dialogRef.componentInstance.printRequested.subscribe((shouldPrint: boolean) => {
      if (shouldPrint) {
        this.handlePrintRequest(result);
      }
    });

    dialogRef.afterClosed().subscribe((shouldPrint: boolean | null) => {
      if (shouldPrint === null) {
        // User clicked cancel, just reset the page
        this.resetPageForNewCreation();
      }
    });
  }

  /**
   * Handles the print request - opens Invoice Demo component in new window using common service
   */
  private handlePrintRequest(result: SalesIntegratedCreateResponse): void {
    console.log('Print requested for document:', result);
    console.log('Document ID:', result.documentId);
    console.log('Document Number:', result.documentNumber);

    if (result.documentId) {
      console.log('Opening Invoice Demo in new window with params:', {
        documentId: result.documentId,
        transactionType: result.documentType || 'SALES',
      });

      // Use the common service to open invoice in new window
      this.commonService.openInvoiceInNewWindow(result.documentId, 'quotation');
    } else {
      console.warn('No document ID available for printing');
    }
  }
}
