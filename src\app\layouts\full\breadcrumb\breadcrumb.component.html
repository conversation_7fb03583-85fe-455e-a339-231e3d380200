<div class="p-b-12 overflow-hidden" *ngIf="pageInfo?.['urls']?.length">
  <div class="breadcrumb-container p-3 rounded shadow-sm">
    <div>
      <div class="row">
        <div class="col-lg-12 col-sm-12">
          <!-- <h4 class="page-title m-0 f-s-18 f-w-500">
            {{ pageInfo?.['title'] | translate }}
          </h4> -->
          <div
            class="d-flex align-items-center overflow-hidden justify-content-start list-style-none">
            <ng-template [ngForOf]="pageInfo?.['urls']" ngFor let-url let-last="last">
              <li class="breadcrumb-item" [routerLink]="url.url">
                <a class="text-primary" [routerLink]="url.url">{{ url.title | translate }}</a>
              </li>
              <li class="breadcrumb-item active" *ngIf="last">
                {{  pageInfo?.['title'] | translate }}
              </li>
            </ng-template>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
