import { Injectable } from '@angular/core';
import { ChartOfAccountsApiService } from 'src/app/core/api/accounts/chart-of-accounts-api.service';
import { Account, ConfiguredAccount } from '../models/account';
import { Observable } from 'rxjs';
import { map, tap } from 'rxjs/operators';
import { HttpParams } from '@angular/common/http';
import { AccountParams } from '../models/accountParams';
import { SetupRequest } from '../components/account-setup/account-setup.payload';

@Injectable({
  providedIn: 'root',
})
export class ChartOfAccountsService {
  constructor(private chartOfAccountsApiService: ChartOfAccountsApiService) {}

  getAllChartOfAccounts(accountParams: AccountParams): Observable<any> {
    let params = new HttpParams();
    if (accountParams.accountCategory) {
      params = params.append('accountCategory', accountParams.accountCategory);
    }
    if (accountParams.accountSubCategory) {
      params = params.append('accountSubCategory', accountParams.accountSubCategory);
    }
    if (accountParams.accountType) {
      params = params.append('accountType', accountParams.accountType);
    }
    if (accountParams.isContraAccount) {
      params = params.append('isContraAccount', accountParams.isContraAccount);
    }
    if (accountParams.isDepreciating) {
      params = params.append('isDepreciating', accountParams.isDepreciating);
    }
    if (accountParams.searchString) {
      params = params.append('searchString', accountParams.searchString);
    }
    if (accountParams.searchString && accountParams.searchType) {
      if (accountParams.searchType == 'accountNumber') {
        accountParams.searchType = 'parentAccountNumber';
      }
      params = params.append('searchType', accountParams.searchType);
    }
    if (accountParams.pageNumber) {
      params = params.append('pageNumber', accountParams.pageNumber.toString());
    }
    if (accountParams.pageSize) {
      params = params.append('pageSize', accountParams.pageSize);
    }
    return this.chartOfAccountsApiService
      .getAllChartOfAccounts(params)
      .pipe(map((response: any) => response));
  }

  getChartOfAccountById(accountId: string): Observable<Account> {
    return this.chartOfAccountsApiService
      .getChartOfAccountById(accountId)
      .pipe(map((response: Account) => response));
  }

  getChartOfAccountsByAccountCategoryAndAccountSubCategory(
    accountCategory: string,
    accountSubCategory: string
  ): Observable<Account[]> {
    return this.chartOfAccountsApiService
      .getChartOfAccountsByAccountCategoryAndAccountSubCategory(accountCategory, accountSubCategory)
      .pipe(map((response: Account[]) => response));
  }

  createChartOfAccounts(chartOfAccounts: any) {
    return this.chartOfAccountsApiService
      .createChartOfAccounts(chartOfAccounts)
      .pipe(map((response: any) => response));
  }

  updateChartOfAccounts(accountId: string, chartOfAccounts: any) {
    return this.chartOfAccountsApiService
      .updateChartOfAccounts(accountId, chartOfAccounts)
      .pipe(map((response: any) => response));
  }

  getAccountStatementReport(params: HttpParams) {
    let reportType;
    if (params.get('type')) {
      reportType = 'application/' + params.get('type');
    } else {
      reportType = 'application/pdf';
    }

    return this.chartOfAccountsApiService.getAccountStatementReport(params).pipe(
      tap((result: ArrayBuffer) => {
        const file = new Blob([result], { type: reportType });
        const fileURL = URL.createObjectURL(file);
        window.open(fileURL);
      })
    );
  }

  getAccountListReport(params: HttpParams) {
    let reportType;
    if (params.get('type')) {
      reportType = 'application/' + params.get('type');
    } else {
      reportType = 'application/pdf';
    }

    return this.chartOfAccountsApiService.getAccountListReport(params).pipe(
      tap((result: ArrayBuffer) => {
        const file = new Blob([result], { type: reportType });
        const fileURL = URL.createObjectURL(file);
        window.open(fileURL);
      })
    );
  }

  setUpAccounts(setupRequest: ConfiguredAccount) {
    return this.chartOfAccountsApiService
      .setUpAccounts(setupRequest)
      .pipe(map((response: any) => response));
  }

  getAllConfiguredAccounts() {
    return this.chartOfAccountsApiService
      .getAllConfiguredAccounts()
      .pipe(map((response: any) => response));
  }
}
