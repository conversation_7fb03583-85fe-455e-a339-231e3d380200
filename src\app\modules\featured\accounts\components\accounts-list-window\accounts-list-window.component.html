<app-dialog-header></app-dialog-header>
<mat-card appearance="outlined">
  <ng-container *ngIf="!isLoading">
    <form
      [formGroup]="searchBoxForm"
      (ngSubmit)="getFilterData($event); (false)"
      autocomplete="off">
      <div class="row no-gutters">
        <div class="p-2 col-lg-4 col-md-4 col-sm-4">
          <mat-form-field class="w-100" appearance="outline">
            <input
              #searchInput
              matInput
              type="text"
              autocomplete="off"
              formControlName="searchString"
              placeholder="{{ searchPlaceholder }}" />
            <button
              (click)="getAccounts($event); (false)"
              matSuffix
              mat-icon-button
              aria-label="Submit">
              <mat-icon>search</mat-icon>
            </button>
            <button
              *ngIf="searchInput?.value"
              (click)="clearFilters($event); (false)"
              matSuffix
              mat-icon-button
              aria-label="Clear">
              <mat-icon>close</mat-icon>
            </button>
          </mat-form-field>
        </div>
        <div class="p-2 col-lg-4 col-md-4 col-sm-4">
          <mat-label>{{ 'caccounts.accountCategory' | translate }}</mat-label>

          <mat-form-field class="w-100" appearance="outline">
            <mat-select placeholder="Account Group" formControlName="accountCategory">
              <mat-option *ngFor="let group of accountCategorys" [value]="group.value">
                {{ group.display }}
              </mat-option>
            </mat-select>
          </mat-form-field>
        </div>
        <div class="p-2 col-lg-4 col-md-4 col-sm-4">
          <mat-label> {{ 'caccounts.subCategory' | translate }}</mat-label>

          <mat-form-field class="w-100" appearance="outline">
            <mat-select placeholder="Sub Category" formControlName="accountSubCategory">
              <mat-option *ngFor="let bGroup of accountSubCategorys" [value]="bGroup.value">
                {{ bGroup.display }}
              </mat-option>
            </mat-select>
          </mat-form-field>
        </div>
        <div class="p-2 col-lg-4 col-md-4 col-sm-4">
          <mat-label>{{ 'caccounts.accountType' | translate }}</mat-label>

          <mat-form-field class="w-100" appearance="outline">
            <mat-select placeholder="Account Type" formControlName="accountType">
              <mat-option *ngFor="let actType of accountTypes" [value]="actType.value">
                {{ actType.display }}
              </mat-option>
            </mat-select>
          </mat-form-field>
        </div>
      </div>
    </form>

    <div class="responsive-table">
      <table class="w-100" [dataSource]="dataSource" mat-table matSort>
        <ng-container matColumnDef="accountNumber">
          <th
            *matHeaderCellDef
            (click)="resetSearchType('accountNumber')"
            mat-header-cell
            mat-sort-header>
            {{ 'caccounts.accountNo' | translate }}
          </th>
          <td *matCellDef="let element" (click)="accountSelected(element)" mat-cell>
            {{ element.accountNumber }}
          </td>
        </ng-container>
        <ng-container matColumnDef="nameArabic">
          <th
            *matHeaderCellDef
            (click)="resetSearchType('nameArabic')"
            mat-header-cell
            mat-sort-header>
            {{ 'caccounts.accountArabicName' | translate }}
          </th>
          <td *matCellDef="let element" (click)="accountSelected(element)" mat-cell>
            {{ getAccountName(element) }}
          </td>
        </ng-container>
        <ng-container matColumnDef="accountType">
          <th
            *matHeaderCellDef
            (click)="resetSearchType('accountType')"
            mat-header-cell
            mat-sort-header>
            {{ 'caccounts.accountType' | translate }}
          </th>
          <td *matCellDef="let element" (click)="accountSelected(element)" mat-cell>
            {{ element.accountType }}
          </td>
        </ng-container>
        <ng-container matColumnDef="accountCategory">
          <th
            *matHeaderCellDef
            (click)="resetSearchType('accountCategory')"
            mat-header-cell
            mat-sort-header>
            {{ 'caccounts.accountCategory' | translate }}
          </th>
          <td *matCellDef="let element" (click)="accountSelected(element)" mat-cell>
            {{ element.accountCategory }}
          </td>
        </ng-container>
        <ng-container matColumnDef="accountSubCategory">
          <th
            *matHeaderCellDef
            (click)="resetSearchType('accountSubCategory')"
            mat-header-cell
            mat-sort-header>
            {{ 'caccounts.subCategory' | translate }}
          </th>
          <td *matCellDef="let element" (click)="accountSelected(element)" mat-cell>
            {{ element.accountSubCategory }}
          </td>
        </ng-container>
        <tr class="mat-row" *matNoDataRow>
          <td class="text-center text-info" [attr.colspan]="displayedColumns.length">
            {{ 'common.noDataFound' | translate }}
          </td>
        </tr>
        <tr *matHeaderRowDef="displayedColumns" mat-header-row></tr>
        <tr *matRowDef="let row; columns: displayedColumns" mat-row></tr>
      </table>
    </div>
  </ng-container>
</mat-card>
