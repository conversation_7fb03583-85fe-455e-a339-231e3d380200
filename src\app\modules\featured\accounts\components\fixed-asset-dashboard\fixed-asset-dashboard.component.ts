import { Component, OnInit } from '@angular/core';
import { DashboardModulesHolder } from 'src/app/modules/shared/components/dashboard-modules-holder/modulesHolder';

@Component({
  selector: 'app-fixed-asset-dashboard',
  templateUrl: './fixed-asset-dashboard.component.html',
  styleUrls: ['./fixed-asset-dashboard.component.scss'],
})
export class FixedAssetDashboardComponent {
  productModulesList: DashboardModulesHolder[] = [
    {
      moduleName: 'navigationMenus.depreciation',
      moduleDescription: 'Manage Depreciation.',
      modulePermission: ['ChartOfAccounts', 'AllPermissions'],
      moduleRouterLink: '../depreciation',
      moduleType: 'subModule',
    },
  ];
}
