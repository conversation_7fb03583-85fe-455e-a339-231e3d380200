.module-card {
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;

  &.main-module {
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);

    &::before {
      content: '';
      position: absolute;
      top: 0;
      right: 0;
      width: 10px; // Adjusted to full height bar
      height: 100%;
      background: rgba(255, 255, 255, 0.25);
    }
  }

  &.sub-module {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  }

  mat-card-title {
    margin: 16px !important;

    p {
      margin: 0;
      font-size: 17px;
    }
  }

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
  }
}

.custom-card {
  position: relative;
}
