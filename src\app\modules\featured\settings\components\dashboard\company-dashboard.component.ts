import { Component, OnInit } from '@angular/core';
import { DashboardModulesHolder } from 'src/app/modules/shared/components/dashboard-modules-holder/modulesHolder';

@Component({
  selector: 'app-company-dashboard',
  templateUrl: './company-dashboard.component.html',
  styleUrls: ['./company-dashboard.component.scss'],
})
export class CompanyDashboardComponent implements OnInit {
  productModulesList: DashboardModulesHolder[] = [
    {
      moduleName: 'navigationMenus.companyProfile',
      moduleDescription: 'Manage Company Profile.',
      modulePermission: ['Company.Update', 'AllPermissions'],
      moduleRouterLink: '../company',
      moduleType: 'subModule',
    },
    {
      moduleName: 'navigationMenus.branchSetup',
      moduleDescription: 'Manage Branches.',
      modulePermission: ['Branch', 'AllPermissions'],
      moduleRouterLink: '../branches',
      moduleType: 'subModule',
    },
    {
      moduleName: 'navigationMenus.wareHouseSetup',
      moduleDescription: 'Manage Warehouse.',
      modulePermission: ['WareHouse', 'AllPermissions'],
      moduleRouterLink: '../warehouses',
      moduleType: 'subModule',
    },
  ];
  constructor() {
    // Constructor can be used for dependency injection if needed
  }

  ngOnInit(): void {
    // Initialization logic can be added here if needed
  }
}
