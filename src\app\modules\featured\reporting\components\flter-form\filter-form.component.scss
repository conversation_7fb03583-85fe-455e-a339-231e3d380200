// Enhanced responsive form layout styles
.filter-form-container {
  // Remove card padding for more space
  ::ng-deep .mat-card {
    padding: 0.75rem;
  }

  // Form section title - more compact
  .form-section-title {
    font-size: 1rem;
    font-weight: 500;
    margin-bottom: 0.75rem;
    color: rgba(0, 0, 0, 0.87);
    border-bottom: 1px solid rgba(0, 0, 0, 0.12);
    padding-bottom: 0.25rem;
  }

  .filter-form-row {
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;
    align-items: flex-start;
    width: 100%; // Ensure full width usage

    // Responsive gap adjustments
    @media (max-width: 767px) {
      gap: 0.5rem; // Smaller gap on mobile
      flex-direction: column; // Stack vertically on mobile if needed

      // Override column stacking for specific cases
      &.keep-row {
        flex-direction: row;
      }
    }

    @media (min-width: 768px) {
      gap: 0.75rem;
    }

    @media (min-width: 1200px) {
      gap: 1rem; // More space on large screens
    }
  }

  // ===== RESPONSIVE FLEXBOX-BASED CONTROL CLASSES =====

  // Base form control wrapper - flex-enabled
  .form-control-wrapper {
    display: flex;
    flex-direction: column;
    min-width: 0; // Prevent flex overflow
    margin-bottom: 0;

    // Ensure all child form fields are flex-aware
    .mat-form-field {
      width: 100%; // Take full width of flex container
      flex: 1; // Allow growth within container
    }
  }

  // Responsive flex-based size classes
  .small-control {
    flex: 1 1 auto; // grow, shrink, auto basis
    min-width: 120px;

    // Mobile: take more space if needed
    @media (max-width: 767px) {
      flex: 1 1 100%;
      min-width: 100%;
    }

    // Tablet and up: constrained but flexible
    @media (min-width: 768px) {
      max-width: 180px;
    }
  }

  .medium-control {
    flex: 1.5 1 auto; // prefer more space
    min-width: 160px;

    // Mobile: full width
    @media (max-width: 767px) {
      flex: 1 1 100%;
      min-width: 100%;
    }

    // Tablet and up: flexible with constraints
    @media (min-width: 768px) {
      max-width: 240px;
    }
  }

  .big-control {
    flex: 2 1 auto; // prefer even more space
    min-width: 200px;

    // Mobile: full width
    @media (max-width: 767px) {
      flex: 1 1 100%;
      min-width: 100%;
    }

    // Tablet: take more space
    @media (min-width: 768px) {
      flex: 2 1 auto;
      max-width: 320px;
    }

    // Desktop: can grow more
    @media (min-width: 1024px) {
      max-width: 400px;
    }
  }

  .full-control {
    flex: 3 1 auto; // take maximum available space
    min-width: 280px;

    // Mobile: full width
    @media (max-width: 767px) {
      flex: 1 1 100%;
      min-width: 100%;
    }

    // Tablet and up: grow to fill available space
    @media (min-width: 768px) {
      flex: 3 1 auto;
    }

    // Large screens: can take even more space
    @media (min-width: 1200px) {
      flex: 4 1 auto;
    }
  }

  .auto-control {
    flex: 0 1 auto; // don't grow, but can shrink
    width: auto;
    min-width: fit-content;

    // Mobile: still responsive
    @media (max-width: 767px) {
      flex: 1 1 100%;
      width: 100%;
    }
  }

  // Enhanced form field styling - flex-aware and responsive
  .mat-form-field {
    width: 100%; // Always take full width of flex container
    margin-bottom: 0;
    flex: 1; // Allow growth within flex container
    min-width: 0; // Prevent overflow in flex containers

    // Ensure form field adapts to container
    &.w-100 {
      width: 100%;
      flex: 1 1 100%;
    }

    // Responsive Material form field adjustments
    ::ng-deep {
      .mat-form-field-wrapper {
        margin-bottom: 0;
        padding-bottom: 0;
        width: 100%; // Ensure wrapper takes full width
      }

      .mat-form-field-flex {
        width: 100%; // Ensure flex container is full width
      }

      .mat-form-field-infix {
        padding: 0.5rem 0;
        width: 100%; // Input takes full width

        // Ensure input elements are responsive
        input,
        select,
        textarea {
          width: 100%;
          min-width: 0; // Prevent overflow
        }
      }

      .mat-form-field-subscript-wrapper {
        margin-top: 0.25rem;
        width: 100%; // Error messages take full width
      }

      // Responsive adjustments for different screen sizes
      @media (max-width: 767px) {
        .mat-form-field-infix {
          padding: 0.75rem 0; // Slightly more padding on mobile
        }
      }
    }
  }

  // Label styling - more compact
  .form-label {
    font-weight: 500;
    margin-bottom: 0.25rem; // Reduced from 0.5rem
    color: rgba(0, 0, 0, 0.87);
    font-size: 0.875rem;
    display: block;
    line-height: 1.2; // Tighter line height
  }

  // Action buttons container - more compact
  .form-actions {
    display: flex;
    justify-content: center;
    gap: 0.75rem; // Reduced gap
    margin-top: 1rem; // Reduced from 1.5rem
    flex-wrap: wrap;
    padding-top: 0.5rem; // Small top padding

    .mat-button {
      min-width: 100px; // Reduced from 120px
      padding: 0.375rem 1rem; // More compact button padding
    }
  }

  // Report type selector specific styling - compact
  .report-type-selector {
    margin-bottom: 0.5rem; // Reduced spacing between sections

    .filter-form-row {
      gap: 0.5rem; // Tighter gap for report selector

      @media (min-width: 768px) {
        grid-template-columns: 2fr 2fr 1fr;
        align-items: end;
      }

      @media (min-width: 1024px) {
        grid-template-columns: 1fr 1fr auto;
      }
    }

    .reset-button-container {
      display: flex;
      align-items: flex-end;
      justify-content: flex-start;

      @media (min-width: 768px) {
        justify-content: flex-end;
      }
    }
  }
}

// Utility classes for specific adjustments - more compact
.responsive-grid {
  display: grid;
  gap: 0.75rem; // Reduced from 1rem

  &.auto-fit {
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr)); // Reduced min width
  }

  &.auto-fill {
    grid-template-columns: repeat(auto-fill, minmax(160px, 1fr)); // Reduced min width
  }
}

// Compact spacing utilities
.margin-top {
  margin-top: 0.75rem; // Reduced from 1rem

  &.large {
    margin-top: 1.25rem; // Reduced from 2rem
  }

  &.small {
    margin-top: 0.5rem; // New small option
  }
}

.margin-bottom {
  margin-bottom: 0.75rem; // Reduced from 1rem

  &.large {
    margin-bottom: 1.25rem; // Reduced from 2rem
  }

  &.small {
    margin-bottom: 0.5rem; // New small option
  }
}

// ===== COMPONENT-SPECIFIC FLEX STYLING =====

// Product search - flex-enabled
.product-search-container {
  display: flex;
  flex: 1;

  .app-product-search-selection {
    width: 100%;
    flex: 1;

    ::ng-deep {
      .mat-form-field {
        width: 100%;
        flex: 1;
        margin-bottom: 0;

        .mat-form-field-wrapper {
          margin-bottom: 0;
          padding-bottom: 0;
          width: 100%;
        }

        .mat-form-field-infix {
          width: 100%;
        }
      }
    }
  }
}

// Date range - flex-enabled
.date-range-container {
  display: flex;
  flex: 1;

  .mat-date-range-input {
    width: 100%;
    flex: 1;
  }

  ::ng-deep {
    .mat-form-field {
      flex: 1;
      width: 100%;
    }

    .mat-form-field-wrapper {
      margin-bottom: 0;
      padding-bottom: 0;
      width: 100%;
    }

    .mat-date-range-input-container {
      width: 100%;

      input {
        width: 100%;
        min-width: 0;
      }
    }
  }
}

// Account search - flex-enabled
.account-search-container {
  display: flex;
  flex: 1;

  .app-accounts-prosearch-box {
    width: 100%;
    flex: 1;

    ::ng-deep {
      .mat-form-field {
        width: 100%;
        flex: 1;
        margin-bottom: 0;

        .mat-form-field-wrapper {
          margin-bottom: 0;
          padding-bottom: 0;
          width: 100%;
        }

        .mat-form-field-infix {
          width: 100%;

          input {
            width: 100%;
            min-width: 0;
          }
        }
      }
    }
  }
}

// Multi-select dropdown - flex-enabled
.multi-select-container {
  display: flex;
  flex: 1;

  ::ng-deep {
    .mat-select {
      width: 100%;
      flex: 1;
    }

    .mat-form-field {
      width: 100%;
      flex: 1;
    }
  }
}

// Standard dropdown - flex-enabled
.dropdown-container {
  display: flex;
  flex: 1;

  ::ng-deep {
    .mat-select {
      width: 100%;
      flex: 1;
    }
  }
}

// ===== UTILITY CLASSES FOR SPECIAL CASES =====

// Force full width on any device
.force-full-width {
  flex: 1 1 100% !important;
  width: 100% !important;
  min-width: 100% !important;
  max-width: none !important;
}

// Prevent shrinking (useful for buttons, icons)
.no-shrink {
  flex-shrink: 0 !important;
}

// Allow unlimited growth
.grow-unlimited {
  flex-grow: 999 !important;
  max-width: none !important;
}

// Compact control (smaller than small)
.compact-control {
  flex: 0.5 1 auto;
  min-width: 80px;
  max-width: 120px;

  @media (max-width: 767px) {
    flex: 1 1 100%;
    min-width: 100%;
    max-width: none;
  }
}

// Extra wide control (bigger than full)
.extra-wide-control {
  flex: 4 1 auto;
  min-width: 320px;

  @media (max-width: 767px) {
    flex: 1 1 100%;
    min-width: 100%;
  }

  @media (min-width: 1200px) {
    flex: 5 1 auto;
    min-width: 400px;
  }
}

// Responsive utilities
.responsive-helper {
  // Hide on mobile
  &.hide-mobile {
    @media (max-width: 767px) {
      display: none !important;
    }
  }

  // Hide on tablet
  &.hide-tablet {
    @media (min-width: 768px) and (max-width: 1023px) {
      display: none !important;
    }
  }

  // Hide on desktop
  &.hide-desktop {
    @media (min-width: 1024px) {
      display: none !important;
    }
  }
}

// Animation support for responsive changes
.filter-form-row {
  transition: grid-template-columns 0.3s ease;
}

.form-control-wrapper {
  transition: grid-column 0.3s ease;
}

// Global Material form field spacing override for compact design
.filter-form-container {
  ::ng-deep {
    // Remove default Material spacing globally
    .mat-form-field {
      line-height: 1.2;

      .mat-form-field-flex {
        padding-top: 0.25rem;
      }

      .mat-form-field-label {
        top: 1.2rem; // Adjust label position for compact padding
      }

      .mat-form-field-underline {
        bottom: 0.25rem; // Adjust underline position
      }

      .mat-form-field-suffix {
        top: 0.25rem; // Adjust suffix icons
      }
    }

    // Compact Material buttons
    .mat-button,
    .mat-flat-button,
    .mat-stroked-button {
      line-height: 1.2;
      padding: 0.375rem 0.75rem;
      min-height: 32px; // Reduced from default 36px
    }

    // Compact Material select
    .mat-select {
      line-height: 1.2;
    }

    // Compact Material datepicker
    .mat-datepicker-toggle {
      padding: 0.25rem;
    }
  }
}
