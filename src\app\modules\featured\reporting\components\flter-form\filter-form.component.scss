// ===== CLEAN & SCALABLE FILTER FORM STYLES =====

.filter-form-container {
  padding: 1rem;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 1rem;

  // ===== FLEX LAYOUT (SIMPLE & RELIABLE) =====
  .filter-form-row {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    align-items: stretch;
  }

  // ===== BASE WRAPPER =====
  .form-control-wrapper {
    display: flex;
    flex-direction: column;
    min-width: 0;
  }

  // ===== BACKEND-DRIVEN SIZE CLASSES =====
  .small-control {
    flex: 0 0 200px;
  }

  .medium-control {
    flex: 0 0 250px;
  }

  .big-control {
    flex: 1 1 300px;
  }

  .full-control {
    flex: 2 1 400px;
  }

  .auto-control {
    flex: 0 0 auto;
  }

  // ===== FORM FIELD BASICS =====
  .mat-form-field {
    width: 100%;
    margin-bottom: 0;
  }

  // ===== FORM ACTIONS =====
  .form-actions {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-top: 1.5rem;
    flex-wrap: wrap;
  }

  // ===== REPORT TYPE SELECTOR =====
  .report-type-selector {
    margin-bottom: 1rem;
  }
}
