// Enhanced responsive form layout styles
.filter-form-container {
  // Form section title - more compact
  .form-section-title {
    font-size: 1rem;
    font-weight: 500;
    margin-bottom: 0.75rem;
    color: rgba(0, 0, 0, 0.87);
    border-bottom: 1px solid rgba(0, 0, 0, 0.12);
    padding-bottom: 0.25rem;
  }

  .filter-form-row {
    display: grid;
    gap: 0.75rem; // Reduced from 1rem
    padding: 0.5rem 0; // Reduced padding significantly

    // Responsive grid configuration
    // Mobile-first approach
    grid-template-columns: 1fr;

    // Tablet and up
    @media (min-width: 768px) {
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }

    // Desktop and up
    @media (min-width: 1024px) {
      grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    }

    // Large desktop
    @media (min-width: 1440px) {
      grid-template-columns: repeat(6, 1fr);
    }
  }

  // Control type specific sizing - more compact
  .form-control-wrapper {
    display: flex;
    flex-direction: column;
    min-width: 0; // Prevent overflow
    margin-bottom: 0; // Remove any default margins

    &.control-small {
      // Standard controls (dropdown, date)
      grid-column: span 1;
    }

    &.control-medium {
      // Multi-select and date range
      grid-column: span 1;

      @media (min-width: 1024px) {
        grid-column: span 1;
      }
    }

    &.control-large {
      // Input controls (search, text input)
      grid-column: span 1;

      @media (min-width: 768px) {
        grid-column: span 2;
      }

      @media (min-width: 1024px) {
        grid-column: span 2;
      }

      @media (min-width: 1440px) {
        grid-column: span 2;
      }
    }

    &.control-full {
      // Account search and special controls
      grid-column: span 1;

      @media (min-width: 768px) {
        grid-column: span 2;
      }

      @media (min-width: 1024px) {
        grid-column: span 3;
      }
    }
  }

  // Form field enhancements - compact styling
  .mat-form-field {
    width: 100%;
    margin-bottom: 0; // Remove default Material margins

    &.w-100 {
      width: 100%;
    }

    // Reduce Material form field spacing
    ::ng-deep {
      .mat-form-field-wrapper {
        margin-bottom: 0;
        padding-bottom: 0;
      }

      .mat-form-field-subscript-wrapper {
        margin-top: 0.25rem; // Reduced from default
      }

      .mat-form-field-infix {
        padding: 0.5rem 0; // More compact padding
      }
    }
  }

  // Label styling - more compact
  .form-label {
    font-weight: 500;
    margin-bottom: 0.25rem; // Reduced from 0.5rem
    color: rgba(0, 0, 0, 0.87);
    font-size: 0.875rem;
    display: block;
    line-height: 1.2; // Tighter line height
  }

  // Action buttons container - more compact
  .form-actions {
    display: flex;
    justify-content: center;
    gap: 0.75rem; // Reduced gap
    margin-top: 1rem; // Reduced from 1.5rem
    flex-wrap: wrap;
    padding-top: 0.5rem; // Small top padding

    .mat-button {
      min-width: 100px; // Reduced from 120px
      padding: 0.375rem 1rem; // More compact button padding
    }
  }

  // Report type selector specific styling - compact
  .report-type-selector {
    margin-bottom: 0.5rem; // Reduced spacing between sections

    .filter-form-row {
      gap: 0.5rem; // Tighter gap for report selector

      @media (min-width: 768px) {
        grid-template-columns: 2fr 2fr 1fr;
        align-items: end;
      }

      @media (min-width: 1024px) {
        grid-template-columns: 1fr 1fr auto;
      }
    }

    .reset-button-container {
      display: flex;
      align-items: flex-end;
      justify-content: flex-start;

      @media (min-width: 768px) {
        justify-content: flex-end;
      }
    }
  }
}

// Utility classes for specific adjustments - more compact
.responsive-grid {
  display: grid;
  gap: 0.75rem; // Reduced from 1rem

  &.auto-fit {
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr)); // Reduced min width
  }

  &.auto-fill {
    grid-template-columns: repeat(auto-fill, minmax(160px, 1fr)); // Reduced min width
  }
}

// Compact spacing utilities
.margin-top {
  margin-top: 0.75rem; // Reduced from 1rem

  &.large {
    margin-top: 1.25rem; // Reduced from 2rem
  }

  &.small {
    margin-top: 0.5rem; // New small option
  }
}

.margin-bottom {
  margin-bottom: 0.75rem; // Reduced from 1rem

  &.large {
    margin-bottom: 1.25rem; // Reduced from 2rem
  }

  &.small {
    margin-bottom: 0.5rem; // New small option
  }
}

// Product search specific styling - compact
.product-search-container {
  .app-product-search-selection {
    width: 100%;

    ::ng-deep {
      .mat-form-field {
        width: 100%;
        margin-bottom: 0;

        .mat-form-field-wrapper {
          margin-bottom: 0;
          padding-bottom: 0;
        }
      }
    }
  }
}

// Date range specific styling - compact
.date-range-container {
  .mat-date-range-input {
    width: 100%;
  }

  ::ng-deep {
    .mat-form-field-wrapper {
      margin-bottom: 0;
      padding-bottom: 0;
    }
  }
}

// Account search specific styling - compact
.account-search-container {
  .app-accounts-prosearch-box {
    width: 100%;

    ::ng-deep {
      .mat-form-field {
        width: 100%;
        margin-bottom: 0;

        .mat-form-field-wrapper {
          margin-bottom: 0;
          padding-bottom: 0;
        }
      }
    }
  }
}

// Responsive utilities
.responsive-helper {
  // Hide on mobile
  &.hide-mobile {
    @media (max-width: 767px) {
      display: none !important;
    }
  }

  // Hide on tablet
  &.hide-tablet {
    @media (min-width: 768px) and (max-width: 1023px) {
      display: none !important;
    }
  }

  // Hide on desktop
  &.hide-desktop {
    @media (min-width: 1024px) {
      display: none !important;
    }
  }
}

// Animation support for responsive changes
.filter-form-row {
  transition: grid-template-columns 0.3s ease;
}

.form-control-wrapper {
  transition: grid-column 0.3s ease;
}

// Global Material form field spacing override for compact design
.filter-form-container {
  ::ng-deep {
    // Remove default Material spacing globally
    .mat-form-field {
      line-height: 1.2;

      .mat-form-field-flex {
        padding-top: 0.25rem;
      }

      .mat-form-field-label {
        top: 1.2rem; // Adjust label position for compact padding
      }

      .mat-form-field-underline {
        bottom: 0.25rem; // Adjust underline position
      }

      .mat-form-field-suffix {
        top: 0.25rem; // Adjust suffix icons
      }
    }

    // Compact Material buttons
    .mat-button,
    .mat-flat-button,
    .mat-stroked-button {
      line-height: 1.2;
      padding: 0.375rem 0.75rem;
      min-height: 32px; // Reduced from default 36px
    }

    // Compact Material select
    .mat-select {
      line-height: 1.2;
    }

    // Compact Material datepicker
    .mat-datepicker-toggle {
      padding: 0.25rem;
    }
  }
}
