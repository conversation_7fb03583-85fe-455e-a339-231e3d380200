.row.no-gutters {
  display: flex;
  flex-wrap: nowrap;
  align-items: center;
  gap: 10px;
  width: 100%;
  overflow-x: auto;
}

.filter-field {
  flex: 1;
  min-width: 150px;
  box-sizing: border-box;
  margin-top: 5px;
  margin-bottom: 5px;
}

.mat-form-field {
  width: 100%;
}

::ng-deep .custom-dropdown-panel {
  min-width: 300px;
}

:host ::ng-deep .mat-mdc-form-field-subscript-wrapper {
  display: none !important;
}

.submit {
  height: 30px;
  background-color: transparent;
  border: 1px solid grey;
  border-radius: 5px;
  cursor: pointer;
}

.report-type-selector {
  margin-bottom: 20px;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.report-type-selector mat-form-field {
  max-width: 400px;
}

.radio-group-label {
  display: block;
  margin-bottom: 8px;
  font-size: 14px;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.6);
}

.mat-radio-group {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

.mat-checkbox {
  margin-bottom: 8px;
}

.margin-bottom {
  margin-bottom: 16px;
}
