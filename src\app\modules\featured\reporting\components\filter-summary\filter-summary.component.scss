// Filter Summary Component Styles
.filter-summary-container {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  margin: 0.75rem 0;
  transition: all 0.3s ease;
  overflow: hidden;

  &.has-filters {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  // Header Section
  .filter-summary-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.75rem 1rem;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 1px solid #dee2e6;

    .header-content {
      display: flex;
      align-items: center;
      gap: 0.5rem;

      .summary-icon {
        color: #6c757d;
        font-size: 1.2rem;
      }

      .summary-title {
        font-weight: 500;
        color: #495057;
        font-size: 0.95rem;

        .filter-count {
          color: #6c757d;
          font-weight: 400;
        }
      }
    }

    .header-actions {
      display: flex;
      align-items: center;
      gap: 0.5rem;

      .toggle-button {
        color: #6c757d;
        
        &:hover {
          color: #495057;
        }
      }

      .clear-all-button {
        font-size: 0.875rem;
        padding: 0.375rem 0.75rem;
        min-height: 32px;

        mat-icon {
          font-size: 1rem;
          margin-right: 0.25rem;
        }
      }
    }
  }

  // Content Section
  .filter-summary-content {
    padding: 1rem;
    max-height: 400px;
    overflow-y: auto;
    transition: all 0.3s ease;

    &.collapsed {
      max-height: 0;
      padding: 0 1rem;
      overflow: hidden;
    }

    // Scrollbar styling
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f3f4;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 3px;

      &:hover {
        background: #a8a8a8;
      }
    }
  }

  // Filter Groups
  .filter-groups {
    .filter-group {
      margin-bottom: 1rem;

      &:last-child {
        margin-bottom: 0;
      }

      .group-header {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        margin-bottom: 0.5rem;

        .group-icon {
          font-size: 1rem;
          color: #6c757d;
        }

        .group-name {
          font-weight: 500;
          color: #495057;
          font-size: 0.875rem;
        }

        .group-count {
          color: #6c757d;
          font-size: 0.8rem;
        }
      }

      .group-filters {
        margin-left: 1.5rem;
      }
    }
  }

  // Filter Chips
  .filter-chips {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    align-items: center;

    .filter-chip {
      max-width: 300px;
      transition: all 0.2s ease;
      cursor: pointer;

      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
      }

      &.mandatory {
        opacity: 0.8;

        .remove-icon {
          display: none;
        }
      }

      &.multiple {
        .chip-value {
          font-weight: 500;
        }
      }

      .chip-content {
        display: flex;
        align-items: center;
        gap: 0.25rem;
        max-width: 250px;

        .chip-label {
          font-weight: 500;
          font-size: 0.8rem;
          opacity: 0.9;
          white-space: nowrap;
        }

        .chip-value {
          font-size: 0.8rem;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }

      .remove-icon {
        font-size: 1rem;
        opacity: 0.7;
        transition: opacity 0.2s ease;

        &:hover {
          opacity: 1;
        }
      }

      // Color variations
      &[color="primary"] {
        background-color: #e3f2fd;
        color: #1976d2;

        &:hover {
          background-color: #bbdefb;
        }
      }

      &[color="accent"] {
        background-color: #fce4ec;
        color: #c2185b;

        &:hover {
          background-color: #f8bbd9;
        }
      }

      &[color="warn"] {
        background-color: #fff3e0;
        color: #f57c00;

        &:hover {
          background-color: #ffe0b2;
        }
      }
    }
  }

  // Flat Chips Layout
  .filter-chips-flat {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    align-items: center;

    .more-filters-indicator {
      .more-chip {
        background-color: #e9ecef;
        color: #6c757d;
        cursor: pointer;
        transition: all 0.2s ease;

        &:hover {
          background-color: #dee2e6;
          transform: translateY(-1px);
        }

        mat-icon {
          font-size: 1rem;
          margin-left: 0.25rem;
        }
      }
    }
  }

  // Empty State
  .empty-state {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 2rem;
    color: #6c757d;
    font-style: italic;

    .empty-icon {
      font-size: 1.5rem;
      opacity: 0.5;
    }

    .empty-message {
      font-size: 0.9rem;
    }
  }
}

// Compact Summary (collapsed state)
.compact-summary {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  margin: 0.75rem 0;
  transition: all 0.3s ease;

  .compact-content {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 0.75rem;

    .compact-icon {
      color: #6c757d;
      font-size: 1rem;
    }

    .compact-text {
      color: #495057;
      font-size: 0.875rem;
      font-weight: 500;
      flex: 1;
    }

    .expand-button {
      color: #6c757d;
      padding: 0.25rem;

      &:hover {
        color: #495057;
      }

      mat-icon {
        font-size: 1rem;
      }
    }
  }

  &:hover {
    background: #e9ecef;
    cursor: pointer;
  }
}

// Responsive Design
@media (max-width: 768px) {
  .filter-summary-container {
    margin: 0.5rem 0;

    .filter-summary-header {
      flex-direction: column;
      gap: 0.5rem;
      align-items: stretch;

      .header-actions {
        justify-content: space-between;
      }
    }

    .filter-summary-content {
      padding: 0.75rem;
    }

    .filter-chips {
      gap: 0.375rem;

      .filter-chip {
        max-width: 250px;

        .chip-content {
          max-width: 200px;
        }
      }
    }

    .filter-groups {
      .group-filters {
        margin-left: 1rem;
      }
    }
  }
}

@media (max-width: 480px) {
  .filter-summary-container {
    .filter-chips {
      .filter-chip {
        max-width: 200px;

        .chip-content {
          max-width: 150px;

          .chip-label,
          .chip-value {
            font-size: 0.75rem;
          }
        }
      }
    }
  }
}

// Animation for filter chips
@keyframes chipAppear {
  from {
    opacity: 0;
    transform: scale(0.8) translateY(10px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.filter-chip {
  animation: chipAppear 0.3s ease-out;
}

// Accessibility improvements
.filter-chip {
  &:focus {
    outline: 2px solid #1976d2;
    outline-offset: 2px;
  }
}

.toggle-button,
.expand-button {
  &:focus {
    outline: 2px solid #1976d2;
    outline-offset: 2px;
  }
}

// High contrast mode support
@media (prefers-contrast: high) {
  .filter-summary-container {
    border: 2px solid #000;

    .filter-summary-header {
      border-bottom: 2px solid #000;
    }

    .filter-chip {
      border: 1px solid #000;
    }
  }
}

// Reduced motion support
@media (prefers-reduced-motion: reduce) {
  .filter-summary-container,
  .filter-chip,
  .compact-summary {
    transition: none;
  }

  .filter-chip {
    animation: none;
  }
}
