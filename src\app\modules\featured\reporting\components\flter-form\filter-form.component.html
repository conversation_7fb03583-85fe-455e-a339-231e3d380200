<div>
  <div class="table-container">
    <form [formGroup]="filtersForm" autocomplete="off">
      <div class="row no-gutters">
        <div class="filter-field" *ngFor="let config of searchConfigs | orderBy : 'position'">
          <mat-form-field
            class="w-100"
            *ngIf="config.type === 'dropdown'"
            [subscriptSizing]="'fixed'"
            hideHints>
            <mat-label>
              {{ 'report.' + config.fieldLabel.trim() | translate }}
            </mat-label>
            <!-- Handle dropdown fields -->
            <mat-select
              [formControlName]="config.backendParam"
              (selectionChange)="onBranchSelectionChange(config)"
              panelClass="custom-dropdown-panel">
              <mat-option [value]="null">{{ 'None' | translate }}</mat-option>
              <mat-option
                *ngFor="let item of getListForField(config.backendParam)"
                [value]="item.display ? item.value : item[config.idField]">
                {{ item.name ? item.name : item.year ? item.year : (item | localized) }}
                {{ item.display | translate }}
              </mat-option>
            </mat-select>
          </mat-form-field>
          <mat-form-field
            class="w-100"
            *ngIf="config.type === 'multiSelectDropdown'"
            [subscriptSizing]="'fixed'">
            <mat-label>
              {{ 'report.' + config.fieldLabel.trim() | translate }}
            </mat-label>
            <!-- Handle dropdown fields -->
            <mat-select
              [formControlName]="config.backendParam"
              (selectionChange)="onBranchSelectionChange(config)"
              multiple
              panelClass="custom-dropdown-panel">
              <app-select-check-all
                [model]="filtersForm.get(config.backendParam)"
                [values]="getSelectForField(config.backendParam)"
                text="Select All"></app-select-check-all>
              <!-- Dynamic options rendering based on config.fieldLabel -->
              <mat-option
                *ngFor="let item of getListForField(config.backendParam)"
                [value]="item[config.idField]">
                {{ item.name ? item.name : item.year ? item.year : (item | localized) }}
              </mat-option>
            </mat-select>
          </mat-form-field>
          <!-- Handle input fields with autocomplete -->
          <mat-form-field class="w-100" *ngIf="config.type === 'input'" [subscriptSizing]="'fixed'">
            <mat-label>
              {{ 'report.' + config.fieldLabel.trim() | translate }}
            </mat-label>
            <input
              [matAutocomplete]="auto"
              [formControlName]="config.backendParam"
              [placeholder]="config.placeholder || 'Search by Item Name/Code'"
              (input)="onSearchInput($event.target.value, config.backendParam)"
              matInput />
            <mat-autocomplete #auto="matAutocomplete" autoActiveFirstOption>
              <mat-option
                *ngFor="let item of getListForField(config.backendParam)"
                [value]="item.itemCode"
                (onSelectionChange)="onItemCodeSelected(item)">
                {{ item | localized }} - {{ item.itemCode }}
              </mat-option>
            </mat-autocomplete>
          </mat-form-field>
          <mat-form-field
            class="w-100"
            *ngIf="config.type === 'dateRange'"
            [subscriptSizing]="'fixed'">
            <mat-label>{{ 'report.' + config.fieldLabel.trim() | translate }}</mat-label>
            <mat-date-range-input [rangePicker]="picker" [max]="todayDate" disabled>
              <input
                matStartDate
                disabled
                formControlName="{{ config.backendParam + 'From' }}"
                placeholder="{{ 'voucher.startDate' | translate }}" />
              <input
                matEndDate
                disabled
                formControlName="{{ config.backendParam + 'To' }}"
                placeholder="{{ 'voucher.endDate' | translate }}" />
            </mat-date-range-input>
            <mat-datepicker-toggle [for]="picker" matIconSuffix></mat-datepicker-toggle>
            <mat-date-range-picker #picker disabled="false"></mat-date-range-picker>
          </mat-form-field>

          <mat-form-field class="w-100" *ngIf="config.type === 'date'" [subscriptSizing]="'fixed'">
            <mat-label>
              {{ 'report.' + config.fieldLabel.trim() | translate }}
            </mat-label>
            <input [matDatepicker]="picker" [formControlName]="config.backendParam" matInput />
            <mat-datepicker-toggle [for]="picker" matSuffix> </mat-datepicker-toggle>
            <mat-datepicker #picker></mat-datepicker>
          </mat-form-field>
          <div class="w-100 margin-bottom" *ngIf="config.type === 'accountSearch'">
            <app-accounts-prosearch-box
              [formControlName]="config.backendParam"
              reportLabel="{{ 'report.' + config.fieldLabel.trim() | translate }}">
            </app-accounts-prosearch-box>
          </div>

          <!-- Handle searchbox fields -->
          <div class="w-100" *ngIf="config.type === 'searchBox'">
            <app-searchbox
              #searchBoxForm
              [formControlName]="config.backendParam"
              [accountNo]="config.accountNo || false"
              [accountName]="config.accountName || false"
              [nameEnglish]="config.nameEnglish || true"
              [nameArabic]="config.nameArabic || true"
              [itemCode]="config.itemCode || true"
              [defaultSearchType]="config.defaultSearchType || 'itemCode'"
              [validatorApplied]="config.mandatory || false">
            </app-searchbox>
          </div>

          <!-- Handle checkbox fields -->
          <div class="w-100 d-flex align-items-center" *ngIf="config.type === 'checkbox'">
            <mat-checkbox [formControlName]="config.backendParam" color="primary">
              {{ 'report.' + config.fieldLabel.trim() | translate }}
            </mat-checkbox>
          </div>

          <!-- Handle radio group fields -->
          <div class="w-100" *ngIf="config.type === 'radioGroup'">
            <mat-label class="radio-group-label">{{
              'report.' + config.fieldLabel.trim() | translate
            }}</mat-label>
            <mat-radio-group
              [formControlName]="config.backendParam"
              aria-label="{{ 'report.' + config.fieldLabel.trim() | translate }}">
              <mat-radio-button
                *ngFor="let option of config.radioOptions"
                [value]="option.value"
                color="primary">
                {{ option.display | translate }}
              </mat-radio-button>
            </mat-radio-group>
          </div>
        </div>
        <div>
          <button class="submit" (click)="getReportData($event)" color="primary">
            {{ 'report.submit' | translate }}
          </button>
        </div>
      </div>
    </form>
  </div>
</div>
