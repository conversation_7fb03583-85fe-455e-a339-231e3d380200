<mat-card class="filter-form-container">
  <!-- Report Type Selector -->
  <ng-container *ngIf="showReportTypeSelector && reportTypeList?.length > 0">
    <div class="report-type-selector">
      <div class="filter-form-row">
        <!-- Parent Report Group Dropdown -->
        <div class="form-control-wrapper medium-control">
          <label>{{ 'report.parentReportGroup' | translate }}</label>
          <mat-form-field>
            <mat-select
              [value]="selectedParentGroup"
              (selectionChange)="onParentGroupChange($event.value)"
              panelClass="custom-dropdown-panel">
              <mat-option *ngFor="let group of reportTypeList" [value]="group.parentReportGroup">
                {{ group.parentReportGroup }}
              </mat-option>
            </mat-select>
          </mat-form-field>
        </div>

        <!-- Report Type Dropdown (filtered by parent group) -->
        <div class="form-control-wrapper medium-control">
          <label>{{ 'report.reportType' | translate }}</label>
          <mat-form-field>
            <mat-select
              [value]="selectedReportType"
              [disabled]="!selectedParentGroup"
              (selectionChange)="onReportTypeSelectionChange($event.value)"
              panelClass="custom-dropdown-panel">
              <mat-option *ngFor="let reportType of filteredReportTypes" [value]="reportType">
                {{ reportType.name }}
                {{ reportType.nameArabic ? '(' + reportType.nameArabic + ')' : '' }}
              </mat-option>
            </mat-select>
          </mat-form-field>
        </div>

        <!-- Reset Button -->
        <div class="form-control-wrapper small-control">
          <label>&nbsp;</label>
          <button (click)="onClearFilters($event)" mat-flat-button color="primary">
            {{ 'report.reset' | translate }}
          </button>
        </div>
      </div>
    </div>
  </ng-container>

  <!-- Dynamic Filter Form -->
  <div *ngIf="selectedReportType && searchConfigs.length > 0">
    <form [formGroup]="filtersForm" (ngSubmit)="onSubmit($event)" autocomplete="off">
      {{ 'report.filters' | translate }}
      <div class="filter-form-row">
        <div
          class="form-control-wrapper"
          *ngFor="let config of searchConfigs | orderBy : 'position'"
          [class]="getControlClass(config)">
          <!-- Field Label -->
          <label>
            {{ 'report.' + config.fieldLabel.trim() | translate }}
          </label>

          <!-- Dropdown fields -->
          <mat-form-field *ngIf="config.type === 'dropdown'" hideHints>
            <mat-select
              [formControlName]="config.backendParam"
              (selectionChange)="onBranchSelectionChange(config)"
              panelClass="custom-dropdown-panel">
              <mat-option
                *ngFor="let item of getListForField(config.backendParam)"
                [value]="item.display ? item.value : item[config.idField]">
                {{ item.name ? item.name : item.year ? item.year : (item | localized) }}
                {{ item.display | translate }}
              </mat-option>
            </mat-select>
          </mat-form-field>

          <!-- Multi-select dropdown fields -->
          <mat-form-field *ngIf="config.type === 'multiSelectDropdown'">
            <mat-select
              #matSelect
              [formControlName]="config.backendParam"
              multiple
              panelClass="custom-dropdown-panel">
              <app-select-check-all
                [model]="filtersForm.get(config.backendParam)"
                [values]="matSelect"
                text="Select All"></app-select-check-all>
              <mat-option
                *ngFor="let item of getListForField(config.backendParam)"
                [value]="item[config.idField]">
                {{ item.name ? item.name : item.year ? item.year : (item | localized) }}
              </mat-option>
            </mat-select>
          </mat-form-field>

          <!-- Product search as form control -->
          <div *ngIf="config.type === 'input'">
            <app-product-search-selection
              [customSearch]="true"
              [customColumns]="['itemCode', 'itemName', 'unitName']"
              [formControlName]="config.backendParam"
              (itemSelected)="onItemCodeSelect($event)">
            </app-product-search-selection>
          </div>

          <!-- Date range fields -->
          <mat-form-field *ngIf="config.type === 'dateRange'">
            <mat-date-range-input [rangePicker]="picker" [max]="todayDate" disabled>
              <input
                [formControlName]="config.backendParam + 'From'"
                matStartDate
                disabled
                placeholder="{{ 'voucher.startDate' | translate }}" />
              <input
                [formControlName]="config.backendParam + 'To'"
                matEndDate
                disabled
                placeholder="{{ 'voucher.endDate' | translate }}" />
            </mat-date-range-input>
            <mat-datepicker-toggle [for]="picker" matIconSuffix></mat-datepicker-toggle>
            <mat-date-range-picker #picker disabled="false"></mat-date-range-picker>
          </mat-form-field>

          <!-- Date fields -->
          <mat-form-field *ngIf="config.type === 'date'">
            <input [matDatepicker]="picker" [formControlName]="config.backendParam" matInput />
            <mat-datepicker-toggle [for]="picker" matSuffix></mat-datepicker-toggle>
            <mat-datepicker #picker></mat-datepicker>
          </mat-form-field>

          <!-- Account search fields -->
          <div *ngIf="config.type === 'accountSearch'">
            <app-accounts-prosearch-box
              [formControlName]="config.backendParam"
              reportLabel="{{ 'report.' + config.fieldLabel.trim() | translate }}">
            </app-accounts-prosearch-box>
          </div>
        </div>
      </div>
    </form>

    <!-- Form action buttons -->
    <div class="form-actions" *ngIf="selectedReportType && searchConfigs.length > 0">
      <button (click)="onClearFilters($event)" mat-stroked-button color="primary">
        {{ 'searchPanel.clear' | translate }}
      </button>
      <button (click)="onSubmit($event)" mat-flat-button color="primary">
        {{ 'report.submit' | translate }}
      </button>
    </div>
  </div>
</mat-card>
