import { Component, OnInit } from '@angular/core';
import { DashboardModulesHolder } from 'src/app/modules/shared/components/dashboard-modules-holder/modulesHolder';
import { navItems } from 'src/app/layouts/full/app-navigation-common-menus';

@Component({
  selector: 'app-accounts-dashboard',
  templateUrl: './accounts-dashboard.component.html',
  styleUrls: ['./accounts-dashboard.component.scss'],
})
export class AccountsDashboardComponent implements OnInit {
  productModulesList: DashboardModulesHolder[] = [];

  constructor() {
    // Constructor can be used for dependency injection if needed
  }

  ngOnInit(): void {
    const accountsMenu = navItems.find(item => item.route === 'accounts');
    if (accountsMenu && accountsMenu.children) {
      this.productModulesList = accountsMenu.children.map(child => ({
        moduleName: child.displayName,
        moduleDescription: `Manage ${child.displayName}.`,
        modulePermission: child.permissions,
        moduleRouterLink: `../${child.route.split('/').pop()}`,
        moduleType: 'subModule',
      }));
    }
  }
}
