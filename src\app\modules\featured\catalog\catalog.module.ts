import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FlexLayoutModule } from '@angular/flex-layout';
import { NgxScrollTopModule } from 'ngx-scrolltop';
import { SharedModule } from 'src/app/modules/shared/shared.module';
import { MaterialModule } from '../../material/material.module';
import { CatalogRoutingModule } from './catalog-routing.module';
import { CatalogComponent } from './catalog.component';
import { CategoryFormComponent } from './components/category/category-form/category-form.component';
import { CategoryListComponent } from './components/category/category-list/category-list.component';
import { CatalogDashboardComponent } from './components/dashboard/catalog-dashboard.component';
import { BulkEditProductsComponent } from './components/product/bulk-edit-products/bulk-edit-products.component';
import { ListProductsComponent } from './components/product/list-products/list-products.component';
import { ProductFormComponent } from './components/product/product-form/product-form.component';
import { UnitsFormComponent } from './components/units/unit-form/units-form.component';
import { UnitsComponent } from './components/units/units.component';
import { InputFormatDirective } from './directives/input-format.directive';

// Stock module components
import { AdjustmentListComponent } from '../stock/components/adjustment-list/adjustment-list.component';
import { OpenQuantityAdjustmentsComponent } from '../stock/components/adjustments-open-qty/open-quantity-adjustments.component';
import { BranchPartnerListingComponent } from '../stock/components/branch-partner-listing/branch-partner-listing.component';
import { BranchPartnerComponent } from '../stock/components/branch-partner/branch-partner.component';
import { StockAdjustmentComponent } from '../stock/components/stock-adjustment-create/stock-adjustment.component';
import { StockTransferCreateObComponent } from '../stock/components/stock-transfer-create-ob/stock-transfer-create-ob.component';
import { StockTransferIbComponent } from '../stock/components/stock-transfer-ib/stock-transfer-ib.component';
import { StockTransferObComponent } from '../stock/components/stock-transfer-ob/stock-transfer-ob.component';
import { StockTransferComponent } from '../stock/components/stock-transfer/stock-transfer.component';
import { StocksDashboardComponent } from '../stock/components/stocks-dashboard/stocks-dashboard.component';
import { UnitPriceUpdateComponent } from '../stock/components/unit-price-update/unit-price-update.component';

@NgModule({
  declarations: [
    // Catalog components
    CatalogComponent,
    ProductFormComponent,
    CategoryListComponent,
    CategoryFormComponent,
    UnitsComponent,
    UnitsFormComponent,
    ListProductsComponent,
    CatalogDashboardComponent,
    InputFormatDirective,
    BulkEditProductsComponent,

    // Stock components
    StocksDashboardComponent,
    OpenQuantityAdjustmentsComponent,
    AdjustmentListComponent,
    StockAdjustmentComponent,
    UnitPriceUpdateComponent,
    BranchPartnerComponent,
    BranchPartnerListingComponent,
    StockTransferComponent,
    StockTransferIbComponent,
    StockTransferObComponent,
    StockTransferCreateObComponent,
  ],
  imports: [
    CommonModule,
    CatalogRoutingModule,
    SharedModule,
    FlexLayoutModule,
    NgxScrollTopModule,
    MaterialModule,
  ],
  providers: [],
})
export class CatalogModule {}
