import { Directionality } from '@angular/cdk/bidi';
import { Component, Input } from '@angular/core';
import { ControlValueAccessor, NG_VALUE_ACCESSOR, UntypedFormBuilder } from '@angular/forms';
import { MatDialog, MatDialogConfig } from '@angular/material/dialog';
import { of } from 'rxjs';
import { catchError, finalize } from 'rxjs/operators';
import { ProductService } from 'src/app/core/api/product.service';
import { IInventory, IProductSearch } from 'src/app/modules/featured/catalog/models/product';
import { ProductAdjustmentsParams } from 'src/app/modules/featured/catalog/models/productAdjustmentParams';
import { BaseSearchSelectionComponent } from '../../base-search-selection/base-search-selection.component';
import { ProductGetAllComponent } from '../product-get-all/product-get-all.component';

@Component({
  selector: 'app-product-search-selection',
  templateUrl: './product-search-selection.component.html',
  styleUrls: ['./product-search-selection.component.scss'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      multi: true,
      useExisting: ProductSearchSelectionComponent,
    },
  ],
})
export class ProductSearchSelectionComponent
  extends BaseSearchSelectionComponent<IInventory>
  implements ControlValueAccessor
{
  @Input() customSearch = false;
  @Input() customColumns: string[] = [];
  get displayedSearchColumns(): string[] {
    if (this.customSearch && this.customColumns.length > 0) {
      return this.customColumns;
    }
    return [
      'itemCode',
      'itemName',
      'unitName',
      'warehouseName',
      'totalQuantityPerUnit',
      'retailPrice',
      'wholesalePrice',
      'distributorPrice',
      'purchasePrice',
      'discount',
      'category',
    ];
  }
  constructor(
    formBuilder: UntypedFormBuilder,
    private productService: ProductService,
    direction: Directionality,
    private dialog: MatDialog
  ) {
    super(formBuilder, direction);
  }

  protected fetchItems(value: string) {
    const params = new ProductAdjustmentsParams();
    params.searchString = value;
    params.pageSize = 999;
    return this.productService.getProductsByFilter(params).pipe(
      catchError(err => {
        console.error('Error fetching products', err);
        this.isLoading = false;
        this.resultNotFound = true;
        return of([]);
      }),
      finalize(() => (this.isLoading = false))
    );
  }

  protected extractData(searchResult: IProductSearch): IInventory[] {
    return searchResult?.inventories ?? [];
  }

  protected clearForms(): void {
    //
  }

  override onSelection(selectedItem: IInventory) {
    if (this.customSearch) {
      // For custom search mode, keep the display value and don't clear
      const displayValue = `${selectedItem.itemName} - ${selectedItem.itemCode}`;
      this.autoCompleteInput.get('userInput').patchValue(displayValue, { emitEvent: false });
      this.itemSelected.emit(selectedItem);
      this.onChange(selectedItem.itemCode);
      this.onTouched();
      this.itemSource = null;
      this.autoCompleteTrigger.closePanel();
    } else {
      // Original behavior for backward compatibility
      super.onSelection(selectedItem);
    }
  }

  override clearSelection() {
    this.itemSource = null;
    this.resultNotFound = false;
    this.autoCompleteTrigger.closePanel();

    if (this.customSearch) {
      // For custom search, reset the form control value
      this.onChange(null);
      this.onTouched();
    }
    // Original behavior for backward compatibility
    this.autoCompleteInput.get('userInput').patchValue(null, { emitEvent: false });
  }

  clearInput() {
    this.autoCompleteInput.get('userInput').patchValue(null, { emitEvent: false });
    if (this.customSearch) {
      this.onChange(null);
      this.onTouched();
    }
    this.itemSource = null;
    this.resultNotFound = false;
    this.autoCompleteTrigger.closePanel();
  }

  getAllProducts() {
    this.clearSelection();
    const dialogConfig = new MatDialogConfig();
    dialogConfig.minWidth = '90vw';
    dialogConfig.position = {
      top: '50px',
    };
    dialogConfig.panelClass = ['green_theme'];
    dialogConfig.hasBackdrop = true;
    dialogConfig.disableClose = true;
    dialogConfig.autoFocus = false;
    dialogConfig.direction = this.direction.value;
    dialogConfig.data = {
      displayedSearchColumns: this.displayedSearchColumns,
      customSearch: this.customSearch,
    };
    const dialogRef = this.dialog.open(ProductGetAllComponent, dialogConfig);
    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        if (this.customSearch) {
          // For custom search mode, update the display value
          const displayValue = `${result.itemName} - ${result.itemCode}`;
          this.autoCompleteInput.get('userInput').patchValue(displayValue, { emitEvent: false });
        }
        this.itemSelected.emit(result);
        this.onChange(result.itemCode);
      }
    });
  }

  // ControlValueAccessor implementation
  private onChange = (value: any) => {};
  private onTouched = () => {};

  writeValue(value: any): void {
    if (value && this.customSearch) {
      this.autoCompleteInput.get('userInput')?.setValue(value, { emitEvent: false });
    }
  }

  registerOnChange(fn: any): void {
    if (this.customSearch) {
      this.onChange = fn;
    }
  }

  registerOnTouched(fn: any): void {
    if (this.customSearch) {
      this.onTouched = fn;
    }
  }

  setDisabledState(isDisabled: boolean): void {
    if (this.customSearch && isDisabled) {
      this.autoCompleteInput.disable();
    } else if (this.customSearch) {
      this.autoCompleteInput.enable();
    }
  }
}
