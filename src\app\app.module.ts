import { HttpClientModule } from '@angular/common/http';
import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { MatPaginatorIntl } from '@angular/material/paginator';
import { BrowserModule, HammerModule } from '@angular/platform-browser';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { TranslateLoader, TranslateModule } from '@ngx-translate/core';
import { NgMultiSelectDropDownModule } from 'ng-multiselect-dropdown';
import { CookieService } from 'ngx-cookie-service';
import { NgxScrollTopModule } from 'ngx-scrolltop';
import { AppComponent } from './app.component';
import { AppRoutingModule } from './app.routing';
import { CustomMatPaginatorIntl } from './core/utils/custom-mat-paginator-int';
import { AuthLayoutComponent } from './layouts/auth-layout/auth-layout.component';
import { CoreModule } from './modules/core/core/core.module';
import { AuthenticationModule } from './modules/featured/authentication/authentication.module';
import { InvoiceDemoComponent } from './modules/invoice/invoice-demo/invoice-demo.component';
import { SharedModule } from './modules/shared/shared.module';
// Import your translation JSON files
import { Observable, of } from 'rxjs';
import ar from 'src/assets/i18n/ar.json';
import en from 'src/assets/i18n/en.json';

// Static translation loader defined inline
export class StaticTranslateLoader implements TranslateLoader {
  private translations: { [lang: string]: any } = {
    en,
    ar,
  };

  getTranslation(lang: string): Observable<any> {
    return of(this.translations[lang] || this.translations['en']);
  }
}

@NgModule({
  declarations: [AppComponent, AuthLayoutComponent, InvoiceDemoComponent],
  imports: [
    CoreModule,
    BrowserModule,
    BrowserAnimationsModule,
    AuthenticationModule,
    AppRoutingModule,
    FormsModule,
    HttpClientModule,
    SharedModule,
    NgMultiSelectDropDownModule.forRoot(),
    TranslateModule.forRoot({
      loader: {
        provide: TranslateLoader,
        useClass: StaticTranslateLoader,
      },
    }),
    HammerModule,
    NgxScrollTopModule,
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  providers: [
    {
      provide: MatPaginatorIntl,
      useClass: CustomMatPaginatorIntl,
    },
    CookieService,
  ],
  bootstrap: [AppComponent],
})
export class AppModule {}
