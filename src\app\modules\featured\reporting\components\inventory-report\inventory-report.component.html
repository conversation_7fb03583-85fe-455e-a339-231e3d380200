<!-- Filter Form Component -->
<app-filter-form
  *ngIf="reportTypeList?.length > 0"
  [searchConfigs]="searchConfigs"
  [filtersForm]="filtersForm"
  [reportTypeList]="reportTypeList"
  [selectedReportType]="selectedReportType"
  [showReportTypeSelector]="true"
  [getListForField]="getListForField"
  (reportTypeChange)="onReportTypeChange($event)"
  (clear)="resetReportType()"
  (clearFilters)="resetFilters()"
  (submit)="getReportData($event)"
  (itemCodeSelected)="onItemCodeSelected($event)"
  (branchChange)="onBranchChange()"
  (filterRemoved)="onFilterRemoved($event)"
  (filterClicked)="onFilterClicked($event)"
  (filtersCleared)="onFiltersCleared()"></app-filter-form>

<!-- Results Card -->
<mat-card class="result-card" class="m-t-10" *ngIf="dataSource?.data.length" appearance="outlined">
  <mat-card-content>
    <div
      class="d-flex justify-content-between align-items-center"
      *ngIf="dataSource.data?.length > 0">
      <mat-card-title class="m-t-10">{{ 'sales.salesList' | translate }}</mat-card-title>
      <div class="d-flex align-items-center">
        <button [matMenuTriggerFor]="columnMenu" mat-stroked-button color="primary" mat-button>
          <i-tabler class="icon-12" name="columns"></i-tabler>
          Columns
        </button>
        <button
          class="m-l-10"
          (click)="downloadReport()"
          mat-stroked-button
          color="primary"
          mat-button>
          <i-tabler class="icon-12" name="download"></i-tabler>
          Download
        </button>

        <mat-menu class="column-menu" #columnMenu="matMenu">
          <div (click)="$event.stopPropagation()">
            <mat-checkbox
              class="column-checkbox"
              *ngFor="let col of displayedColumns"
              [checked]="visibleColumns.includes(col)"
              [disabled]="visibleColumns.length === 1 && visibleColumns[0] === col"
              (change)="toggleColumn(col, $event.checked)">
              {{ 'inventoryReport.' + col | translate }}
            </mat-checkbox>
          </div>
        </mat-menu>
      </div>
    </div>
    <!-- Data Table -->
    <div class="table-responsive" *ngIf="dataSource.data?.length">
      <table
        class="w-100"
        [dataSource]="dataSource"
        (matSortChange)="onSortChange($event)"
        mat-table
        matSort>
        <ng-container *ngFor="let column of visibleColumns" [matColumnDef]="column">
          <th *matHeaderCellDef mat-header-cell mat-sort-header sticky>
            {{ 'inventoryReport.' + column | translate }}
          </th>
          <td *matCellDef="let element" mat-cell>{{ element[column] }}</td>
        </ng-container>
        <tr class="mat-row" *matNoDataRow>
          <td class="text-center" [attr.colspan]="visibleColumns.length">
            {{ 'common.noDataFound' | translate }}
          </td>
        </tr>
        <tr class="sticky-header" *matHeaderRowDef="visibleColumns" mat-header-row></tr>
        <tr *matRowDef="let row; columns: visibleColumns" mat-row></tr>
      </table>
    </div>

    <!-- Pagination -->
    <div class="pagination-container" *ngIf="reportData.length">
      <mat-paginator
        [length]="totalItems"
        [pageSize]="pageSize"
        [pageIndex]="currentPage"
        [pageSizeOptions]="[5, 10, 25, 100]"
        (page)="onPageChange($event)">
      </mat-paginator>
    </div>
  </mat-card-content>
</mat-card>
