  <mat-card class="filter-card" *ngIf="filtersForm && selectedReportType" appearance="outlined">
    <app-filter-form
      [formName]="formName"
      [searchConfigs]="searchConfigs"
      [filtersForm]="filtersForm"
      [isFullScreen]="false"
      [isAdvancedSearchVisible]="isAdvancedSearchVisible"
      [getListForField]="getListForField.bind(this)"
      [getReportData]="getReportData.bind(this)"
      [onSearchInput]="onSearchInput.bind(this)"
      [onItemCodeSelected]="onItemCodeSelected.bind(this)">
    </app-filter-form>
  </mat-card>
  <mat-card class="result-card" *ngIf="selectedReportType" appearance="outlined">
    <div class="no-data" *ngIf="!reportData.length">
      <p>No Data Found</p>
    </div>

    <div class="table-container" *ngIf="reportData.length">
      <table class="mat-elevation-z8 sticky-table" [dataSource]="dataSource" mat-table matSort>
        <ng-container *ngFor="let column of displayedColumns" [matColumnDef]="column">
          <th *matHeaderCellDef mat-header-cell mat-sort-header sticky>
            {{ 'inventoryReport.' + column | translate }}
          </th>
          <td *matCellDef="let element" mat-cell>{{ element[column] }}</td>
        </ng-container>

        <tr class="sticky-header" *matHeaderRowDef="displayedColumns" mat-header-row></tr>
        <tr *matRowDef="let row; columns: displayedColumns" mat-row></tr>
      </table>
    </div>
    <div class="pagination-container" *ngIf="reportData.length">
      <mat-paginator
        [length]="totalItems"
        [pageSize]="pageSize"
        [pageIndex]="currentPage"
        [pageSizeOptions]="[5, 10, 25, 100]"
        (page)="onPageChange($event)">
      </mat-paginator>
    </div>
  </mat-card>
</div>
