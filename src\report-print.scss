/* Report Print Styles */
$print-border-color: #cccccc;
$print-bg-light: #f9f9f9;
$print-bg-accent: #e6f2ff;
$print-text-accent: #0056b3;
$print-border-width: 1px;

@media print {
  /* Page setup */
  @page {
    size: A4;
    margin: 0;
  }

  html,
  body {
    margin: 0;
    padding: 0;
    width: 210mm;
    height: auto;
    background-color: white;
    -webkit-print-color-adjust: exact;
    print-color-adjust: exact;
  }

  /* Hide UI elements */
  .no-print,
  app-header,
  app-footer,
  .mat-toolbar,
  .mat-sidenav,
  .mat-drawer,
  .mat-drawer-container,
  .mat-drawer-content,
  button,
  .action-buttons,
  .invoice-container .action-buttons {
    display: none !important;
  }

  /* Utility classes */
  .page-break-before {
    page-break-before: always;
  }
  .page-break-after {
    page-break-after: always;
  }
  .avoid-break {
    page-break-inside: avoid;
  }

  /* Invoice structure */
  .invoice-container {
    width: 100%;
    margin: 0 auto;
    padding: 0;
    background-color: white;
    box-shadow: none;
  }

  .header-layout,
  .invoice-client-info,
  .invoice-footer,
  .invoice-footer-info,
  .client-details-qrcode-row {
    page-break-inside: avoid;
    display: flex !important;
    width: 100% !important;
    align-items: flex-start !important;
  }

  /* Tables */
  .items-table,
  .client-info-table,
  .client-details-table,
  .meta-details-table,
  .footer-info-table {
    width: 100%;
    border-collapse: collapse;
    border: $print-border-width solid $print-border-color;
  }

  .items-table th,
  .items-table td,
  .client-details-table td {
    border: $print-border-width solid $print-border-color;
    padding: 1px 2px;
    line-height: 1.1;
    word-break: break-word;
  }

  .items-table thead {
    display: table-header-group;
  }

  .items-table th {
    background-color: $print-bg-accent;
  }

  .right-align {
    text-align: right;
    padding-right: 4px;
  }

  .left-align {
    text-align: left;
    padding-left: 4px;
  }

  /* Colors */
  .title-table .title-cell,
  .totals-table tr.total-row,
  .totals-table tr.total-row td {
    background-color: $print-bg-accent;
  }

  .invoice-notes .notes-content {
    background-color: $print-bg-light;
    border: $print-border-width solid $print-border-color;
  }

  .qr-code-section {
    width: 195px !important;
    min-width: 195px !important;
    max-width: 195px !important;
    height: 195px !important;
    min-height: 195px !important;
    max-height: 195px !important;
    margin: 0 auto;
    padding: 0;
    background-color: white;
    border: none;
    flex-shrink: 0 !important;
    box-sizing: border-box !important;
  }

  .qr-code-section qrcode,
  .qr-code-section img,
  .qr-fallback-image {
    width: 190px !important;
    height: 190px !important;
    margin: 2px;
    border: none;
    box-sizing: border-box !important;
  }

  /* Print-specific adjustments for info-header */
  .info-header td {
    border-top: none !important; /* Remove top border for print */
    border-bottom: $print-border-width solid $print-border-color !important; /* Ensure consistent border */
  }

  /* Print-specific adjustments for invoice-title */
  .title-table .title-cell {
    border-bottom: $print-border-width solid $print-border-color !important; /* Ensure consistent border thickness */
  }
}
