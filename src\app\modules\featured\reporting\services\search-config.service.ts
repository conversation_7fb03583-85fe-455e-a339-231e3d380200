import { Injectable } from '@angular/core';
import { ISearchConfig } from '../models/search-config.interface';

@Injectable({
  providedIn: 'root'
})
export class SearchConfigService {

  constructor() { }

  /**
   * Get default search configuration for stock reports
   */
  getStockReportConfig(): ISearchConfig[] {
    return [
      {
        field: 'branch',
        type: 'dropdown',
        mandatory: true,
        placeholder: ' ',
        position: 1,
        isAdvanced: false,
        fieldLabel: 'branch',
        backendParam: 'branchId',
        idField: 'branchId'
      },
      {
        field: 'category',
        type: 'multiSelectDropdown',
        mandatory: false,
        placeholder: ' ',
        position: 2,
        isAdvanced: false,
        fieldLabel: 'categories',
        backendParam: 'categoryIds',
        idField: 'categoryId'
      },
      {
        field: 'warehouse',
        type: 'multiSelectDropdown',
        mandatory: false,
        placeholder: ' ',
        position: 3,
        isAdvanced: false,
        fieldLabel: 'warehouse',
        backendParam: 'warehouseIds',
        idField: 'warehouseId'
      }
    ];
  }

  /**
   * Validate search configuration
   */
  validateConfig(config: ISearchConfig[]): boolean {
    return config && config.length > 0 && config.every(c => 
      c.field && c.type && c.backendParam && c.fieldLabel
    );
  }

  /**
   * Sort configurations by position
   */
  sortByPosition(configs: ISearchConfig[]): ISearchConfig[] {
    return configs.sort((a, b) => a.position - b.position);
  }
}
