import { Injectable } from '@angular/core';
import { ISearchConfig, SearchConfigTemplates } from '../models/search-config.interface';

@Injectable({
  providedIn: 'root'
})
export class SearchConfigService {

  constructor() { }

  /**
   * Get search configurations for Stock Reports
   */
  getStockReportConfigs(): ISearchConfig[] {
    return [
      SearchConfigTemplates.searchBoxConfig(),
      SearchConfigTemplates.branchConfig(),
      SearchConfigTemplates.categoryConfig(),
      SearchConfigTemplates.warehouseConfig(),
      SearchConfigTemplates.showDetailedConfig(),
      SearchConfigTemplates.outputFormatConfig()
    ];
  }

  /**
   * Get search configurations for Price Reports
   */
  getPriceReportConfigs(): ISearchConfig[] {
    return [
      SearchConfigTemplates.searchBoxConfig(),
      SearchConfigTemplates.branchConfig(),
      SearchConfigTemplates.warehouseConfig(),
      SearchConfigTemplates.categoryConfig(),
      {
        type: 'multiSelectDropdown',
        backendParam: 'unitId',
        fieldLabel: 'unit',
        mandatory: false,
        position: 4,
        idField: 'unitId'
      },
      SearchConfigTemplates.showGroupedConfig(),
      SearchConfigTemplates.outputFormatConfig()
    ];
  }

  /**
   * Get common configurations that can be reused across reports
   */
  getCommonConfigs() {
    return {
      branch: SearchConfigTemplates.branchConfig(),
      warehouse: SearchConfigTemplates.warehouseConfig(),
      category: SearchConfigTemplates.categoryConfig(),
      year: SearchConfigTemplates.yearConfig(),
      dateRange: SearchConfigTemplates.dateRangeConfig(),
      searchBox: SearchConfigTemplates.searchBoxConfig(),
      outputFormat: SearchConfigTemplates.outputFormatConfig(),
      showDetailed: SearchConfigTemplates.showDetailedConfig(),
      showGrouped: SearchConfigTemplates.showGroupedConfig()
    };
  }

  /**
   * Create a custom configuration by combining common configs
   */
  createCustomConfig(configKeys: string[], customConfigs: ISearchConfig[] = []): ISearchConfig[] {
    const commonConfigs = this.getCommonConfigs();
    const selectedConfigs: ISearchConfig[] = [];

    configKeys.forEach(key => {
      if (commonConfigs[key]) {
        selectedConfigs.push({ ...commonConfigs[key] });
      }
    });

    // Add any custom configurations
    selectedConfigs.push(...customConfigs);

    // Sort by position
    return selectedConfigs.sort((a, b) => (a.position || 0) - (b.position || 0));
  }

  /**
   * Merge backend searchConfigs with local enhancements
   */
  mergeWithBackendConfigs(backendConfigs: any[], localConfigs: ISearchConfig[]): ISearchConfig[] {
    const merged: ISearchConfig[] = [];

    // First, add all backend configs
    backendConfigs.forEach(backendConfig => {
      merged.push({
        type: backendConfig.type,
        backendParam: backendConfig.backendParam,
        fieldLabel: backendConfig.fieldLabel,
        mandatory: backendConfig.mandatory,
        position: backendConfig.position,
        idField: backendConfig.idField,
        placeholder: backendConfig.placeholder
      });
    });

    // Then, add local configs that don't exist in backend
    localConfigs.forEach(localConfig => {
      const exists = merged.find(config => config.backendParam === localConfig.backendParam);
      if (!exists) {
        merged.push(localConfig);
      }
    });

    // Sort by position
    return merged.sort((a, b) => (a.position || 0) - (b.position || 0));
  }

  /**
   * Validate search configuration
   */
  validateConfig(config: ISearchConfig): boolean {
    if (!config.type || !config.backendParam || !config.fieldLabel) {
      console.error('Invalid search config: missing required fields', config);
      return false;
    }

    if (config.type === 'radioGroup' && (!config.radioOptions || config.radioOptions.length === 0)) {
      console.error('Invalid radio group config: missing options', config);
      return false;
    }

    return true;
  }

  /**
   * Get field dependencies map
   */
  getFieldDependencies(): { [key: string]: string[] } {
    return {
      'warehouseId': ['branchId'],
      'yearId': ['branchId']
    };
  }
}
