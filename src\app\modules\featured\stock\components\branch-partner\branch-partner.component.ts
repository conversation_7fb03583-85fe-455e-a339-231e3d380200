import { Component, Inject, Optional, SkipSelf } from '@angular/core';
import { UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { ActivatedRoute, Router } from '@angular/router';
import { CookieService } from 'ngx-cookie-service';
import { ToastrService } from 'ngx-toastr';
import { forkJoin } from 'rxjs';
import { CommonService } from 'src/app/core/api/common.service';
import { PartnerService } from 'src/app/core/api/partners/partner.service';
import { externalBranchPartner } from 'src/app/core/configs/dropDownConfig';
import { ActionType } from 'src/app/core/enums/actionType';
import { IActionEventType } from 'src/app/core/interfaces/actionEventType';
import { ICustomerModalData } from 'src/app/core/interfaces/customer';
import { IDistributor } from 'src/app/core/interfaces/distributor';
import { ILoginBranches } from 'src/app/modules/core/core/models/login';
import { AuthService } from 'src/app/modules/core/core/services/auth.service';
import { Account } from '../../../accounts/models/account';
import { CostCentre } from '../../../accounts/models/costCentre';
import { ChartOfAccountsService } from '../../../accounts/services/chart-of-accounts.service';
import { BranchService } from '../../../settings/services/branch.service';
import { CustomerFormComponent } from '../../../trading/components/customers/customer-form/customer-form.component';
import { BranchPartnerResponse } from '../../models/partner';

@Component({
  selector: 'app-branch-partner',
  templateUrl: './branch-partner.component.html',
  styleUrls: ['./branch-partner.component.scss'],
})
export class BranchPartnerComponent {
  branchPartnerForm: UntypedFormGroup;
  partnerId: string;
  parentAccounts: Account[] = [];
  branches: ILoginBranches[] = [];
  distributorAccounts: IDistributor[] = [];
  costCentreAccounts: CostCentre[] = [];
  mode: ActionType;
  actionMode = ActionType;
  loggedinBranch: number;
  loading = true;
  constructor(
    @Optional() @SkipSelf() @Inject(MAT_DIALOG_DATA) public modalData: ICustomerModalData,
    @Optional() @SkipSelf() public dialogRef: MatDialogRef<CustomerFormComponent>,
    private authService: AuthService,
    private toastr: ToastrService,
    private partnerService: PartnerService,
    private fb: UntypedFormBuilder,
    private route: ActivatedRoute,
    private router: Router,
    private chartOfAccountsService: ChartOfAccountsService,
    private branchService: BranchService,
    private cookieService: CookieService,
    private commonService: CommonService
  ) {}

  get IsViewMode() {
    return this.mode === this.actionMode.view;
  }

  ngOnInit(): void {
    this.loading = true;
    this.getAllDropDownData();
    this.mode = this.route.snapshot.data['mode'];
    this.route.params.subscribe(params => {
      this.partnerId = params['id'];
      if (this.partnerId) {
        this.getCustomer(this.partnerId);
      } else {
        this.initializeForm();
        this.loading = false;
      }
    });
  }

  ngAfterViewInit(): void {}

  getAllDropDownData(): void {
    this.loggedinBranch = +this.cookieService.get('branchId');
    const branches = this.branchService.getAllLoggedBranches();
    const parentAccounts =
      this.chartOfAccountsService.getChartOfAccountsByAccountCategoryAndAccountSubCategory(
        'ASSETS',
        'BRANCH'
      );

    forkJoin([parentAccounts, branches]).subscribe(results => {
      console.log(results);
      this.parentAccounts = results[0];
      const filteredBranches = results[1]?.filter(data => data.branchId !== this.loggedinBranch);
      this.branches = [...filteredBranches, externalBranchPartner];
      if (this.branches.length) {
        this.branchPartnerForm.get('partnerBranchNameArabic').setValue(this.branches[0].nameArabic);
        this.branchPartnerForm
          .get('partnerBranchNameEnglish')
          .setValue(this.branches[0].nameEnglish);
      }
    });
  }

  getCustomer(customerId: string): void {
    this.partnerService.getById(customerId).subscribe(
      response => {
        this.patchData(response);
        this.loading = false;
      },
      error => console.log(error)
    );
  }

  initializeForm(data?: BranchPartnerResponse) {
    this.branchPartnerForm = this.fb.group({
      parentAccountId: [data?.parentAccountId ?? null, Validators.compose([Validators.required])],
      nameArabic: [data?.nameArabic ?? null, Validators.compose([Validators.required])],
      nameEnglish: [data?.nameEnglish ?? null, Validators.compose([Validators.required])],
      partnerBranchId: [data?.partnerBranchId ?? null, Validators.compose([Validators.required])],
      id: [data?.id ?? null],
      accountNumber: [data?.accountNumber ?? null],
      accountId: [data?.accountId ?? null],
      partnerBranchNameArabic: [data?.partnerBranchNameArabic ?? null],
      partnerBranchNameEnglish: [data?.partnerBranchNameEnglish ?? null],
    });
    this.disableForm();
  }

  patchData(data: BranchPartnerResponse): void {
    this.initializeForm(data);
  }

  private disableForm(): void {
    if (this.mode === this.actionMode.view) {
      this.disbaleAllForms();
    }
  }

  private disbaleAllForms(): void {
    this.branchPartnerForm.disable();
  }

  onSubmit(event: IActionEventType) {
    this.branchPartnerForm.markAllAsTouched();
    if (this.branchPartnerForm && this.branchPartnerForm?.valid) {
      // create Mode
      if (event.actionType === this.actionMode.create) {
        console.log('this.customerForm.value', this.branchPartnerForm.value);
        this.partnerService.create(this.branchPartnerForm.value).subscribe(() => {
          //this.toastr.success('Customer Created Successfully');
          this.commonService.playSuccessSound();
          this.router.navigate(['../'], { relativeTo: this.route });
        });
      }
      // edit Mode
      if (event.actionType === this.actionMode.edit) {
        this.partnerService.edit(this.branchPartnerForm.value, this.partnerId).subscribe(() => {
          //this.toastr.success('Customer Updated Successfully');
          this.commonService.playSuccessSound();
          this.router.navigate(['../../'], { relativeTo: this.route });
        });
      }
    } else {
      this.commonService.scrollToError();
    }
  }

  onNoClick(): void {
    this.dialogRef.close('cancel');
  }
}
