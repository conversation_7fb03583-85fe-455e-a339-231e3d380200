{"retailPrice": "Retail Price", "wholesalePrice": "Wholesale Price", "distributorPrice": "Distributor Price", "openPurchasePrice": "Open Purchase Price", "purchasePrice": "Purchase Price", "avgPurchasePrice": "Average Purchase Price", "NOTRETURNED": "Not Returned", "PARTIALLYRETURNED": "Partially Returned", "RETURNED": "Returned", "fullCreditNote": "Full Credit Note", "partialCreditNote": "Partial Credit Note", "debitNote": "Debit Note", "payment": "Payment Method", "offer": "Offer Duration", "guarante": "Warranty Duration", "delivery": "Delivery Duration", "note": "Notes", "notposted": "Not Approved", "posted": "Approved", "jurnotposted": "Not Posted", "jurposted": "Posted", "site": {}, "layouts": {"admin": {"catalogSubhead": "Catalog Management", "brands": "Brands", "categories": "Categories", "products": "Products", "peopleSubhead": "People Management", "customers": "Customers", "about": "About", "appSubhead": "Application", "roles": "Roles", "users": "Users", "accSubhead": "Access Management", "eventLogs": "Event Logs", "salesSubhead": "Sales Management", "orders": "Orders"}}, "dashboard": {"title": "Dashboard", "subtitle": "Quick Overview of your Business."}, "about": {}, "settings": {"title": "Settings", "subtitle": "Manage Application Settings.", "generalSettings": "General Settings", "language": "Language", "english": "English", "khmer": "Khmer", "russian": "Russian", "french": "French", "spanish": "Spanish"}, "userCard": {"profile": "Profile", "settings": "Settings", "logout": "Logout"}, "dialogs": {"logout": {"title": "Logout", "message": "Are you sure?", "button": "Confirm"}, "checkOut": {"title": "Checkout", "message": "Make an Order!", "button": "Confirm", "buttonProcessing": "Processing..."}, "delete": {"title": "Delete Confirmation", "buttons": {"cancel": "Cancel", "confirm": "Confirm"}}, "confirmPost": {"title": "Confirm Submission", "buttons": {"cancel": "Cancel", "confirm": "Confirm"}}}, "components": {"accessDenial": {"header": "Access Denied.", "text": "You do not have permission to access the page you were looking for. Contact the system administrator.", "button": "Go Home"}, "notFound": {"header": "Page Not Found.", "text": "The page you were looking for could not be found. It might have been removed, renamed, or did not exist in the first place.", "button": "Go Home"}, "serverError": {"header": "Internal Server Error.", "text": "There is a problem with the internal server.", "button": "Go Home"}}, "common": {"buttons": {"edit": "Edit", "view": "View", "delete": "Delete", "cancel": "Cancel", "submit": "Submit", "ok": "OK", "yes": "Yes", "no": "No", "confirm": "Confirm", "save": "Save", "back": "Back", "logout": "Logout", "logoutCancel": "Cancel", "fullCreditNote": "Issue Credit Note", "partialCreditNoteAction": "Issue Credit Note", "debitNoteAction": "Issue Debit Note"}, "minLength": "Must be {{length}} characters", "searchnodata": "Record Not Found", "success_operation": "Operation was successful", "chooseFile": "Choose <PERSON>", "grantAllPermission": "Grant All Permissions", "selectAll": "Select All Permissions", "required": "Field Required", "reset": "Reset", "cancel": "Cancel", "submit": "Submit", "ok": "OK", "yes": "Yes", "no": "No", "confirm": "Confirm", "confirmSubmission": "Confirm Submission", "bulkEdit": "Bulk Edit Product Details", "action": "Action", "itemsPerPage": "Items per Page", "editAction": "Edit", "viewAction": "View", "postAction": "Approve", "deleteAction": "Delete", "noDataFound": "No Data Found", "copyright": "Copyright © Swami Information Systems", "print": "Print", "searching": "Searching...", "active": "Active", "confirmAction": "Are you sure?", "confirmDeleteAction": "Deleting {{count}} items. Are you sure?", "field": {"accountNumber": "Account Number", "nameArabic": "Arabic Name", "nameEnglish": "English Name", "vatNo": "VAT Number", "phone": "Phone Number", "email": "Email", "freezed": "Frozen", "percDiscount": "Discount Percentage", "allowedBalance": "Allowed Balance", "allowedDays": "Allowed Days", "yrlyTarget": "Annual Target", "costAccount": "Cost Center", "distributorAccount": "Distributor", "transactionDetails": "Transaction Details", "accountDetails": "Account Details", "commission": "Commission"}, "pagination": {"itemsPerPage": "Items per Page", "nextPage": "Next Page", "previousPage": "Previous Page", "ofLabel": "of"}}, "customer": {"customerTitle": "Customer Information", "customerList": "Customer List", "register_customer": "Add New Customer", "parentId": "Parent Account", "name": "Arabic Name", "englishName": "English Name", "customerSelection": "Select Customer"}, "supplier": {"supplierTitle": "Supplier Information", "supplierList": "Supplier List", "register_supplier": "Add New Supplier", "parentId": "Parent Account", "name": "Arabic Name", "englishName": "English Name", "vendorType": "Vendor Type", "supplierSelection": "Select Supplier"}, "distributor": {"distributorTitle": "Distributor Information", "distributorList": "Distributor List", "register_distributor": "Add New Distributor", "parentId": "Parent Account", "name": "Arabic Name", "englishName": "English Name"}, "address": {"address": "Address", "bldgnNo": "Building Number", "addnNo": "Additional Number", "streetName": "Street", "city": "City", "district": "District", "country": "Country", "shortAddress": "Short Address", "otherAddress": "Other Address", "postalCode": "Postal Code"}, "paymentsType": {"paymentMethod": "Payment Method", "0": "Cash", "3": "Credit", "2": "Bank Transfer", "1": "Card", "4": "Multiple", "cash": "Cash", "credit": "Credit", "bank": "Bank Transfer", "card": "Card", "creditDueDate": "Due Date", "cashAccounts": "Cash Accounts", "bankAccounts": "Bank Accounts", "cardAccounts": "Card Accounts", "amount": "Amount", "paymentLabel": "Available Payment Types", "paymentSelectionError": "Please select a payment method", "balanceError": "Insufficient Balance", "availableBalance": "Available Balance", "returnDate": "Return Date", "returnText": "Reason for Return", "paymentSelectionErrorDebitNote": "Please select a payment method", "singleOnly": "Only one payment method can be selected", "creditType": "Another payment method cannot be selected", "halala": "Dismiss <PERSON>action", "otherpaymentsSelected": "Selection not allowed when other payment types selected"}, "login": {"password-required": "Password is required", "username-required": "Username is required", "await-login": "Logging In", "branch-select": "Please select the branch and year", "branch": "Branch", "branch-year": "Year", "login": "<PERSON><PERSON>", "userName": "Username", "password": "Password", "signin": "Sign In", "tenant": "Tenant Code", "tenant-required": "Tenant Code is required"}, "sales": {"salesListing": "Sales Invoices List", "salesReceivables": "Settlement of Credit Invoices", "updateSales": "Update Sales", "viewSales": "View Invoice", "createSales": "New Invoice", "createSalesCreditNote": "Issue Credit Note (Full)", "createSalesPartialNote": "Issue Credit Note (Partial)", "createSalesDebitNote": "Issue Debit Note", "salesList": "Sales Invoices List", "saleInvoice": "Sales Invoice No.", "fullCreditInvoice": "Credit Note - Full Return for Invoice No.", "partilCreditInvoice": "Credit Note - Partial Return for Invoice No.", "debitInvoice": "Debit Note for Invoice No.", "register_sales": "New Invoice", "salesDate": "Invoice Date", "referenceNo": "Reference Number", "referenceDate": "Reference Date", "invoiceNote": "Invoice Notes", "customerSelection": "Select Customer", "code": "Item Code", "name": "Item Name", "unit": "Unit", "priceType": "Pricing Method", "quantity": "Quantity", "vatAmt": "VAT Amount", "subTotal": "Item Total", "note": "Notes", "discount": "Discount", "price": "Price", "returnableQty": "Returnable Quantity", "exp-itemCode": "Item Code", "exp-wareHouse": "Warehouse", "exp-vatPct": "VAT %", "exp-currentQty": "Current Quantity", "exp-partNo": "Part Number", "exp-discount": "Discount", "exp-discountType": "Discount Type", "invoiceNo": "Invoice Number", "invoiceDate": "Invoice Date", "invoiceAmt": "Invoice Amount", "invoiceCustomerNo": "Account Number", "invoiceCustomerName": "Customer Name", "amount": "Amount", "fullCreditNote": "Credit Note - Full Return", "viewNotes": "View Credit and Debit Notes", "partialCreditNote": "Credit Note - Partial Return", "debitNote": "Debit Note", "partial-qty-sold": "Quantity Sold", "partial-ref-quantity": "Returned Quantity", "partial-price": "Price", "fullCreditConfirmation": "Do you wish to complete the return for the items below?", "returnStatus": "Status", "unpaidAmount": "Outstanding Amount", "fullCreditNoteButton": "Issue Credit Note", "zatcaInvoiceError": "Error - Document does not meet compliance requirements and cannot be sent", "zatcaInvoiceWarnings": "Document partially meets compliance requirements (Warnings present), please confirm sending the file", "zatcaInvoiceResubmit": "Send File", "zatcaEinvoices": "Electronic Invoices", "zatcaInvoiceNoErrors": "No errors for this document"}, "salesNotes": {"fullCredit": "Credit Notes - Full Return", "partialCredit": "Credit Notes - Partial Return", "debitNote": "Debit Notes", "allNotes": "All Notes", "fullCreditNote": "Credit Note - Full Return", "partialCreditNote": "Credit Note - Partial Return"}, "purchaseNotes": {"fullCredit": "Full Return", "partialCredit": "Partial Return", "allNotes": "All Returns", "fullCreditNote": "Full Return", "partialCreditNote": "Partial Return"}, "customerTypes": {"walkIn": "Cash Customer", "Existing": "Registered Customer"}, "supplierTypes": {"cashPurchase": "Cash Supplier", "registeredSupplier": "Registered Supplier"}, "priceTypes": {"retail": "Retail Price", "wholeSale": "Wholesale Price", "distributor": "Distributor Price", "purchase": "Purchase Price", "avgPurchase": "Average Purchase Price"}, "salesSummary": {"totalExVatDisc": "Total excluding discount and VAT", "totalDiscount": "Total Discount", "totalVat": "VAT Amount", "grandTotal": "Grand Total", "balanceAmt": "Balance", "changeAmt": "Change", "fractionAmt": "Fractional Discount", "salesLabel": "Sales Summary"}, "placeHolder": {"customerSearch": "Minimum search length 3 characters/numbers", "customerNotFound": "Customer not found"}, "productSearch": {"productSearch": "Select Item", "placeHolder": "Minimum search length 3 characters/numbers", "productNotFound": "Item not found", "itemCode": "Item Code", "itemName": "Item Name", "unitName": "Unit", "warehouseName": "Warehouse", "currentQty": "Current Quantity", "retailPrice": "Retail Price", "wholesalePrice": "Wholesale Price", "distributorPrice": "Distributor Price", "purchasePrice": "Purchase Price", "discount": "Discount", "category": "Category", "totalQuantityPerUnit": "Current Quantity"}, "dashBoardModules": {"posFeatures": "POS Features", "inventoryMgt": "Inventory Management", "identityMgt": "Identity Management", "enterpriseMgt": "System Settings", "acountingMgt": "Accounting System", "reportMgt": "Report Management", "utilityMgt": "Utility Management", "tradeMgt": "Sales and Purchases", "sales": "Sales", "purchase": "Purchases", "customers": "Customers", "suppliers": "Suppliers", "distributors": "Distributors"}, "navigationMenus": {"reportsSetup": "Reports Set-Up", "posFeatures": "POS Features", "inventory": "Inventory", "products": "Product Data", "units": "Product Units", "catagories": "Categories", "stock": "Stock Management", "openquantityAdjustment": "Add and Adjust Opening Quantities", "stockTracking": "Stock Inventory", "Inventory": "Inventory Reports List", "priceAdjustment": "Add and Adjust Prices", "trading": "Sales and Purchases System", "sales": "Sales", "customers": "Customers", "purchase": "Purchases", "suppliers": "Suppliers", "distributors": "Distributors", "reports": "Reports", "itemPriceReport": "Item Price Report", "stockValueReport": "Stock Value Report", "accountingReport": "Accounting Reports List", "enterprise": "System Settings", "companyProfile": "Company Settings", "branchSetup": "Branch Settings", "wareHouseSetup": "Warehouse Settings", "accounting": "Accounting", "chartOfAccounts": "Chart of Accounts", "journalEntries": "Journal Entries", "costCenter": "Cost Centers", "accountReports": "Reports", "accountBookSetup": "Accounting Setup", "fixedAsset": "Fixed Asset Management", "listOfAccount": "Account List", "statementOfAccount": "Account Statement", "identity": "Permissions", "rolesPermissions": "Roles and Permissions", "users": "Users", "utility": "Utilities", "depreciation": "Depreciation", "quotations": "Quotations", "purchases": "Purchases", "ucvoucher": "Receipt and Payment Vouchers", "tenantConfiguration": "Tenant Configuration", "tenantMgt": "Tenant Management", "branchPartner": "Branch Accounts Setup", "stockTransfer": "Stock Transfers In and Out", "zatcaListings": "Zatca Registration Listings", "zatcaReports": "Zatca Reports", "salesReports": "Sales Reports", "purchaseReports": "Purchase Reports"}, "invoiceStatus": {"notReturned": "Not Returned", "partiallyReturned": "Partially Returned", "returned": "Returned"}, "transactionTypes": {"sales": "Sales", "creditNote": "Credit Note", "debitNote": "Debit Note", "all": "All", "allNotes": "All Adjustments", "quotation": "Quotation"}, "settlementStatus": {"unpaid": "UnPaid", "partiallyPaid": "Partially Paid", "fullyPaid": "<PERSON>y Paid"}, "roles": {"createRole": "Create Role", "editRole": "Edit Role", "viewRole": "View Role", "listRole": "Role List", "register_role_Permission": "Create Roles and Permissions", "roleId": "Role ID", "roleName": "Name", "roleDesc": "Description", "roleTab": "Roles", "permissionTab": "Permissions", "roleNameField": "Role Name", "profile": "Role Name"}, "users": {"userListings": "User List", "editUser": "Edit User", "viewUser": "View User", "register_user": "New User", "userName": "Username", "userFname": "First Name", "userLname": "Last Name", "userRole": "User Role", "firstName": "First Name", "lastName": "Last Name", "userId": "User ID", "userEmail": "Email", "userPhone": "Phone", "userRoles": "Role", "userBranches": "Authorized Branches", "userDefaultBranch": "Default Branch", "userWarehouse": "Authorized Warehouses", "userActive": "Active", "defaultPassword": "Default Password", "userPasswordReset": "New Password"}, "notes": {"notesNo": "Note Number", "notesDate": "Note Date", "invoiceNo": "Invoice Number", "invoiceDate": "Invoice Date", "customerName": "Customer Name", "vatNo": "VAT Number", "noteType": "Type", "grandTotal": "Note Amount"}, "compareTable": {"code": "Item Code", "name": "Item Name", "unit": "Unit", "warehouse": "Warehouse", "existingQty": "Sold Quantity", "purchasedQty": "Purchased Quantity", "updatedQty": "Returned Quantity", "partialReturnText": "Do you wish to complete the return for the items below?"}, "compareTableDebitNote": {"code": "Item Code", "name": "Item Name", "unit": "Unit", "priceType": "Pricing Method", "price": "Price", "quantity": "Quantity", "discount": "Discount", "debitNoteReturnText": "Do you wish to complete the process for the items below?"}, "searchPanel": {"itemCode": "Item Code", "nameArabic": "Arabic Name", "nameEnglish": "English Name", "accountNo": "Account Number", "accountName": "Account Name", "searchType": "Search By", "searchString": "Search", "clear": "Clear", "advancedSearch": "Advanced Search", "searchByEnglishName": "Search by English Name", "searchByArabicName": "Search by Arabic Name", "searchByCode": "Search by Item Code", "searchByAccountNumber": "Search by Account Number", "searchByJournalNumber": "Journal Number", "accountNumber": "Account Number", "searchByJournalRef": "Search by Journal Number", "searchByAccountNo": "Search by Account Number", "searchByAccountName": "Search by Account Name", "min3charsEntry": "Minimum search length 3 characters/numbers"}, "productGrid": {"action": "Action", "itemCode": "Item Code", "nameArabic": "Arabic Name", "nameEnglish": "English Name", "partNumber": "Part Number", "category": "Category", "vat": "VAT %", "Bulk Update Product": "Bulk Update Products", "Reload": "Reload", "Delete": "Delete"}, "productBaicTab": {"listing": "Product List", "productConfiguration": "Product Details", "register_product": "Add New Product", "itemCode": "Item Code", "basicInformation": "Basic Information", "unitsAndPrices": "Units and Prices", "discountAndProfits": "Discounts and Profits", "images": "Images", "specialInstructions": "Special Instructions", "nameArabic": "Arabic Name", "nameEnglish": "English Name", "partNumber": "Part Number", "category": "Category", "vat": "VAT %", "itemTypes": "Type", "warehouse": "Warehouse", "maxQty": "Maximum Quantity", "minQty": "Minimum Quantity", "color": "Color", "size": "Size", "daysToAlert": "<PERSON><PERSON>", "active": "Active", "expiryDate": "Has Expiry Date", "weighted": "Has Weight", "private": "Private", "general": "General", "description": "Description", "cancel": "Cancel", "submit": "Save"}, "productUnitPricesTab": {"action": "Action", "unitBarcode": "Barcode", "unitOfMeasureCost": "Unit of Measure", "costPrice": "Cost Price", "openPurRice": "Opening Purchase Price", "purchasePrice": "Purchase Price", "wholesalePrice": "Wholesale Price", "distributorPrice": "Distributor Price", "retailPrice": "Retail Price", "transportCost": "Additional Cost", "cancel": "Cancel", "submit": "Save"}, "productDiscountsTab": {"unitBarcode": "Barcode", "unitOfMeasure": "Unit of Measure", "discountMethod": "Discount Method", "discount": "Discount", "profitMethod": "Profit Method", "profit": "Profit", "cancel": "Cancel", "submit": "Save"}, "productSpecialInstructionsTab": {"itemFree": "Free Item", "freeStartDate": "Start Date", "freeEndDate": "End Date", "itemPrintFrozen": "Freeze Printing", "itemSalesFrozen": "Freeze Sales", "itemTransferFrozen": "Freeze Transfer", "reason": "Reason", "cancel": "Cancel", "submit": "Save"}, "productUnits": {"createUnit": "Add Unit", "listing": "Unit List", "action": "Action", "edit": "Edit", "view": "View", "delete": "Delete", "name": "Name", "type": "Type", "factor": "Factor"}, "productCategory": {"createCatgory": "Add Category", "listings": "Category List", "action": "Action", "edit": "Edit Category", "view": "View", "delete": "Delete", "nameArabic": "Arabic Name", "nameEnglish": "English Name", "parentCategory": "Parent Category"}, "openqty": {"action": "Action", "edit": "Edit", "view": "View", "delete": "Delete", "warehouse": "Warehouse", "category": "Category", "itemCode": "Item Code", "nameArabic": "Arabic Name", "nameEnglish": "English Name", "unit": "Unit", "openQty": "Opening Quantity", "cancel": "Cancel", "updateQty": "Update Quantities"}, "createAdjsutments": {"createAdjustment": "Add Inventory Adjustment", "warehouse": "Warehouse", "search": "Search", "action": "Action", "document": "Adjustment Document Number", "adjustmentDate": "Adjustment Date", "createdBy": "Created By", "status": "Status", "itemCode": "Item Code", "nameArabic": "Arabic Name", "nameEnglish": "English Name", "unit": "Unit", "OpenQty": "Opening Quantity", "UpdateQty": "Update Quantity", "actualqty": "Actual Quantity", "difference": "Difference", "remark": "Remarks", "post": "Approve"}, "bulkEdit": {"bulkEditText": "You are about to update <span class='text-error'>{{count}}</span> items. Are you sure?", "category": "Update Category", "vat": "Update VAT Rate", "status": "Update Item Status", "active": "Active", "inactive": "Inactive", "selectOption": "Please select an option"}, "priceUpdate": {"itemCode": "Item Code", "name": "Item Name", "unit": "Unit", "wholesalesPrice": "Wholesale Price", "distributorPrice": "Distributor Price", "retailPrice": "Retail Price", "openPurchasePrice": "Opening Purchase Price", "purchasePrice": "Purchase Price", "costPrice": "Cost Price"}, "salesQuotes": {"updateQuotation": "Edit Quotation", "deleteQuotation": "Delete Quotation", "viewQuotation": "View Quotation", "postQuotation": "Approve Quotation", "createQuotation": "New Quotation", "quotationList": "Quotation List", "quotationInfo": "Quotation No.", "register_quotes": "New Quotation", "invoiceNo": "Quotation Number", "invoiceDate": "Quotation Date", "invoiceAmt": "Quotation Amount", "invoiceCustomerNo": "Customer Number", "invoiceCustomerName": "Customer Name", "amount": "Amount", "salesDate": "Invoice Date", "referenceNo": "Reference Number", "referenceDate": "Reference Date", "invoiceNote": "Invoice Notes", "customerSelection": "Select Customer", "code": "Item Code", "name": "Item Name", "unit": "Unit", "priceType": "Pricing Method", "quantity": "Quantity", "vatAmt": "VAT Amount", "subTotal": "Item Total", "note": "Notes", "discount": "Discount", "price": "Price", "returnableQty": "Returnable Quantity", "exp-itemCode": "Item Code", "exp-wareHouse": "Warehouse", "exp-vatPct": "VAT %", "exp-currentQty": "Current Quantity", "exp-partNo": "Part Number", "exp-discount": "Discount", "exp-discountType": "Discount Type", "fullCreditNote": "Credit Note - Full Return", "viewNotes": "View Credit and Debit Notes", "partialCreditNote": "Credit Note - Partial Return", "debitNote": "Debit Note", "partial-qty-sold": "Quantity Sold", "partial-ref-quantity": "Returned Quantity", "partial-price": "Price", "fullCreditConfirmation": "Do you wish to complete the return process for the items below?", "returnStatus": "Status", "notes": "Notes", "termsAndConditions": "Terms and Conditions"}, "purchase": {"purchaseList": "Purchase Invoices List", "register_purchase": "New Invoice", "invoiceNo": "Invoice Number", "invoiceDate": "Invoice Date", "invoiceAmt": "Invoice Amount", "invoiceCustomerNo": "Account Number", "invoiceCustomerName": "Customer Name", "amount": "Amount", "salesDate": "Invoice Date", "referenceNo": "Reference Number", "referenceDate": "Reference Date", "invoiceNote": "Invoice Notes", "customerSelection": "Select Customer", "code": "Item Code", "name": "Item Name", "unit": "Unit", "priceType": "Pricing Method", "quantity": "Quantity", "vatAmt": "VAT Amount", "subTotal": "Item Total", "note": "Notes", "discount": "Discount", "price": "Price", "returnableQty": "Returnable Quantity", "distributorPrice": "Distributor Price", "wholeSalePrice": "Wholesale Price", "retailPrice": "Retail Price", "purchasePrice": "Purchase Price", "profit": "Profit", "exp-itemCode": "Item Code", "exp-wareHouse": "Warehouse", "exp-vatPct": "VAT %", "exp-currentQty": "Current Quantity", "exp-partNo": "Part Number", "exp-discount": "Discount", "exp-discountType": "Discount Type", "fullCreditNote": "Full Return for Invoice No.", "fullCreditNoteButton": "Return Invoice", "viewNotes": "View Return Documents", "partialCreditNote": "Partial Return for Invoice No.", "debitNote": "Debit Note", "partial-qty-sold": "Quantity Sold", "partial-ref-quantity": "Returned Quantity", "partial-price": "Price", "fullCreditConfirmation": "Do you wish to complete the return process for the items below?", "returnStatus": "Status", "fullReturn": "Full Return", "partialReturn": "Partial Return", "purchaseView": "Purchase Invoice No.", "partialCreditNoteAction": "Return Invoice", "newfullCreditNote": "Issue Full Return", "newpartialCreditNote": "Issue Partial Return"}, "company": {"registration": "Company Settings", "nameArabic": "Arabic Name", "nameEnglish": "English Name", "vatNo": "VAT Number", "phone": "Phone Number", "email": "Email"}, "branch": {"listbranch": "Branch List", "createBranch": "Create New Branch", "editBranch": "Edit Branch Details", "viewBranch": "View Branch Details", "nameArabic": "Arabic Name", "nameEnglish": "English Name", "vatNo": "VAT Number", "phone": "Phone Number", "email": "Email", "businessCategoryArabic": "Category Arabic", "businessCategoryEnglish": "Category English", "invoiceTrailing": "Invoice Trailing"}, "stores": {"branchName": "Branch Name", "listStores": "Store List", "createStore": "Add New Store", "editStore": "Edit Store Details", "viewStore": "View Store Details", "nameArabic": "Arabic Name", "nameEnglish": "English Name", "vatNo": "VAT Number", "phone": "Phone Number", "email": "Email"}, "caccounts": {"createAccount": "Add Account", "viewAccount": "View Account Details", "editAccount": "Edit Account", "listAccounts": "Account Directory", "accountNo": "Account Number", "accountEnglishName": "English Name", "accountArabicName": "Arabic Name", "accountType": "Account Type", "accountGroup": "Account Group", "bsnsGroup": "Business Group", "parentAccount": "Parent Account", "costAccount": "Cost Account", "finalAccountGroup": "Final Account Group", "notes": "Notes", "contraAccount": "Contra Account", "depreciating": "Depreciating Account", "frozen": "Frozen Account", "hidden": "Hidden", "calistings": "Accounting Directory List", "catreeview": "Accounting Directory Tree"}, "finalAccountGrpDropDown": {"budget": "Budget", "operation": "Operation", "trading": "Trading", "ProfitandLoss": "Profit and Loss", "income": "Net Income"}, "accountTypeDropDown": {"general": "General", "detailed": "Detailed"}, "bsnsGroupDropDown": {"general": "General", "cashier": "Cashiers", "bank": "Banks", "customer": "Customers", "supplier": "Suppliers", "branch": "Branch", "distributor": "Distributors"}, "accountGrpDropDown": {"assets": "Assets", "liabilities": "Liabilities", "capital": "Capital", "revenues": "Revenues", "expenses": "Expenses"}, "journals": {"listJournals": "Journal Entries List", "create": "Add Journal", "journalNumber": "Journal Number", "journalType": "Journal Type", "journalCreationType": "Creation Method", "journalRef": "Journal", "date": "Date", "posted": "Posted", "editJournal": "Edit Journal", "viewJournal": "View Journal", "journalCreationDate": "Date", "description": "Notes", "description2": "Notes", "accountNumber": "Account Number", "accountArabicName": "Arabic Name", "accountEnglishName": "English Name", "costCenter": "Cost Center", "debitAmount": "Debit Amount", "creditAmount": "Credit Amount", "totalDebitAmount": "Total Debit", "totalCreditAmount": "Total Credit", "message": "Credit And Debit Amount Must Be Equal"}, "journalEntryDropDowns": {"normal": "Normal Journal", "opening": "Opening Journal", "operationClose": "Operation Close Journal", "tradeClose": "Trading Close Journal", "P/LClose": "Profit and Loss Close Journal", "incClose": "Net Income Close Journal"}, "journalEntryCreationTypesDropDowns": {"manual": "Manual", "automatic": "Automatic"}, "costCenter": {"view": "View Cost Center Details", "edit": "Edit Cost Center Details", "listings": "Cost Centers List", "costCentres": "Cost Centers", "create": "Add Cost Center", "accountNumber": "Cost Center Number", "arabicName": "Arabic Name", "englishName": "English Name"}, "accountSetupSales": {"accountsSetup": "Settings", "salesAccount": "Sales Settings", "cashSalesAccount": "Cash Sales Account", "creditSalesAccount": "Credit Sales Account", "cardSalesAccount": "Card Sales Account", "wireTransSalesAccount": "Wire Transfer Sales Account", "vatSalesAccount": "VAT Account - Sales", "discountSalesAccount": "Discount Allowed Account", "returnSalesAccount": "Sales Returns Account", "roundingFractionSalesAccount": "Rounding Fraction Account"}, "accountSetupPurchase": {"accountsSetup": "Settings", "purchaseAccount": "Purchase Settings", "cashPurchaseAccount": "Cash Purchases Account", "creditPurchaseAccount": "Credit Purchases Account", "cardPurchaseAccount": "Card Purchases Account", "wireTransPurchaseAccount": "Wire Transfer Purchases Account", "vatPurchaseAccount": "VAT Account - Purchases and Expenses", "discountPurchaseAccount": "Discount Earned Account", "returnPurchaseAccount": "Purchase Returns Account"}, "accountSetupTransfer": {"accountsSetup": "Settings", "transferAccount": "Transfer Settings", "transferInAccount": "Transfer In Account", "transferOutAccount": "Transfer Out Account", "transferInReturnAccount": "Transfer In Returns Account", "transferOutReturnAccount": "Transfer Out Returns Account"}, "accountSetupInventory": {"accountsSetup": "Settings", "inventoryAccount": "Inventory Settings", "badInventoryDebitAccount": "Bad Inventory Debit Account", "badInventoryCreditAccount": "Bad Inventory Credit Account", "shortageInventoryDebitAccount": "Inventory Shortage Debit Account", "shortageInventoryCreditAccount": "Inventory Shortage Credit Account", "surplusInventoryDebitAccount": "Inventory Surplus Debit Account", "surplusInventoryCreditAccount": "Inventory Surplus Credit Account", "costOfGoodsAccount": "Cost of Goods Account", "goodsEndAccount": "Ending Inventory Account"}, "header": {"mainProfile": "User Profile", "profile": "Permissions", "logout": "Logout"}, "accountListReport": {"businessGroup": "Business Group", "accountType": "Account Type", "fromAccount": "From Account", "toAccount": "To Account", "accountGroup": "Account Group"}, "reportSetup": {"branch": "Branch", "warehouse": "Warehouse", "qrText": "QR Text", "mainText1": "HEADER_TXT_1", "mainText2": "HEADER_TXT_2", "mainText3": "HEADER_TXT_3", "addrLine1": "ADDR_LINE_1", "addrLine2": "ADDR_LINE_2", "addrLine3": "ADDR_LINE_3", "subText1": "SUB_HEADER_TXT_1", "subText2": "SUB_HEADER_TXT_2", "subText3": "SUB_HEADER_TXT_3", "subAddrLine1": "SUB_ADDR_LINE_1", "subAddrLine2": "SUB_ADDR_LINE_2", "subAddrLine3": "SUB_ADDR_LINE_3", "showAdditionalFields": "Show Additional Fields", "configuration": "Report Configuration", "branchWarehouse": "Branch & Warehouse Selection", "mainHeader": "Main Header Information (Arabic)", "subHeader": "Sub Header Information (English)", "qrSection": "QR Code Section", "preview": "Live Preview", "previewNote": "This preview shows how your report header will appear. Changes are reflected in real-time.", "viewA4": "View A4 Size"}, "report": {"TotalSales": "Total Sales", "transactionType": "Transaction Tpe", "invoiceStatus": "Invoice Status", "documentNumber": "Document Number", "searchString": "Search String", "InventoryAgeing": "Inventory Ageing", "ChartOfAccounts": "Chart of Accounts", "AccountStatements": "Account Statements", "StockValue": "Stock Value", "ItemDetailed": "<PERSON>em Detailed", "Pricing": "Pricing", "ItemMovement": "Item Movement", "dateRange": "Date Range", "submit": "Submit", "dateFrom": "From Date", "dateTo": "To Date", "accountGroup": "Account Group", "businessGroup": "Business Group", "accountType": "Account Type", "fromAccount": "From Account", "toAccount": "To Account", "account": "Account", "report": "Report Type", "advanceSearch": "Advance search", "easySearch": "Easy search", "inventoryReportSearch": "Inventory Report Search", "accountingReportSearch": "Accounting Report Search", "warehouse": "Warehouse", "categories": "Category", "branch": "Branch", "units": "Unit", "itemCode": "<PERSON><PERSON>", "year": "Year", "TotalPurchase": "Total Purchase", "DetailedPurchase": "Detailed Purchase", "reportType": "Report Type", "selectReportType": "Select Report Type", "item": "<PERSON><PERSON>", "nameEnglish": "Name English", "nameArabic": "Name Arabic", "showDetailed": "Show Detailed", "outputFormat": "Output Format", "clear": "Clear"}, "accountStatReport": {"dateFrom": "From Date", "dateTo": "To Date", "accountGroup": "Account Group", "businessGroup": "Business Group", "accountType": "Account Type", "fromAccount": "From Account", "toAccount": "To Account", "account": "Account", "reportType": "Report Type", "advanceSearch": "Advance search", "easySearch": "Easy search", "inventoryReportSearch": "Inventory Report Search", "accountingReportSearch": "Accounting Report Search"}, "depreciation": {"listing": "Depreciation List", "depreciationCreate": "Add Asset Depreciation", "depreciationEdit": "Edit Asset Depreciation", "depreciationDetails": "View Asset Depreciation", "depreciationListings": "Depreciation List", "supplierName": "Supplier Name", "assetAccountId": "Asset Account Number", "assetLocation": "Asset Location", "deprecationMethod": "Depreciation Method", "search": "Search", "clear": "Clear", "action": "Action", "underAsset": "Asset Account Number", "cumDeprAccount": "Accumulated Depreciation Account", "deprExpAccount": "Depreciation Expense Account", "deprPercentage": "Depreciation Percentage", "deprMethod": "Depreciation Method", "straightLine": "Straight Line", "decliningBalance": "Declining Balance", "purchaseDate": "Purchase Date", "purchaseAmount": "Purchase Amount", "salvageValue": "Salvage Value", "notes": "Notes", "lastDepreciationDate": "Last Depreciation Date", "cumDepreciation": "Total Depreciation", "costAccountNo": "Cost Center Account Number"}, "depreciationMethodDropDown": {"straightLine": "Straight Line", "decliningBalance": "Declining Balance"}, "salesVoucher": {"totalInvoice": "Total Invoice Amount", "totalAmountPaid": "Total Amount Paid"}, "voucher": {"createVoucher": "Create Payment Voucher", "create": "New Voucher", "notes": "Notes", "paidAmount": "<PERSON><PERSON>", "distributor": "Distributor", "costCenter": "Cost Center", "name": "Name", "accountNo": "Account Number", "invoiceAmount": "Invoice Amount", "invoiceDate": "Invoice Date", "invoiceNo": "Invoice Number", "voucherDate": "Voucher Date", "fullPayment": "Full Payment", "salesListing": "Sales Invoices List", "salesReceivables": "Receivables Payment", "status": "Status", "dateRange": "Date Range", "startDate": "Start Date", "endDate": "End Date", "voucherStatus": "Payment Vouchers", "voucherNumber": "Voucher Number", "noVouchersCreated": "No Vouchers Created", "invoiceVouchersListings": "Payment Vouchers List", "costCentreId": "Cost Center", "distributorAccountId": "Distributor", "paymentMethod": "Payment Method", "voucherAmountError": "Amount exceeds invoice value", "voucherTotal": "Total"}, "salesStatusDropDown": {"notPaid": "Not Paid", "partiallyPaid": "Partially Paid", "fullyPaid": "<PERSON>y Paid", "allInvoices": "All"}, "priceReports": {"warehouse": "Warehouse", "category": "Category", "branch": "Branch", "unit": "Unit"}, "stockReports": {"warehouse": "Warehouse", "category": "Category", "branch": "Branch", "unit": "Unit"}, "profile": {"personalDetails": "User Details", "security": "Change Password", "firstName": "First Name", "lastName": "Last Name", "email": "Email", "birthdate": "Birthdate", "phone": "Phone", "language": "System Language", "password": "Password", "confirmPassword": "Confirm Password", "passwordsDoNotMatch": "Passwords do not match"}, "ucvoucher": {"listings": "List of Receipts and Payment Vouchers", "createReceipt": "New Receipt", "createPurchase": "New Voucher", "purchaseList": "List of Payment Vouchers", "receiveList": "List of Receipt Vouchers", "newPaymentVoucher": "New Payment Voucher", "newReceiptVoucher": "New Receipt Voucher", "ucvoucher": "Receipts and Payment Vouchers", "receipt": "Receipt", "payment": "Payment", "costCentreId": "Cost Centre", "distributorAccountId": "Distributor", "amount": "Amount", "issueType": "Issue Type", "notes": "Notes", "voucherTotal": "Voucher Amount", "voucherDate": "Voucher Date", "invoiceNo": "Invoice Number", "paidAmount": "Amount", "voucherNumber": "Voucher Number", "voucherType": "Voucher Type", "accountNo": "Account Number", "voucherAmount": "Voucher Amount"}, "issueTypeDropDown": {"general": "General", "vatexpenses": "VAT Expenses", "vatrecon": "VAT Reconciliation"}, "voucherTypesDisplay": {"0": "Sales Payment Voucher", "1": "Purchase Payment Voucher", "2": "Receipt Voucher", "3": "Payment Voucher"}, "stockTransfer": {"stock": "Stock Management", "create": "New Account", "configurePartner": "New Account", "branchParent": "Define Branch Accounts", "branchParentListings": "List of Branch Accounts", "partnerNameArabic": "Arabic Branch Name", "partnerNameEnglish": "English Branch Name", "partnerBranch": "For Branch", "inward": "Internal Branch", "outward": "External Branch", "issueDate": "Issue Date", "docno": "Transfer Number", "transferType": "External", "accno": "Account Number", "branchout": "Branch Transferred To", "branchin": "Branch Transferred From", "grandTotal": "Transfer Value", "outgoingStock": "List of Transfers", "outgoingStocktab": "Outgoing Transfers", "incomingStocktab": "Incoming Transfers", "transferCreate": "New Transfer", "stockOutward": "Outgoing Transfer", "stockInward": "Incoming Transfer", "crStocksalesDate": "Invoice Date", "crStockreferenceNo": "Reference Number", "crStockreferenceDate": "Reference Date", "crStockinvoiceNote": "Notes", "crStockbrselection": "Branch", "crStockstocktype": "Transfer Type", "crStockpaymentnote": "All transfers are considered deferred transfers", "wareHouse": "Warehouse", "accountEnglish": "English Account Name", "accountArabic": "Arabic Account Name", "branchArabic": "Arabic Branch Name", "branchEnglish": "English Branch Name"}, "identitymodes": {"SelectAll": "Select All", "Create": "Add", "Delete": "Delete", "Update": "Update", "View": "View", "AccountsManagement": "Accounting", "EnterpriseManagement": "System Settings", "IdentityManagement": "Permissions", "InventoryManagement": "Inventory", "ReportsManagement": "Reports", "TradeManagement": "Sales and Purchases", "PaymentsManagement": "Receipts and Payment Vouchers", "voucher": "xxx", "PriceUpdate": "Update Item Prices", "BulkEdit": "Update Item Additional Data", "CreateTRPA": "Define Branch Account", "ViewTRPA": "View Branch Account Data", "CreateTIO": "Add Outgoing/Incoming Transfer Document", "ViewTIO": "View Outgoing/Incoming Transfer Document", "CreateAdj": "Create New Inventory Document", "PostAdj": "Approve Specific Inventory Document", "PriceUpdateAdj": "Update Item Prices", "UpdateAdj": "Edit Specific Inventory Document", "ViewAdj": "View Specific Inventory Document", "CreateOQA": "Update Beginning Quantities", "CreateUPA": "Update Item Prices", "Accounts": "Chart of Accounts", "AccountStatement": "Specific Account Statement", "PriceReports": "Item Price Report", "StockValueReports": "Inventory Value Report", "ucvoucher": "Receipts and Payment Vouchers System", "Post": "Approve"}, "feature": {"Accounting": "Accounting Reports List", "Adjustments": "Quantity Inventory", "Categories": "Categories", "OpenQtyAdjustments": "Beginning Quantities", "Product": "Item Data", "Transfer": "Branch Transfers", "UnitPriceAdjustment": "Update Item Prices", "Units": "Units", "Role": "Roles", "User": "Users", "Branch": "Branches", "Company": "Company", "WareHouse": "Warehouses", "ChartOfAccounts": "Chart of Accounts", "CostCentre": "Cost Centres", "Depreciation": "Depreciation", "JournalEntries": "Accounting Entries", "Customer": "Customers", "Distributor": "Distributors", "Purchase": "Purchases", "Quotation": "Quotations", "Sales": "Sales", "Supplier": "Suppliers", "Inventory": "Inventory Reports List"}, "tenants": {"enterpriseType": "Enterprise Type", "tenantInfo": "Tenant Information", "tenantLists": "Tenant Listings", "tenantCreate": "Add Tenant", "tenantConfiguration": "Tenant Configuration", "id": "Tenant Id", "name": "Tenant Name", "regNo": "Registration No", "phone": "Phone Number", "email": "Email", "installDate": "Installation Date", "expDate": "Expiration Date", "contactNameManager": "Manager Contact Name", "active": "Active", "notes": "Notes", "maxBranch": "<PERSON>", "maxWareHouse": "Max Warehouses", "maxUsers": "Max Users", "storageLim": "Max Storage Limit", "vatNo": "VAT Number", "identification": "Identification", "identificationCode": "Identification Code"}, "zatcaInvoiceListings": {"documentNumber": "Document Number", "issueDate": "Document Date", "documentType": "Document Type", "invoiceType": "File Type", "status": "File Status", "reportToZatca": "Send File"}, "zatcaInvoiceStatus": {"STANDARD": "Tax Document", "SIMPLIFIED": "Simplified Document", "REJECTED": "Rejected", "CLEARED": "Approved", "REPORTED": "<PERSON><PERSON>", "EINVOICE_SAVED": "Created but Not Sent", "SALES": "Sales Invoice", "CREDIT_NOTE": "Credit Note", "DEBIT_NOTE": "Debit Note", "CLEARED_WITH_WARNINGS": "CLEARED_WITH_WARNINGS", "NOT_SUBMITTED": "NOT_SUBMITTED"}, "xmlViewer": {"download": "Download File", "xmlInvoice": "Invoice XML"}, "confirmationModal": {"add": "Add", "delete": "Delete", "update": "Update", "confirm": "Confirm", "confirmationText": "Are you sure", "logout": "Logout"}, "identificationcodes": {"CRN": "Commercial registration number", "MOM": "Momra license", "MLS": "MLSD license", "SAG": "Sagia license", "NAT": "National ID", "TIN": "Tax Identification Number", "IQA": "Iqama Number", "PAS": "Passport ID", "GCC": "GCC ID", "OTH": "Other ID"}, "inventoryReport": {"itemId": "Item Id", "itemCode": "Item Code", "nameArabic": "Name", "category": "Category", "unitName": "Unit Name", "unitBarcode": "Unit Barcode", "retailPrice": "Retail Price", "wholesalePrice": "Wholesale Price", "distributorPrice": "Distributor Price", "purchasePrice": "Purchase Price", "avgPurchasePrice": "Average Purchase Price", "openPurchasePrice": "Open Purchase Price", "warehouseName": "Warehouse Name", "currentQty": "Current Quantity", "nameEnglish": "Name", "quantityOfItemsOlderThan90days": "Items Agedd > 90 days", "quantityOfItemsAged61To90days": "Items Aged 61-90 days", "quantityOfItemsAged31To60days": "Items Aged 31-60 days", "quantityOfItemsAged1To30days": "Items Aged 1-30 days"}, "salesReport": {"invoiceNumber": "Invoice Number", "issueDate": "Issue Date", "grandTotal": "Grand Total", "customerPhoneNumber": "Customer Phone Number"}, "accountingReport": {"accountId": "Account Id", "branchId": "Branch Id", "accountNumber": "Account Number", "nameArabic": "Name", "nameEnglish": "Name", "accountType": "Account Type", "accountGroup": "Account Group", "businessGroup": "Business Group", "entryDate": "Journal Date", "debitAmount": "Debit Amount", "creditAmount": "Credit Amount", "balance": "Balance", "description": "Description"}, "accountColumns": {"BUDGET": "Budget", "OPERATION": "Operation", "TRADING": "Trading", "PROFITANDLOSS": "Profit and Loss", "INCOME": "Net Income", "DETAILED": "Detailed", "GENERAL": "General", "CASHIER": "Cashiers", "BANK": "Banks", "CUSTOMER": "Customers", "SUPPLIER": "Suppliers", "BRANCH": "Branch", "DISTRIBUTOR": "Distributors", "ASSETS": "Assets", "LIABILITIES": "Liabilities", "EQUITY": "Equity", "REVENUES": "Revenues", "EXPENSES": "Expenses"}, "itemReportingColumns": {"totalQuantityPerUnit": "Total Quantity Per Unit", "lastInventoryCheckDate": "Last Inventory Check Date", "avgPurchasePrice": "Avg. Purchase Price", "currentQty": "Current Quantity", "discount": "Discount", "distributorPrice": "Distributor Price", "itemQty": "Item Quantity", "openPurchasePrice": "Open Purchase Price", "openQty": "Open Quantity", "purchasePrice": "Purchase Price", "retailPrice": "Retail Price", "totalQuantity": "Total Quantity", "vat": "Vat", "wholesalePrice": "Wholesale Price", "factorRefUom": "factor", "category": "Category", "itemCode": "Item Code", "itemName": "Item Name", "nameArabic": "Name Arabic", "nameEnglish": "Name English", "partNumber": "Part Number", "unitBarcode": "Unit Barcode", "unitName": "Unit Name", "warehouseName": "Warehouse Name", "brand": "Brand", "parentCategory": "Parent Category", "color": "Color", "size": "Size", "maxQty": "Maximun Quantity", "minQty": "minimum Quantity", "costPrice": "Cost Prince", "dscntAmt": "Discount Amount", "dscntMthd": "Discount Method", "dscntPct": "Discount Percentage", "profit": "Profit", "profitPct": "Profit Percentage"}, "purchaseReport": {"invoiceNumber": "Invoice Number", "issueDate": "Issue Date", "grandTotal": "Grand Total", "customerPhoneNumber": "Customer Phone Number", "itemCode": "Item Code", "itemNameArabic": "Name", "itemNameEnglish": "Name", "unitName": "Unit", "documentNumber": "Document Number", "quantity": "Quantity", "price": "Price", "vat": "Vat"}, "purchaseTransactionTypes": {"purchase": "Purchase", "creditNote": "Credit Note"}}