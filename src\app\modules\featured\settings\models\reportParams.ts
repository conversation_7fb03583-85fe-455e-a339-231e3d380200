import { SaletransactionTypes } from 'src/app/core/interfaces/sales';

export class ReportParams {
  yearIds?: number[];
  branchId?: number[];
  warehouseId?: number[];
  categoryId?: number[];
  unitId?: number[];
  type?: string;
  searchString: string;
  searchType: string;
  showGrouped?: boolean;
  showDetailed?: boolean;
  reportType?: string;
  page: number;
  pageSize: number;
  templateId: number;
  sortFields: string[];
  sortDir: string;
  sortBy: string;
  yearId: string;
  itemId: string;
  date: string;
  dateFrom: string;
  dateTo: string;
  endPoint: string;
  jasperEndPoint: string;
}

export class AccountingReportParams {
  yearIds?: number[];
  branchId?: number[];
  warehouseId?: number[];
  categoryId?: number[];
  unitId?: number[];
  type?: string;
  searchString: string;
  searchType: string;
  showGrouped?: boolean;
  showDetailed?: boolean;
  reportType?: string;
  accountCategory?: string;
  accountSubCategory?: string;
  accountType?: string;
  startingAccountNo?: string;
  endingAccountNo?: string;
  startingDate?: any;
  endingDate?: any;
  page: number;
  pageSize: number;
  templateId: number;
  sortFields: string[];
  sortDir: string;
  sortBy: string;
  yearId?: string;
  date: string;
  dateFrom: string;
  dateTo: string;
  endPoint?: string;
  jasperEndPoint?: string;
}

export class SalesReportParams {
  yearIds?: number[];
  branchId?: number[];
  warehouseId?: number[];
  categoryId?: number[];
  type?: string;
  searchString: string;
  searchType: string;
  showGrouped?: boolean;
  showDetailed?: boolean;
  reportType?: string;
  page: number;
  pageSize: number;
  templateId: number;
  sortFields: string[];
  sortDir: string;
  sortBy: string;
  yearId?: string;
  date: string;
  // dateFrom: string;
  // dateTo: string;
  issueDateFrom?: string;
  issueDateTo?: string;
  endPoint?: string;
  settlementStatus?: string;
  invoiceStatus?: string;
  jasperEndPoint?: string;
  documentNumber?: string;
  transactionType?: string;
}

export class TransactionParams {
  transactionId?: number;
  transactionType?: string;
  type?: string;
  searchString: string;
  searchType: string;
  page: number;
  pageSize: number;
  sortFields: string[];
  sortDir: string;
  sortBy: string;
}

export class PurchaseReportParams {
  yearIds?: number[];
  branchId?: number[];
  warehouseId?: number[];
  categoryId?: number[];
  type?: string;
  searchString: string;
  searchType: string;
  showGrouped?: boolean;
  showDetailed?: boolean;
  reportType?: string;
  page: number;
  pageSize: number;
  templateId: number;
  sortFields: string[];
  sortDir: string;
  sortBy: string;
  yearId?: string;
  date: string;
  // dateFrom: string;
  // dateTo: string;
  issueDateFrom?: string;
  issueDateTo?: string;
  endPoint?: string;
  invoiceStatus?: string;
  jasperEndPoint?: string;
  documentNumber?: string;
  transactionType?: string;
  settlementStatus?: string;
}
