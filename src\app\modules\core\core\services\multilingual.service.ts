import { Directionality } from '@angular/cdk/bidi';
import { DOCUMENT } from '@angular/common';
import { Inject, Injectable, Renderer2, RendererFactory2 } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { AppSettings, defaults } from 'src/app/app.config';
import { LocalStorageService } from './local-storage.service';
import { TokenService } from './token.service';

interface LocalizedText {
  nameEnglish: string;
  nameArabic: string;
}

@Injectable({
  providedIn: 'root',
})
export class MultilingualService {
  private readonly renderer: Renderer2;
  private readonly languageSubject = new BehaviorSubject<string>('en');
  private options: AppSettings = { ...defaults };
  private readonly SUPPORTED_LANGUAGES = ['en', 'ar'];

  public readonly languageChanged$ = this.languageSubject.asObservable();

  constructor(
    private translateService: TranslateService,
    private localStorage: LocalStorageService,
    private directionality: Directionality,
    rendererFactory: RendererFactory2,
    @Inject(DOCUMENT) private document: Document,
    private tokenService: TokenService
  ) {
    this.renderer = rendererFactory.createRenderer(null, null);
    this.translateService.addLangs(this.SUPPORTED_LANGUAGES);
    this.translateService.setDefaultLang('en');
    this.translateService.onLangChange.subscribe(({ lang }) => {
      this.setDirection(lang === 'ar' ? 'rtl' : 'ltr');
      this.languageSubject.next(lang);
    });
  }

  loadDefaultLanguage(): Observable<string> {
    const lang =
      this.localStorage.getItem('preferredLanguage') ||
      this.tokenService.getLanguageLocale() ||
      'en';
    return this.setLanguage(lang);
  }

  setLanguage(lang: string, userToggled = false): Observable<string> {
    const validLang = this.SUPPORTED_LANGUAGES.includes(lang) ? lang : 'en';

    if (userToggled) {
      this.localStorage.setItem('preferredLanguage', validLang);
    }

    this.setDirection(validLang === 'ar' ? 'rtl' : 'ltr');
    this.languageSubject.next(validLang);
    return this.translateService.use(validLang);
  }

  private setDirection(dir: 'ltr' | 'rtl'): void {
    this.renderer.setAttribute(this.document.body, 'dir', dir);
    if (this.directionality.value !== dir) {
      (this.directionality as { value: 'ltr' | 'rtl' }).value = dir;
      this.directionality.change.emit(dir);
    }
  }

  getCurrentLanguage(): string {
    return this.translateService.currentLang || 'en';
  }

  getOptions(): AppSettings {
    return {
      ...this.options,
      language: this.getCurrentLanguage(),
      dir: this.getCurrentLanguage() === 'ar' ? 'rtl' : 'ltr',
    };
  }

  setOptions(options: AppSettings): void {
    this.options = { ...options };
  }

  updateDisplayedColumns(columns: string[]): string[] {
    const isArabic = this.getCurrentLanguage() === 'ar';
    return columns.filter(column =>
      isArabic ? column !== 'nameEnglish' : column !== 'nameArabic'
    );
  }

  updateDisplayedColumns1(columns: string[]): string[] {
    const isArabic = this.getCurrentLanguage() === 'ar';
    return columns.filter(column =>
      isArabic ? column !== 'description' : column !== 'description2'
    );
  }

  getLocalizedProperty(options: LocalizedText): string {
    return this.getCurrentLanguage() === 'ar' ? options.nameArabic : options.nameEnglish;
  }

  translate(key: string | string[], params?: Record<string, unknown>): Observable<string> {
    return this.translateService.get(key, params);
  }

  toggleLanguage(): Observable<string> {
    return this.setLanguage(this.getCurrentLanguage() === 'ar' ? 'en' : 'ar', true);
  }
}
