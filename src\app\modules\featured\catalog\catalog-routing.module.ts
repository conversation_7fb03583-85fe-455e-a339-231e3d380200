import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { PermissionGuard } from '../../core/core/guards/permission.guard';
import { CategoryFormComponent } from './components/category/category-form/category-form.component';
import { CategoryListComponent } from './components/category/category-list/category-list.component';
import { CatalogDashboardComponent } from './components/dashboard/catalog-dashboard.component';
import { ListProductsComponent } from './components/product/list-products/list-products.component';
import { ProductFormComponent } from './components/product/product-form/product-form.component';
import { UnitsFormComponent } from './components/units/unit-form/units-form.component';
import { UnitsComponent } from './components/units/units.component';

// Stock components
import { AdjustmentListComponent } from '../stock/components/adjustment-list/adjustment-list.component';
import { OpenQuantityAdjustmentsComponent } from '../stock/components/adjustments-open-qty/open-quantity-adjustments.component';
import { BranchPartnerListingComponent } from '../stock/components/branch-partner-listing/branch-partner-listing.component';
import { BranchPartnerComponent } from '../stock/components/branch-partner/branch-partner.component';
import { StockAdjustmentComponent } from '../stock/components/stock-adjustment-create/stock-adjustment.component';
import { StockTransferCreateObComponent } from '../stock/components/stock-transfer-create-ob/stock-transfer-create-ob.component';
import { StockTransferComponent } from '../stock/components/stock-transfer/stock-transfer.component';
import { StocksDashboardComponent } from '../stock/components/stocks-dashboard/stocks-dashboard.component';
import { UnitPriceUpdateComponent } from '../stock/components/unit-price-update/unit-price-update.component';

const config = {
  mainDashBoard: '/dashboard',
  stockDashBoard: '/inventory/dashboard',
  stockDashBoardTitle: 'Inventory Dashboard',
  stockAdjustmentDashBoard: '/inventory/stockadjustment',
  stockAdjustmentDashBoardTitle: '',
};

const routes: Routes = [
  {
    path: '',
    redirectTo: 'dashboard',
    pathMatch: 'full',
  },
  {
    path: 'dashboard',
    component: CatalogDashboardComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'Inventory Management',
      urls: [{ title: 'Main Dashboard', url: '/dashboard' }],
      allowedPermissions: ['InventoryManagement', 'AllPermissions'],
    },
  },
  {
    path: 'category',
    component: CategoryListComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'productCategory.listings',
      urls: [{ title: 'Inventory Dashboard', url: '/inventory/dashboard' }],
      allowedPermissions: ['Categories', 'AllPermissions'],
    },
  },
  {
    path: 'units',
    component: UnitsComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'productUnits.listing',
      urls: [{ title: 'Inventory Dashboard', url: '/inventory/dashboard' }],
      allowedPermissions: ['Units', 'AllPermissions'],
    },
  },
  {
    path: 'units/create',
    component: UnitsFormComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'productUnits.createUnit',
      urls: [{ title: 'Unit Listing', url: '/inventory/units' }],
      allowedPermissions: ['Units.Create', 'AllPermissions'],
    },
  },
  {
    path: 'units/edit/:id',
    component: UnitsFormComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'productUnits.edit',
      urls: [{ title: 'Unit Listing', url: '/inventory/units' }],
      allowedPermissions: ['Units.Update', 'AllPermissions'],
    },
  },
  {
    path: 'products',
    component: ListProductsComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'productBaicTab.listing',
      urls: [{ title: 'Inventory Dashboard', url: '/inventory/dashboard' }],
      allowedPermissions: ['Product', 'AllPermissions'],
    },
  },
  {
    path: 'products/create',
    component: ProductFormComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'productBaicTab.productConfiguration',
      urls: [{ title: 'Product Listing', url: '/inventory/products' }],
      allowedPermissions: ['Product.Create', 'AllPermissions'],
      mode: 'create',
    },
  },
  {
    path: 'products/edit/:id',
    component: ProductFormComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'Edit Product',
      urls: [{ title: 'Product Listing', url: '/inventory/products' }],
      allowedPermissions: ['Product.Update', 'AllPermissions'],
      mode: 'edit',
    },
  },
  {
    path: 'products/view/:id',
    component: ProductFormComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'View Product',
      urls: [{ title: 'Product Listing', url: '/inventory/products' }],
      allowedPermissions: ['Product.View', 'AllPermissions'],
      mode: 'view',
    },
  },
  {
    path: 'category/create',
    component: CategoryFormComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'productCategory.createCatgory',
      urls: [{ title: 'Category Listing', url: '/inventory/category' }],
      allowedPermissions: ['Categories.Create', 'AllPermissions'],
    },
  },
  {
    path: 'category/edit/:id',
    component: CategoryFormComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'productCategory.edit',
      urls: [{ title: 'Category Listing', url: '/inventory/category' }],
      allowedPermissions: ['Categories.Update', 'AllPermissions'],
    },
  },

  // Stock routes
  {
    path: 'stocks-dashboard',
    component: StocksDashboardComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'Stock Management',
      urls: [{ title: 'Main Dashboard', url: config.mainDashBoard }],
      allowedPermissions: ['InventoryManagement', 'AllPermissions'],
    },
  },
  {
    path: 'adjustments',
    component: OpenQuantityAdjustmentsComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'Adjust Open Quantity',
      urls: [{ title: config.stockDashBoardTitle, url: config.stockDashBoard }],
      allowedPermissions: ['OpenQtyAdjustments.CreateOQA', 'AllPermissions'],
    },
  },
  {
    path: 'stockadjustment/create',
    component: StockAdjustmentComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'Create Adjustment',
      urls: [{ title: 'Adjustments List', url: config.stockAdjustmentDashBoard }],
      allowedPermissions: ['Adjustments.CreateAdj', 'AllPermissions'],
    },
  },
  {
    path: 'stockadjustment',
    component: AdjustmentListComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'Adjustments List',
      urls: [{ title: config.stockDashBoardTitle, url: config.stockDashBoard }],
      allowedPermissions: ['Adjustments', 'AllPermissions'],
    },
  },
  {
    path: 'stockadjustment/edit/:id',
    component: StockAdjustmentComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'Edit Stock Adjustment',
      urls: [{ title: 'Adjustments List', url: config.stockAdjustmentDashBoard }],
      allowedPermissions: ['Adjustments.UpdateAdj', 'AllPermissions'],
    },
  },
  {
    path: 'stockadjustment/view/:id',
    component: StockAdjustmentComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'View Stock Adjustment',
      urls: [{ title: 'Adjustments List', url: config.stockAdjustmentDashBoard }],
      allowedPermissions: ['Adjustments.ViewAdj', 'AllPermissions'],
    },
  },
  {
    path: 'priceupdate',
    component: UnitPriceUpdateComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'Price Update Listing',
      urls: [{ title: config.stockDashBoardTitle, url: config.stockDashBoard }],
      allowedPermissions: ['Adjustments.PriceUpdateAdj', 'AllPermissions'],
    },
  },
  {
    path: 'branchpartner',
    component: BranchPartnerListingComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'stockTransfer.branchParentListings',
      urls: [{ title: config.stockDashBoardTitle, url: config.stockDashBoard }],
      allowedPermissions: ['Transfer.ViewTRPA', 'AllPermissions'],
    },
  },
  {
    path: 'branchpartner/create',
    component: BranchPartnerComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'stockTransfer.configurePartner',
      urls: [{ title: config.stockDashBoardTitle, url: config.stockDashBoard }],
      allowedPermissions: ['Transfer.CreateTRPA', 'AllPermissions'],
      mode: 'create',
    },
  },
  {
    path: 'branchpartner/view/:id',
    component: BranchPartnerComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'stockTransfer.branchParentListings',
      urls: [{ title: config.stockDashBoardTitle, url: config.stockDashBoard }],
      allowedPermissions: ['Transfer.ViewTRPA', 'AllPermissions'],
      mode: 'view',
    },
  },
  {
    path: 'transfer',
    component: StockTransferComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'stockTransfer.outgoingStock',
      urls: [{ title: config.stockDashBoardTitle, url: config.stockDashBoard }],
      allowedPermissions: ['Transfer.ViewTIO', 'AllPermissions'],
    },
  },
  {
    path: 'transfer/obtransfer',
    component: StockTransferCreateObComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'stockTransfer.stockOutward',
      urls: [{ title: config.stockDashBoardTitle, url: config.stockDashBoard }],
      allowedPermissions: ['Transfer.CreateTIO', 'AllPermissions'],
      mode: 'create',
      transferMode: 'Outward',
    },
  },
  {
    path: 'transfer/obtransfer/view/:id',
    component: StockTransferCreateObComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'stockTransfer.stockOutward',
      urls: [{ title: config.stockDashBoardTitle, url: config.stockDashBoard }],
      allowedPermissions: ['Transfer.ViewTIO', 'AllPermissions'],
      mode: 'view',
      transferMode: 'Outward',
    },
  },
  {
    path: 'transfer/ibtransfer',
    component: StockTransferCreateObComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'stockTransfer.stockInward',
      urls: [{ title: config.stockDashBoardTitle, url: config.stockDashBoard }],
      allowedPermissions: ['Transfer.CreateTIO', 'AllPermissions'],
      mode: 'create',
      transferMode: 'Inward',
    },
  },
  {
    path: 'transfer/ibtransfer/view/:id',
    component: StockTransferCreateObComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'stockTransfer.stockInward',
      urls: [{ title: config.stockDashBoardTitle, url: config.stockDashBoard }],
      allowedPermissions: ['Transfer.ViewTIO', 'AllPermissions'],
      mode: 'view',
      transferMode: 'Inward',
    },
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class CatalogRoutingModule {}
