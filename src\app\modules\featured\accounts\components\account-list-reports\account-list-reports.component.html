<ng-container class="advancedSearch" *ngIf="!isLoading">
  <mat-card appearance="outlined">
    <form [formGroup]="filterForm" autocomplete="off">
      <div class="row no-gutters">
        <div class="p-2 col-md-5">
          <!-- main search box panel -->
          <app-accounts-search-box
            #searchBoxForm
            formControlName="searchBoxForm"></app-accounts-search-box>
        </div>
        <div class="p-2 col-lg-2 col-md-2 col-sm-3 d-flex align-items-end">
          <div class="form-group">
            <mat-label>{{ 'accountListReport.fromAccount' | translate }}</mat-label>
            <mat-form-field class="w-100">
              <input
                class="toolbar-search"
                type="text"
                matInput
                formControlName="startingAccountNo" />
            </mat-form-field>
          </div>
        </div>
        <div class="p-2 col-lg-2 col-md-2 col-sm-3 d-flex align-items-end">
          <div class="form-group">
            <mat-label>{{ 'accountListReport.toAccount' | translate }}</mat-label>
            <mat-form-field class="w-100">
              <input
                class="toolbar-search"
                type="text"
                matInput
                formControlName="endingAccountNo" />
            </mat-form-field>
          </div>
        </div>
        <div class="p-2 col-lg-2 col-md-2 col-sm-3 d-flex align-items-end">
          <div class="form-group">
            <mat-label>{{ 'accountListReport.accountCategory' | translate }}</mat-label>
            <mat-form-field class="w-100">
              <mat-select formControlName="accountCategory">
                <mat-option *ngFor="let group of accountCategorys" [value]="group.value">
                  {{ group.display | translate }}
                </mat-option>
              </mat-select>
            </mat-form-field>
          </div>
        </div>
        <div class="p-2 col-lg-2 col-md-2 col-sm-3 d-flex align-items-end">
          <div class="form-group">
            <mat-label>{{ 'accountListReport.accountSubCategory' | translate }}</mat-label>

            <mat-form-field class="w-100">
              <mat-select formControlName="accountSubCategory">
                <mat-option *ngFor="let bGroup of accountSubCategorys" [value]="bGroup.value">
                  {{ bGroup.display | translate }}
                </mat-option>
              </mat-select>
            </mat-form-field>
          </div>
        </div>
        <div class="p-2 col-lg-2 col-md-2 col-sm-3 d-flex align-items-end">
          <div class="form-group">
            <mat-label>{{ 'accountListReport.accountType' | translate }}</mat-label>

            <mat-form-field class="w-100">
              <mat-select formControlName="accountType">
                <mat-option *ngFor="let actType of accountTypes" [value]="actType.value">
                  {{ actType.display | translate }}
                </mat-option>
              </mat-select>
            </mat-form-field>
          </div>
        </div>
      </div>
      <div class="row no-gutters">
        <div class="p-2 col-lg-3 col-md-3 col-sm-5">
          <mat-radio-group aria-label="Select an option" formControlName="reportType">
            <mat-radio-button color="primary" value="PDF">PDF</mat-radio-button>
            <mat-radio-button color="primary" value="XLS">XLS</mat-radio-button>
            <mat-radio-button color="primary" value="CSV">CSV</mat-radio-button>
          </mat-radio-group>
        </div>
      </div>

      <ng-container>
        <button (click)="getReport($event); (false)" mat-stroked-button color="primary">
          {{ 'searchPanel.searchString' | translate }}
        </button>
        <button class="m-l-10" (click)="clearFilters($event); (false)" mat-stroked-button>
          {{ 'searchPanel.clear' | translate }}
        </button>
      </ng-container>
    </form>
  </mat-card>
</ng-container>
