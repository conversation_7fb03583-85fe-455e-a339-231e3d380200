import { Component, EventEmitter, Input, Output, OnChanges, SimpleChanges } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { SearchConfig } from '../../models/report-template';

export interface ActiveFilter {
  id: string;
  label: string;
  value: string | string[];
  displayValue: string;
  type: 'single' | 'multiple';
  config: SearchConfig;
  removable: boolean;
}

export interface FilterGroup {
  name: string;
  filters: ActiveFilter[];
  icon?: string;
}

@Component({
  selector: 'app-filter-summary',
  templateUrl: './filter-summary.component.html',
  styleUrls: ['./filter-summary.component.scss'],
})
export class FilterSummaryComponent implements OnChanges {
  @Input() form: FormGroup;
  @Input() searchConfigs: SearchConfig[] = [];
  @Input() getListForField: (field: string) => unknown[];
  @Input() collapsed = false;
  @Input() showGrouped = true;
  @Input() maxVisibleFilters = 10;

  @Output() filterRemoved = new EventEmitter<string>();
  @Output() filterClicked = new EventEmitter<ActiveFilter>();
  @Output() clearAllFilters = new EventEmitter<void>();
  @Output() toggleCollapsed = new EventEmitter<boolean>();

  activeFilters: ActiveFilter[] = [];
  filterGroups: FilterGroup[] = [];
  hasFilters = false;
  isCollapsed = false;

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['form'] || changes['searchConfigs']) {
      this.updateActiveFilters();
    }
    if (changes['collapsed']) {
      this.isCollapsed = this.collapsed;
    }
  }

  /**
   * Extract and process active filters from the form
   */
  private updateActiveFilters(): void {
    if (!this.form || !this.searchConfigs?.length) {
      this.activeFilters = [];
      this.filterGroups = [];
      this.hasFilters = false;
      return;
    }

    this.activeFilters = this.extractActiveFilters();
    this.filterGroups = this.groupFilters(this.activeFilters);
    this.hasFilters = this.activeFilters.length > 0;
  }

  /**
   * Extract active filters from form controls
   */
  private extractActiveFilters(): ActiveFilter[] {
    const filters: ActiveFilter[] = [];

    this.searchConfigs.forEach(config => {
      const control = this.form.get(config.backendParam);
      if (!control?.value) return;

      const value = control.value;
      const displayValue = this.getDisplayValue(value, config);

      if (displayValue) {
        filters.push({
          id: config.backendParam,
          label: config.fieldLabel,
          value: value,
          displayValue: displayValue,
          type: Array.isArray(value) ? 'multiple' : 'single',
          config: config,
          removable: !config.mandatory,
        });
      }
    });

    // Handle date range filters separately
    this.searchConfigs.forEach(config => {
      if (config.type === 'dateRange') {
        const fromControl = this.form.get(config.backendParam + 'From');
        const toControl = this.form.get(config.backendParam + 'To');

        if (fromControl?.value || toControl?.value) {
          const displayValue = this.getDateRangeDisplayValue(fromControl?.value, toControl?.value);

          if (displayValue) {
            filters.push({
              id: config.backendParam + 'Range',
              label: config.fieldLabel + ' Range',
              value: [fromControl?.value, toControl?.value],
              displayValue: displayValue,
              type: 'multiple',
              config: config,
              removable: !config.mandatory,
            });
          }
        }
      }
    });

    return filters.sort((a, b) => a.label.localeCompare(b.label));
  }

  /**
   * Get human-readable display value for filter
   */
  private getDisplayValue(value: unknown, config: SearchConfig): string {
    if (!value) return '';

    if (Array.isArray(value)) {
      if (value.length === 0) return '';

      const items = this.getListForField?.(config.backendParam) || [];
      const displayValues = value.map(v => this.getItemDisplayName(v, items, config));

      if (displayValues.length > 3) {
        return `${displayValues.slice(0, 2).join(', ')} +${displayValues.length - 2} more`;
      }
      return displayValues.join(', ');
    }

    // Single value
    const items = this.getListForField?.(config.backendParam) || [];
    return this.getItemDisplayName(value, items, config);
  }

  /**
   * Get display name for a single item
   */
  private getItemDisplayName(value: unknown, items: unknown[], config: SearchConfig): string {
    const item = items.find(
      i => i[config.idField] === value || (i as any).value === value || (i as any).id === value
    );

    if (item) {
      return (item as any).name || (item as any).year || (item as any).display || String(value);
    }

    return String(value);
  }

  /**
   * Get display value for date range
   */
  private getDateRangeDisplayValue(fromDate: unknown, toDate: unknown): string {
    const formatDate = (date: unknown) => {
      if (!date) return '';
      const d = new Date(date as string);
      return d.toLocaleDateString();
    };

    const from = formatDate(fromDate);
    const to = formatDate(toDate);

    if (from && to) {
      return `${from} - ${to}`;
    } else if (from) {
      return `From ${from}`;
    } else if (to) {
      return `Until ${to}`;
    }

    return '';
  }

  /**
   * Group filters by category for better organization
   */
  private groupFilters(filters: ActiveFilter[]): FilterGroup[] {
    const groups = new Map<string, ActiveFilter[]>();

    filters.forEach(filter => {
      const groupName = this.getFilterGroupName(filter.config.type);
      if (!groups.has(groupName)) {
        groups.set(groupName, []);
      }
      groups.get(groupName)!.push(filter);
    });

    return Array.from(groups.entries()).map(([name, filters]) => ({
      name,
      filters,
      icon: this.getGroupIcon(name),
    }));
  }

  /**
   * Get group name based on filter type
   */
  private getFilterGroupName(type: string): string {
    switch (type) {
      case 'dropdown':
      case 'multiSelectDropdown':
        return 'Selections';
      case 'dateRange':
      case 'date':
        return 'Dates';
      case 'input':
        return 'Search';
      case 'accountSearch':
        return 'Accounts';
      default:
        return 'Other';
    }
  }

  /**
   * Get icon for filter group
   */
  private getGroupIcon(groupName: string): string {
    switch (groupName) {
      case 'Selections':
        return 'list';
      case 'Dates':
        return 'calendar_today';
      case 'Search':
        return 'search';
      case 'Accounts':
        return 'account_circle';
      default:
        return 'filter_list';
    }
  }

  /**
   * Remove a specific filter
   */
  onRemoveFilter(filter: ActiveFilter): void {
    if (!filter.removable) return;

    if (filter.config.type === 'dateRange') {
      // Clear both date range controls
      this.form.get(filter.config.backendParam + 'From')?.setValue(null);
      this.form.get(filter.config.backendParam + 'To')?.setValue(null);
    } else {
      // Clear the specific control
      this.form.get(filter.id)?.setValue(null);
    }

    this.filterRemoved.emit(filter.id);
    this.updateActiveFilters();
  }

  /**
   * Handle filter click for editing
   */
  onFilterClick(filter: ActiveFilter): void {
    this.filterClicked.emit(filter);
  }

  /**
   * Clear all removable filters
   */
  onClearAll(): void {
    this.activeFilters
      .filter(filter => filter.removable)
      .forEach(filter => {
        if (filter.config.type === 'dateRange') {
          this.form.get(filter.config.backendParam + 'From')?.setValue(null);
          this.form.get(filter.config.backendParam + 'To')?.setValue(null);
        } else {
          this.form.get(filter.id)?.setValue(null);
        }
      });

    this.clearAllFilters.emit();
    this.updateActiveFilters();
  }

  /**
   * Toggle collapsed state
   */
  onToggleCollapsed(): void {
    this.isCollapsed = !this.isCollapsed;
    this.toggleCollapsed.emit(this.isCollapsed);
  }

  /**
   * Get visible filters (for collapsed view)
   */
  get visibleFilters(): ActiveFilter[] {
    if (!this.isCollapsed) {
      return this.activeFilters;
    }
    return this.activeFilters.slice(0, this.maxVisibleFilters);
  }

  /**
   * Get hidden filter count
   */
  get hiddenFilterCount(): number {
    if (!this.isCollapsed) {
      return 0;
    }
    return Math.max(0, this.activeFilters.length - this.maxVisibleFilters);
  }

  /**
   * Check if filter has multiple values
   */
  isMultipleValue(filter: ActiveFilter): boolean {
    return filter.type === 'multiple' && Array.isArray(filter.value);
  }

  /**
   * Get chip color based on filter type
   */
  getChipColor(filter: ActiveFilter): string {
    switch (filter.config.type) {
      case 'dropdown':
      case 'multiSelectDropdown':
        return 'primary';
      case 'dateRange':
        return 'accent';
      case 'input':
        return 'warn';
      default:
        return '';
    }
  }
}
