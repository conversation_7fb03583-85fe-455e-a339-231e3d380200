<app-dialog-header></app-dialog-header>
<ng-container *ngIf="!loading">
  <form [formGroup]="chartOfAccountsForm" autocomplete="off">
    <mat-card appearance="outlined">
      <mat-card-title>{{ formTitle | translate }}</mat-card-title>
      <div class="row no-gutters">
        <div class="col-md-3 col-lg-3 col-sm-4 p-2">
          <mat-label>{{ 'caccounts.accountCategory' | translate }}</mat-label>

          <mat-form-field class="w-100">
            <mat-select formControlName="accountCategory">
              <mat-option *ngFor="let group of accountCategorys" [value]="group.value">
                {{ group.display | translate }}
              </mat-option>
            </mat-select>
            <mat-error
              *ngIf="
                chartOfAccountsForm?.controls['accountCategory']?.hasError('required') &&
                chartOfAccountsForm?.controls['accountCategory']?.touched
              "
              >{{ 'common.required' | translate }}</mat-error
            >
          </mat-form-field>
        </div>
        <div class="col-md-3 col-lg-3 col-sm-4 p-2">
          <mat-label>{{ 'caccounts.subCategory' | translate }}</mat-label>

          <mat-form-field class="w-100">
            <mat-select formControlName="accountSubCategory">
              <mat-option *ngFor="let bGroup of accountSubCategorys" [value]="bGroup.value">
                {{ bGroup.display | translate }}
              </mat-option>
            </mat-select>
            <mat-error
              *ngIf="
                chartOfAccountsForm?.controls['accountSubCategory']?.hasError('required') &&
                chartOfAccountsForm?.controls['accountSubCategory']?.touched
              "
              >{{ 'common.required' | translate }}</mat-error
            >
          </mat-form-field>
        </div>
        <div class="col-md-3 col-lg-3 col-sm-4 p-2">
          <mat-label>{{ 'caccounts.parentAccount' | translate }}</mat-label>

          <mat-form-field class="w-100">
            <mat-select formControlName="parentAccountId">
              <mat-option
                *ngFor="let parentAccount of parentAccounts"
                [value]="parentAccount.accountId">
                {{ parentAccount | localized }}
              </mat-option>
            </mat-select>
          </mat-form-field>
        </div>

        <div class="col-md-3 col-lg-3 col-sm-4 p-2">
          <mat-label>{{ 'caccounts.accountType' | translate }}</mat-label>

          <mat-form-field class="w-100">
            <mat-select formControlName="accountType">
              <mat-option *ngFor="let type of accountTypes" [value]="type.value">
                {{ type.display | translate }}
              </mat-option>
            </mat-select>
            <mat-error
              *ngIf="
                chartOfAccountsForm?.controls['accountType']?.hasError('required') &&
                chartOfAccountsForm?.controls['accountType']?.touched
              "
              >{{ 'common.required' | translate }}</mat-error
            >
          </mat-form-field>
        </div>
        <div class="col-md-6 col-sm-6 p-2">
          <mat-label>{{
            isArabic
              ? ('caccounts.accountArabicName' | translate)
              : ('caccounts.accountEnglishName' | translate)
          }}</mat-label>
          <mat-form-field class="w-100">
            <input
              [formControlName]="isArabic ? 'nameArabic' : 'nameEnglish'"
              type="text"
              maxlength="40"
              matInput />
            <i-tabler
              class="icon-16"
              (click)="provideOptionalName()"
              matPrefix
              name="world"></i-tabler>
            <mat-error
              *ngIf="
                isArabic
                  ? chartOfAccountsForm?.controls['nameArabic'].hasError('required') &&
                    chartOfAccountsForm?.controls['nameArabic'].touched
                  : chartOfAccountsForm?.controls['nameEnglish'].hasError('required') &&
                    chartOfAccountsForm?.controls['nameEnglish'].touched
              "
              >{{ 'common.required' | translate }}</mat-error
            >
          </mat-form-field>
        </div>
        <div class="col-md-6 col-sm-6 p-2" *ngIf="addOptionalName">
          <mat-label>{{
            isArabic
              ? ('caccounts.accountEnglishName' | translate)
              : ('caccounts.accountArabicName' | translate)
          }}</mat-label>
          <mat-form-field class="w-100">
            <input
              [formControlName]="isArabic ? 'nameEnglish' : 'nameArabic'"
              type="text"
              maxlength="40"
              matInput />
          </mat-form-field>
        </div>
        <div class="col-md-3 col-lg-3 col-sm-4 p-2" id="account-costAccountNumber">
          <mat-label>{{ 'caccounts.costAccount' | translate }}</mat-label>

          <mat-form-field class="w-100">
            <mat-select formControlName="costCentreId">
              <mat-option
                *ngFor="let costCentre of costCentreList"
                [value]="costCentre.costCentreId">
                {{
                  costCentre.accountNumber + '-' + accountSearchComponent.getAccountName(costCentre)
                }}
              </mat-option>
            </mat-select>
          </mat-form-field>
        </div>
        <div class="col-md-3 col-lg-3 col-sm-4 p-2">
          <mat-label>{{ 'caccounts.financialStatementType' | translate }}</mat-label>

          <mat-form-field class="w-100">
            <mat-select formControlName="financialStatementType">
              <mat-option *ngFor="let faGroup of financialStatementTypes" [value]="faGroup.value">
                {{ faGroup.display | translate }}
              </mat-option>
            </mat-select>
          </mat-form-field>
        </div>
        <div class="col-md-3 col-lg-3 col-sm-4 p-2" id="account-note">
          <mat-label>{{ 'caccounts.notes' | translate }}</mat-label>

          <mat-form-field class="w-100">
            <textarea
              #autosize="cdkTextareaAutosize"
              maxlength="200"
              type="text"
              matInput
              formControlName="note"
              cdkTextareaAutosize
              cdkAutosizeMaxRows="8"></textarea>
          </mat-form-field>
        </div>
      </div>
      <div class="row no-gutters">
        <div class="col-md-3 col-lg-3 col-sm-4 p-2">
          <mat-slide-toggle [formControl]="chartOfAccountsForm?.controls['isContraAccount']">{{
            'caccounts.contraAccount' | translate
          }}</mat-slide-toggle>
        </div>
        <div class="col-md-3 col-lg-3 col-sm-4 p-2">
          <mat-slide-toggle [formControl]="chartOfAccountsForm?.controls['isDepreciating']">{{
            'caccounts.depreciating' | translate
          }}</mat-slide-toggle>
        </div>
        <div class="col-md-3 col-lg-3 col-sm-4 p-2">
          <mat-slide-toggle [formControl]="chartOfAccountsForm?.controls['isHidden']">{{
            'caccounts.hidden' | translate
          }}</mat-slide-toggle>
        </div>
        <div class="col-md-3 col-lg-3 col-sm-4 p-2">
          <mat-slide-toggle [formControl]="chartOfAccountsForm?.controls['isFreezed']">{{
            'caccounts.frozen' | translate
          }}</mat-slide-toggle>
        </div>
      </div>
    </mat-card>
  </form>
  <div class="text-center">
    <button
      class="m-l-10"
      *ngIf="isCreateMode"
      (click)="onSubmit($event)"
      mat-stroked-button
      color="primary">
      {{ 'common.submit' | translate }}
    </button>
    <button
      class="m-l-10"
      *ngIf="isEditMode"
      (click)="onSubmit($event)"
      mat-stroked-button
      color="primary">
      {{ 'common.submit' | translate }}
    </button>
    <button
      class="m-l-10"
      *ngIf="isCreateMode"
      [routerLink]="['../']"
      mat-stroked-button
      color="warn">
      {{ 'common.cancel' | translate }}
    </button>
    <button
      class="m-l-10"
      *ngIf="isEditMode"
      [routerLink]="['../../']"
      mat-stroked-button
      color="warn">
      {{ 'common.cancel' | translate }}
    </button>
    <button
      class="m-l-10"
      *ngIf="isViewMode"
      [routerLink]="['../../']"
      type="button"
      mat-stroked-button
      color="primary">
      {{ 'common.cancel' | translate }}
    </button>
  </div>
</ng-container>
