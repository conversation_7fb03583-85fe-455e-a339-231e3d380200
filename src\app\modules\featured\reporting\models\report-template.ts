export interface SearchConfig {
  field: string;
  type: 'dropdown' | 'input' | 'multiSelectDropdown' | 'dateRange' | 'accountSearch';
  mandatory: boolean;
  placeholder: string;
  position: number;
  isAdvanced: boolean;
  fieldLabel: string;
  backendParam: string;
  idField: string;
  // Enhanced CSS configuration
  cssClasses?: ControlCssConfig;
}

export interface ControlCssConfig {
  // Primary size class (required)
  size: 'control-small' | 'control-medium' | 'control-large' | 'control-full' | 'control-auto';
  // Additional modifier classes (optional)
  modifiers?: string[];
  // Custom CSS classes (optional)
  custom?: string[];
  // Responsive behavior (optional)
  responsive?: {
    mobile?: string;
    tablet?: string;
    desktop?: string;
  };
}

export interface Report {
  id: number;
  type: number;
  name: string;
  nameArabic: string | null;
  authority: string;
  languageCode: string;
  searchConfigs: SearchConfig[];
  endPoint: string | null;
  jasperEndPoint: string | null;
}

export interface ReportGroup {
  parentReportGroup: string;
  reports: Report[];
}

export type ReportData = ReportGroup[];
