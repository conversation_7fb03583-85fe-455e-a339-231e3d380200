import { AfterViewInit, Component, Input, OnInit } from '@angular/core';
import { ReportInvoice } from './invoice.model';
import { toWords } from 'number-to-words';
import { toArabicWord } from 'number-to-arabic-words/dist/index-node.js';

@Component({
  selector: 'app-invoice-template',
  templateUrl: './invoice-template.component.html',
  styleUrls: ['./invoice-template.component.scss'],
})
export class InvoiceTemplateComponent implements OnInit, AfterViewInit {
  @Input() invoice: ReportInvoice;
  @Input() companyLogo = 'assets/images/sawami_logo.png';
  @Input() companyName = 'Sawami Information Technology';
  @Input() companyAddress = '1234 King Fahd Road - Olaya District - Riyadh 12345';
  @Input() companyTower = 'Tamaniya - South Tower - Floor 16';
  @Input() companyPOBox = 'PO Box 12345 Riyadh 12345';
  @Input() companyPhone = '966111111111';
  @Input() companyVatNumber = '123456789012345';

  currentDate: Date = new Date();
  totalInWords = '';
  arabicTotalInWords = '';

  // Default template header for when no invoice is provided
  defaultTemplateHeader = {
    mainText1: 'موسسة سوامي لتقنية المعلومات',
    mainText2: 'فرع شمال العليا',
    mainText3: 'تطوير برامج - اجهزة - حلول واستشارات',
    contactLine1: 'الرقم الضريبي 300000000000003 السجل التجاري 1234567890',
    contactLine2: 'هاتف :0114793132 ********* *********',
    contactLine3:
      '1234 الشارع الشمالي - شمال العليا -الرياض - 4548-12345 المملكة العربية السعودية 7176',
    optionalText1: 'Sawami Information Technology',
    optionalText2: 'Olaya North Branch',
    optionalText3: 'Application Development, Hardware,Technical Consultation',
    optionalContactLine1: 'Vat 300000000000003 / CR 1234567890',
    optionalContactLine2: 'Tel : 0114793132 ********* *********',
    optionalContactLine3: '1234,North Street1, Olaya North Side ,Riyadh,4548-12345 KSA 7176',
    warehouseId: null,
    qrText: null, // Will use default image if no QR text provided
  };

  ngOnInit(): void {
    // Initialize default invoice if none is provided
    if (!this.invoice) {
      this.invoice = {
        templateStaticData: this.defaultTemplateHeader,
        items: [],
        customer: null,
        payments: {
          grandTotal: 0,
          totalVat: 0,
          totalDiscount: 0,
          totalExclVatDiscount: 0,
        },
      } as ReportInvoice;
    }
    // for test
    // this.invoice.qrInvoice =
    //  'ASvYtNix2YPYqSDYudix2YjZgiDYp9mE2KzZiNiyINmE2YTYqtis2KfYsdipAg8zMTEyNzI3OTc0MDAwMDMDEzIwMjUtMDQtMjhUMTA6NTM6MTYEBjc5My41MAUGMTAzLjUwBix4dnFiYkZhRnFFZ2N6NjMyVDMrZUJZcDZ1N2ljU1dtYWVBWVk4dVdINjh3PQdgTUVZQ0lRRFAzdEtGMnBwa0VUVXZWM0JUZHl3M0VIWmNTaWJlZnBlbzhZdmVrS0FsdndJaEFQaUlybFg5cndZWWpsVVZ1OTY3Rmd5aGJ2UGViSmdLOU00VFd0V1BDWWZhCFgwVjAQBgcqhkjOPQIBBgUrgQQACgNCAASZpBxPf6LLVBgr2jdd2CMFSO7bh2fULk2wSIz2f69MyTlUxesx5L6gPJPR3EC/KlQH2iO2PG0Vkk5es1nWHAPh';
  }

  ngAfterViewInit(): void {
    this.generateTotalInWords();
  }

  grantTotal(): number {
    if (!this.invoice || !this.invoice.payments) return 0;
    const total =
      this.invoice.payments.totalExclVatDiscount + (this.invoice?.payments?.totalVat || 0);
    return total.toFixed(2) ? parseFloat(total.toFixed(2)) : 0;
  }

  /**
   * Formats customer address, returns '-' if no valid address data
   */
  getFormattedAddress(): string {
    if (!this.invoice?.customer?.address) {
      return '-';
    }

    const addr = this.invoice.customer.address;
    const parts = [
      addr.buildingNumber,
      addr.streetName,
      addr.district,
      addr.city,
      addr.country,
      addr.postalCode,
      addr.address2,
    ].filter(part => part && part.trim() !== '' && part !== '0');

    return parts.length > 0 ? parts.join(', ') : '-';
  }

  /**
   * Formats phone and email, returns '-' if no valid data
   */
  getFormattedPhoneEmail(): string {
    const phone = this.invoice?.customer?.phoneNumber;
    const email = this.invoice?.customer?.emailId;

    const parts = [];
    if (phone && phone.trim() !== '') parts.push(phone);
    if (email && email.trim() !== '') parts.push(email);

    return parts.length > 0 ? parts.join(' | ') : '-';
  }

  /**
   * Formats VAT number and identification, returns '-' if no valid data
   */
  getFormattedVatIdentification(): string {
    const vatNumber = this.invoice?.customer?.vatNumber;
    const identificationCode = this.invoice?.customer?.identificationCode;
    const identification = this.invoice?.customer?.identification;

    const parts = [];
    if (vatNumber && vatNumber.trim() !== '') parts.push(vatNumber);

    if (identificationCode && identification) {
      parts.push(`${identificationCode}-${identification}`);
    } else if (identificationCode && identificationCode.trim() !== '') {
      parts.push(identificationCode);
    } else if (identification && identification.trim() !== '') {
      parts.push(identification);
    }

    return parts.length > 0 ? parts.join(' | ') : '-';
  }

  /**
   * Formats distributor name (Arabic and English), returns '-' if no valid data
   */
  getFormattedDistributorName(): string {
    const distributorArabic = this.invoice?.distributorNameArabic;
    const distributorEnglish = this.invoice?.distributorNameEnglish;

    const parts = [];
    if (distributorArabic && distributorArabic.trim() !== '') parts.push(distributorArabic);
    if (distributorEnglish && distributorEnglish.trim() !== '') parts.push(distributorEnglish);

    return parts.length > 0 ? parts.join(' ') : '-';
  }

  private generateTotalInWords(): void {
    this.totalInWords = '';
    this.arabicTotalInWords = '';

    if (!this.invoice || !this.invoice.payments) return;

    try {
      const grandTotal = this.grantTotal();

      // Validate that we have a valid number
      if (isNaN(grandTotal) || !isFinite(grandTotal) || grandTotal <= 0) {
        console.log('Invalid grand total:', grandTotal);
        return;
      }

      // Format to 2 decimal places and convert to string
      const formattedTotal = grandTotal.toFixed(2);
      console.log('Formatted total:', formattedTotal);

      // Split at decimal point
      const [integerPart, decimalPart] = formattedTotal.split('.');

      // Ensure we have valid integer part
      if (!integerPart || isNaN(parseInt(integerPart))) {
        console.log('Invalid integer part:', integerPart);
        return;
      }

      // Convert integer part to words
      let words = '';
      try {
        words = toWords(parseInt(integerPart));
        // Capitalize first letter
        words = words.charAt(0).toUpperCase() + words.slice(1);
      } catch (error) {
        console.error('Error converting integer to words:', error);
        return;
      }

      // If decimal part exists and is not zero, add it
      if (decimalPart && parseInt(decimalPart) > 0) {
        words += ` Riyals and ${decimalPart} Halalas`;
      }

      this.totalInWords = `${words}`;
      this.arabicTotalInWords = this.convertNumberToArabicWords(grandTotal);
    } catch (error) {
      console.error('Error in generateTotalInWords:', error);
    }
  }

  convertNumberToArabicWords(num: number): string {
    const [integerPart, decimalPart] = num.toString().split('.');

    let words = toArabicWord(integerPart);

    if (decimalPart) {
      words += ' و  ' + toArabicWord(decimalPart) + ' هلله';
    }

    return words;
  }

  getPaymentTypeText(payments: {
    cashType?: boolean;
    bankType?: boolean;
    cardType?: boolean;
    creditType?: boolean;
  }): string {
    if (!payments) return '';

    const paymentTypes = [];

    if (payments.cashType) {
      paymentTypes.push('Cash نقدا');
    }

    if (payments.bankType) {
      paymentTypes.push('Bank تحويل');
    }

    if (payments.cardType) {
      paymentTypes.push('Card شبكه');
    }

    if (payments.creditType) {
      paymentTypes.push('Credit آجل');
    }

    if (paymentTypes.length > 2) {
      return 'Multiple - متعدد';
    }

    return paymentTypes.length > 0 ? paymentTypes.join(' - ') : 'N/A';
  }

  isReferenceNumber() {
    const transactionType = this.invoice.transactionType?.toUpperCase();
    if (transactionType === 'CREDIT_NOTE' || transactionType === 'DEBIT_NOTE') {
      return true;
    }
    return false;
  }
  isOrderNumber() {
    const transactionType = this.invoice.transactionType?.toUpperCase();
    if (transactionType !== 'CREDIT_NOTE' && transactionType !== 'DEBIT_NOTE') {
      return true;
    }
    return false;
  }

  getInvoiceTitle(): string {
    if (!this.invoice) return '';

    const transactionType = this.invoice.transactionType?.toUpperCase();
    const taxType = this.invoice.taxType;

    if (transactionType === 'SALES') {
      return taxType === 2
        ? 'SIMPLIFIED TAX INVOICE |   فاتورة ضريبية مبسطة'
        : 'TAX INVOICE  | فاتورة ضريبية';
    }

    if (transactionType === 'CREDIT_NOTE') {
      return taxType === 2
        ? 'SIMPLIFIED TAX CREDIT NOTE  | اشعار دائن ضريبي مبسط'
        : 'TAX CREDIT NOTE  | اشعار دائن ضريبي';
    }

    if (transactionType === 'DEBIT_NOTE') {
      return taxType === 2
        ? 'SIMPLIFIED TAX DEBIT NOTE  | اشعار مدين ضريبي مبسط'
        : 'TAX DEBIT NOTE  | اشعار مدين ضريبي';
    }

    return 'SALES QUOTATION  | عرض أسعار';
  }

  printInvoice() {
    // Add event listener for afterprint
    window.addEventListener('afterprint', () => {
      // Close the window after printing is done
      window.close();
    });

    // Trigger print
    window.print();
  }
}
