<div class="filter-summary-container" *ngIf="hasFilters" [class.has-filters]="hasFilters">
  <!-- Header Section -->
  <div class="filter-summary-header">
    <div class="header-content">
      <mat-icon class="summary-icon">filter_list</mat-icon>
      <span class="summary-title">
        {{ 'report.activeFilters' | translate }}
        <span class="filter-count">({{ activeFiltersCount }})</span>
      </span>
    </div>
    <div class="header-actions">
      <!-- Toggle Collapse Button -->
      <button
        class="toggle-button"
        *ngIf="shouldShowToggleButton"
        [attr.aria-label]="isCollapsed ? 'Expand filters' : 'Collapse filters'"
        (click)="onToggleCollapsed()"
        mat-icon-button>
        <mat-icon>{{ isCollapsed ? 'expand_more' : 'expand_less' }}</mat-icon>
      </button>
      <!-- Clear All Button -->
      <button
        class="clear-all-button"
        [disabled]="isClearAllDisabled"
        [attr.aria-label]="'Clear all filters'"
        (click)="onClearAll()"
        mat-button
        color="warn">
        <mat-icon>clear_all</mat-icon>
        {{ 'report.clearAll' | translate }}
      </button>
    </div>
  </div>

  <!-- Filter Content -->
  <div class="filter-summary-content" [class.collapsed]="isCollapsed">
    <!-- Grouped View -->
    <div class="filter-groups" *ngIf="shouldShowGroupedView">
      <div class="filter-group" *ngFor="let group of filterGroups">
        <div class="group-header">
          <mat-icon class="group-icon">{{ group.icon }}</mat-icon>
          <span class="group-name">{{ group.name }}</span>
          <span class="group-count">({{ getGroupFiltersCount(group) }})</span>
        </div>
        <div class="group-filters">
          <mat-chip-list class="filter-chips">
            <mat-chip
              class="filter-chip"
              *ngFor="let filter of group.filters"
              [ngClass]="{ mandatory: !filter.removable, multiple: filter.type === 'multiple' }"
              [color]="
                filter.config.type === 'dateRange'
                  ? 'accent'
                  : filter.config.type === 'input'
                  ? 'warn'
                  : 'primary'
              "
              [removable]="filter.removable"
              (click)="onFilterClick(filter)"
              (removed)="onRemoveFilter(filter)">
              <!-- Chip Content -->
              <span class="chip-label">{{ filter.label }}:</span>
              <span class="chip-value">{{ filter.displayValue }}</span>
              <!-- Remove Icon -->
              <mat-icon *ngIf="filter.removable" matChipRemove>cancel</mat-icon>
            </mat-chip>
          </mat-chip-list>
        </div>
      </div>
    </div>

    <!-- Flat View -->
    <div class="filter-chips-flat" *ngIf="shouldShowFlatView">
      <mat-chip-list class="filter-chips">
        <mat-chip
          class="filter-chip"
          *ngFor="let filter of visibleFilters"
          [ngClass]="{ mandatory: !filter.removable, multiple: filter.type === 'multiple' }"
          [color]="
            filter.config.type === 'dateRange'
              ? 'accent'
              : filter.config.type === 'input'
              ? 'warn'
              : 'primary'
          "
          [removable]="filter.removable"
          (click)="onFilterClick(filter)"
          (removed)="onRemoveFilter(filter)">
          <!-- Chip Content -->
          <span class="chip-label">{{ filter.label }}:</span>
          <span class="chip-value">{{ filter.displayValue }}</span>
          <!-- Remove Icon -->
          <mat-icon *ngIf="filter.removable" matChipRemove>cancel</mat-icon>
        </mat-chip>
      </mat-chip-list>

      <!-- Show More Indicator -->
      <div class="more-filters-indicator" *ngIf="hiddenFilterCount > 0">
        <mat-chip class="more-chip" (click)="onToggleCollapsed()">
          <span>+{{ hiddenFilterCount }} {{ 'report.moreFilters' | translate }}</span>
          <mat-icon>expand_more</mat-icon>
        </mat-chip>
      </div>
    </div>
  </div>

  <!-- Empty State -->
  <div class="empty-state" *ngIf="!hasFilters">
    <mat-icon class="empty-icon">filter_list_off</mat-icon>
    <span class="empty-message">{{ 'report.noActiveFilters' | translate }}</span>
  </div>
</div>

<!-- Compact Summary (when collapsed) -->
<div class="compact-summary" *ngIf="hasFilters && isCollapsed">
  <div class="compact-content">
    <mat-icon class="compact-icon">filter_list</mat-icon>
    <span class="compact-text">
      {{ activeFiltersCount }} {{ 'report.filtersApplied' | translate }}
    </span>
    <button class="expand-button" (click)="onToggleCollapsed()" mat-icon-button>
      <mat-icon>expand_more</mat-icon>
    </button>
  </div>
</div>
