<div class="main-container">
  <div class="row no-gutters">
    <!-- Form Section -->
    <div class="col-md-6 col-lg-6 col-sm-12 form-section">
      <form [formGroup]="myForm" autocomplete="off">
        <mat-card class="form-card" appearance="outlined">
          <mat-card-header>
            <mat-card-title>{{ 'reportSetup.configuration' | translate }}</mat-card-title>
          </mat-card-header>
          <mat-card-content>
            <!-- Branch and Warehouse Selection -->
            <div class="section-header">
              <h4>{{ 'reportSetup.branchWarehouse' | translate }}</h4>
            </div>
            <div class="row no-gutters">
              <div
                class="col-md-6 col-lg-6 col-sm-12 p-2"
                *ngFor="let field of mandatoryFields; let i = index">
                <mat-label>{{ field.label | translate }}</mat-label>
                <mat-form-field class="w-100" *ngIf="field.controlName === 'branch'">
                  <mat-select formControlName="branchId">
                    <mat-option *ngFor="let branch of branchList" [value]="branch.branchId">
                      {{ branch | localized }}
                    </mat-option>
                    <mat-error
                      *ngIf="
                        myForm?.controls['branchId']?.hasError('required') &&
                        myForm?.controls['branchId']?.touched
                      "
                      >{{ 'common.required' | translate }}</mat-error
                    >
                  </mat-select>
                </mat-form-field>
                <mat-form-field class="w-100" *ngIf="field.controlName === 'warehouse'">
                  <mat-select formControlName="warehouseId">
                    <mat-option [value]="0">{{ 'Other' | translate }}</mat-option>
                    <mat-option
                      *ngFor="let warehouse of warehouseList"
                      [value]="warehouse.warehouseId">
                      {{ warehouse | localized }}
                    </mat-option>
                  </mat-select>
                </mat-form-field>
              </div>
            </div>

            <!-- Main Header Information -->
            <div class="section-header">
              <h4>{{ 'reportSetup.mainHeader' | translate }}</h4>
            </div>
            <div class="row no-gutters">
              <div
                class="col-md-6 col-lg-6 col-sm-12 p-2"
                *ngFor="let field of defaultFields; let i = index">
                <mat-label>{{ field.label | translate }}</mat-label>
                <mat-form-field class="w-100">
                  <textarea
                    [formControlName]="field.controlName"
                    [placeholder]="field.placeholder"
                    matInput
                    rows="3"
                    cdkTextareaAutosize
                    cdkAutosizeMinRows="2"
                    cdkAutosizeMaxRows="4">
                  </textarea>
                </mat-form-field>
              </div>
            </div>

            <!-- Sub Header Information -->
            <div class="section-header">
              <h4>{{ 'reportSetup.subHeader' | translate }}</h4>
            </div>
            <div class="row no-gutters">
              <div
                class="col-md-6 col-lg-6 col-sm-12 p-2"
                *ngFor="let field of additionalFields; let i = index">
                <mat-label>{{ field.label | translate }}</mat-label>
                <mat-form-field class="w-100">
                  <textarea
                    [formControlName]="field.controlName"
                    [placeholder]="field.placeholder"
                    matInput
                    rows="3"
                    cdkTextareaAutosize
                    cdkAutosizeMinRows="2"
                    cdkAutosizeMaxRows="4">
                  </textarea>
                </mat-form-field>
              </div>
            </div>

            <!-- QR Text -->
            <div class="section-header">
              <h4>{{ 'reportSetup.qrSection' | translate }}</h4>
            </div>
            <div class="row no-gutters">
              <div
                class="col-md-12 col-lg-12 col-sm-12 p-2"
                *ngFor="let field of requiredFields; let i = index">
                <mat-label>{{ field.label | translate }}</mat-label>
                <mat-form-field class="w-100">
                  <textarea
                    [formControlName]="field.controlName"
                    [placeholder]="field.placeholder"
                    matInput
                    rows="2"
                    cdkTextareaAutosize
                    cdkAutosizeMinRows="2"
                    cdkAutosizeMaxRows="3">
                  </textarea>
                </mat-form-field>
              </div>
            </div>
          </mat-card-content>
        </mat-card>
      </form>

      <!-- Action Buttons -->
      <div class="action-buttons">
        <button class="m-l-10" (click)="onSubmit($event)" mat-stroked-button color="primary">
          {{ 'common.submit' | translate }}
        </button>
        <button class="m-l-10" [routerLink]="['../']" mat-stroked-button color="warn">
          {{ 'common.cancel' | translate }}
        </button>
      </div>
    </div>

    <!-- Preview Section -->
    <div class="col-md-6 col-lg-6 col-sm-12 preview-section">
      <mat-card class="preview-card" appearance="outlined">
        <mat-card-header>
          <!-- <mat-card-title>
            <mat-icon>visibility</mat-icon>
            {{ 'reportSetup.preview' | translate }}
          </mat-card-title> -->
          <div class="header-actions">
            <button
              class="a4-preview-button"
              (click)="openA4Preview()"
              mat-raised-button
              color="primary">
              <mat-icon>fullscreen</mat-icon>
              {{ 'reportSetup.viewA4' | translate }}
            </button>
          </div>
        </mat-card-header>
        <mat-card-content>
          <div class="preview-container">
            <ng-container *ngTemplateOutlet="previewTemplate"></ng-container>

            <!-- <div class="preview-note">
              <p>
                <em>{{ 'reportSetup.previewNote' | translate }}</em>
              </p>
            </div> -->
          </div>
        </mat-card-content>
      </mat-card>
    </div>
  </div>
</div>

<!-- Reusable Preview Template -->
<ng-template #previewTemplate>
  <div class="preview-header">
    <!--
      Fixed Layout: Always English (Left) | Logo (Center) | Arabic (Right)
      Regardless of app's RTL/LTR direction
    -->
    <div class="header-layout">
      <!-- Left Address (English) - Always on LEFT -->
      <div class="english-address address">
        <h3 *ngIf="myForm.get('subText1')?.value">{{ myForm.get('subText1')?.value }}</h3>
        <p class="company-info" *ngIf="myForm.get('subText2')?.value">
          {{ myForm.get('subText2')?.value }}
        </p>
        <p class="company-info" *ngIf="myForm.get('subText3')?.value">
          {{ myForm.get('subText3')?.value }}
        </p>
        <p class="contact-detail" *ngIf="myForm.get('subAddrLine1')?.value">
          {{ myForm.get('subAddrLine1')?.value }}
        </p>
        <p class="contact-detail" *ngIf="myForm.get('subAddrLine2')?.value">
          {{ myForm.get('subAddrLine2')?.value }}
        </p>
        <p class="contact-detail" *ngIf="myForm.get('subAddrLine3')?.value">
          {{ myForm.get('subAddrLine3')?.value }}
        </p>
      </div>

      <!-- Center Logo - Always in CENTER -->
      <div class="logo">
        <img src="assets/images/sawami_logo.png" alt="Company Logo" />
      </div>

      <!-- Right Address (Arabic) - Always on RIGHT -->
      <div class="arabic-address address">
        <h3 *ngIf="myForm.get('mainText1')?.value">{{ myForm.get('mainText1')?.value }}</h3>
        <p class="company-info" *ngIf="myForm.get('mainText2')?.value">
          {{ myForm.get('mainText2')?.value }}
        </p>
        <p class="company-info" *ngIf="myForm.get('mainText3')?.value">
          {{ myForm.get('mainText3')?.value }}
        </p>
        <p class="contact-detail" *ngIf="myForm.get('addrLine1')?.value">
          {{ myForm.get('addrLine1')?.value }}
        </p>
        <p class="contact-detail" *ngIf="myForm.get('addrLine2')?.value">
          {{ myForm.get('addrLine2')?.value }}
        </p>
        <p class="contact-detail" *ngIf="myForm.get('addrLine3')?.value">
          {{ myForm.get('addrLine3')?.value }}
        </p>
      </div>
    </div>
  </div>
</ng-template>

<!-- A4 Modal Template with Close Button -->
<ng-template #a4ModalTemplate>
  <!-- Close button for A4 modal only -->
  <button class="a4-close-button" (click)="closeA4Preview()" mat-fab color="primary">
    <mat-icon>close</mat-icon>
  </button>

  <!-- Reuse the preview template content -->
  <ng-container *ngTemplateOutlet="previewTemplate"></ng-container>
</ng-template>
