import { Injectable } from '@angular/core';
import { <PERSON>ttp<PERSON><PERSON>, <PERSON>ttp<PERSON><PERSON><PERSON>, HttpInterceptor, HttpRequest } from '@angular/common/http';
import { Observable, throwError, BehaviorSubject } from 'rxjs';
import { catchError, switchMap, filter, take } from 'rxjs/operators';
import { AuthService } from '../services/auth.service';
import { JwtHelperService } from '@auth0/angular-jwt';

@Injectable()
export class JwtInterceptor implements HttpInterceptor {
  private jwtHelper = new JwtHelperService();
  private isRefreshing = false;
  private refreshTokenSubject: BehaviorSubject<string | null> = new BehaviorSubject<string | null>(
    null
  );

  constructor(private authService: AuthService) {}

  intercept(request: HttpRequest<unknown>, next: HttpHandler): Observable<HttpEvent<unknown>> {
    // Skip authentication for auth endpoints
    if (this.isAuthEndpoint(request.url)) {
      return next.handle(request);
    }

    const token = this.authService.getToken;

    // No token case - proceed without authentication
    if (!token) {
      return next.handle(request);
    }

    // Token is valid - add it to the request
    if (!this.jwtHelper.isTokenExpired(token)) {
      return next.handle(this.addToken(request, token));
    }

    // Token is expired - handle refresh
    if (!this.isRefreshing) {
      this.isRefreshing = true;
      this.refreshTokenSubject.next(null);

      return this.authService.tryRefreshingToken().pipe(
        switchMap((newToken: string) => {
          this.isRefreshing = false;

          if (!newToken) {
            this.authService.logout();
            return throwError('Token refresh failed - no new token received');
          }

          this.refreshTokenSubject.next(newToken);
          return next.handle(this.addToken(request, newToken));
        }),
        catchError(error => {
          this.isRefreshing = false;
          this.authService.logout();
          return throwError(`Token refresh failed: ${error.message || 'Unknown error'}`);
        })
      );
    } else {
      // Wait for the token to be refreshed
      return this.refreshTokenSubject.pipe(
        filter(token => token !== null),
        take(1),
        switchMap(token => next.handle(this.addToken(request, token as string)))
      );
    }
  }

  private addToken(request: HttpRequest<any>, token: string): HttpRequest<any> {
    return request.clone({
      setHeaders: { Authorization: `Bearer ${token}` },
      withCredentials: true,
    });
  }

  private isAuthEndpoint(url: string): boolean {
    // Skip authentication for login/refresh endpoints
    return url.includes('/auth/login') || url.includes('/auth/refresh-token');
  }
}
