import { Component, OnInit } from '@angular/core';
import { DashboardModulesHolder } from 'src/app/modules/shared/components/dashboard-modules-holder/modulesHolder';
import { navItems } from 'src/app/layouts/full/app-navigation-common-menus';

@Component({
  selector: 'app-identity-dashboard',
  templateUrl: './identity-dashboard.component.html',
  styleUrls: ['./identity-dashboard.component.scss'],
})
export class IdentityDashboardComponent implements OnInit {
  productModulesList: DashboardModulesHolder[] = [];

  constructor() {
    // Constructor can be used for dependency injection if needed
  }

  ngOnInit(): void {
    const identityMenu = navItems.find(item => item.route === 'identity');
    if (identityMenu && identityMenu.children) {
      this.productModulesList = identityMenu.children.map(child => ({
        moduleName: child.displayName,
        moduleDescription: `Manage ${child.displayName}.`,
        modulePermission: child.permissions,
        moduleRouterLink: `../${child.route.split('/').pop()}`,
        moduleType: 'subModule',
      }));
    }
  }
}
