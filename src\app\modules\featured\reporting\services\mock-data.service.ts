import { Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class MockDataService {
  constructor() {}

  /**
   * Mock report types data for testing
   */
  getMockReportTypes(): any[] {
    return [
      {
        id: 1,
        type: 1,
        name: 'Pricing',
        nameArabic: 'Pricing',
        authority: 'ReportsManagement.Inventory.PriceReports',
        languageCode: 'AR',
        searchConfigs: [
          {
            field: 'branch',
            type: 'dropdown',
            mandatory: true,
            placeholder: ' ',
            position: 1,
            isAdvanced: false,
            fieldLabel: 'branch',
            backendParam: 'branchId',
            idField: 'branchId',
          },
          {
            field: 'year',
            type: 'dropdown',
            mandatory: true,
            placeholder: ' ',
            position: 2,
            isAdvanced: false,
            fieldLabel: 'year',
            backendParam: 'yearId',
            idField: 'id',
          },
          {
            field: 'itemCode',
            type: 'input',
            mandatory: false,
            placeholder: 'Search By ItemCode/Name',
            position: 3,
            isAdvanced: false,
            fieldLabel: 'item',
            backendParam: 'searchString',
            idField: 'itemId',
          },
          {
            field: 'categories',
            type: 'multiSelectDropdown',
            mandatory: false,
            placeholder: ' ',
            position: 4,
            isAdvanced: true,
            fieldLabel: 'categories',
            backendParam: 'categoryIds',
            idField: 'categoryId',
          },
          {
            field: 'units',
            type: 'multiSelectDropdown',
            mandatory: false,
            placeholder: ' ',
            position: 5,
            isAdvanced: true,
            fieldLabel: 'units',
            backendParam: 'unitIds',
            idField: 'unitId',
          },
        ],
        endPoint: '/pages/price-list',
        jasperEndPoint: '/dynamic/price-list',
      },
      {
        id: 3,
        type: 1,
        name: 'Item Detailed',
        nameArabic: 'Item Detailed',
        authority: 'ReportsManagement.Inventory.StockValueReports',
        languageCode: 'AR',
        searchConfigs: [
          {
            field: 'branch',
            type: 'dropdown',
            mandatory: true,
            placeholder: ' ',
            position: 1,
            isAdvanced: false,
            fieldLabel: 'branch',
            backendParam: 'branchId',
            idField: 'branchId',
          },
          {
            field: ' year',
            type: 'dropdown',
            mandatory: true,
            placeholder: ' ',
            position: 2,
            isAdvanced: false,
            fieldLabel: 'year',
            backendParam: 'yearId',
            idField: 'id',
          },
          {
            field: 'itemCode',
            type: 'input',
            mandatory: false,
            placeholder: 'Search By ItemCode/Name',
            position: 4,
            isAdvanced: false,
            fieldLabel: 'item',
            backendParam: 'searchString',
            idField: 'itemId',
          },
          {
            field: 'warehouse',
            type: 'multiSelectDropdown',
            mandatory: false,
            placeholder: ' ',
            position: 5,
            isAdvanced: false,
            fieldLabel: 'warehouse',
            backendParam: 'warehouseIds',
            idField: 'warehouseId',
          },
          {
            field: 'categories',
            type: 'multiSelectDropdown',
            mandatory: false,
            placeholder: ' ',
            position: 6,
            isAdvanced: true,
            fieldLabel: 'categories',
            backendParam: 'categoryIds',
            idField: 'categoryId',
          },
          {
            field: 'units',
            type: 'multiSelectDropdown',
            mandatory: false,
            placeholder: ' ',
            position: 7,
            isAdvanced: true,
            fieldLabel: 'units',
            backendParam: 'unitIds',
            idField: 'unitId',
          },
        ],
        endPoint: '/pages/item-detailed',
        jasperEndPoint: '/dynamic/item-detailed',
      },
      {
        id: 2,
        type: 1,
        name: 'Stock Value',
        nameArabic: 'Stock Value',
        authority: 'ReportsManagement.Inventory.StockValueReports',
        languageCode: 'AR',
        searchConfigs: [
          {
            field: 'branch',
            type: 'dropdown',
            mandatory: true,
            placeholder: ' ',
            position: 1,
            isAdvanced: false,
            fieldLabel: 'branch',
            backendParam: 'branchId',
            idField: 'branchId',
          },
          {
            field: 'year',
            type: 'dropdown',
            mandatory: true,
            placeholder: ' ',
            position: 2,
            isAdvanced: false,
            fieldLabel: 'year',
            backendParam: 'yearId',
            idField: 'id',
          },
          {
            field: 'itemCode',
            type: 'input',
            mandatory: false,
            placeholder: 'Search By ItemCode/Name',
            position: 4,
            isAdvanced: false,
            fieldLabel: 'item',
            backendParam: 'searchString',
            idField: 'itemId',
          },
          {
            field: 'warehouse',
            type: 'multiSelectDropdown',
            mandatory: true,
            placeholder: ' ',
            position: 5,
            isAdvanced: false,
            fieldLabel: 'warehouse',
            backendParam: 'warehouseIds',
            idField: 'warehouseId',
          },
          {
            field: 'categories',
            type: 'multiSelectDropdown',
            mandatory: false,
            placeholder: ' ',
            position: 6,
            isAdvanced: true,
            fieldLabel: 'categories',
            backendParam: 'categoryIds',
            idField: 'categoryId',
          },
          {
            field: 'units',
            type: 'multiSelectDropdown',
            mandatory: false,
            placeholder: ' ',
            position: 7,
            isAdvanced: true,
            fieldLabel: 'units',
            backendParam: 'unitIds',
            idField: 'unitId',
          },
        ],
        endPoint: '/pages/stock-value',
        jasperEndPoint: '/dynamic/stock-value',
      },
    ];
  }

  /**
   * Mock branches data
   */
  getMockBranches(): any[] {
    return [
      { branchId: 1, nameEnglish: 'Main Branch', nameArabic: 'الفرع الرئيسي', name: 'Main Branch' },
      { branchId: 2, nameEnglish: 'Branch 2', nameArabic: 'الفرع الثاني', name: 'Branch 2' },
      { branchId: 3, nameEnglish: 'Branch 3', nameArabic: 'الفرع الثالث', name: 'Branch 3' },
    ];
  }

  /**
   * Mock categories data
   */
  getMockCategories(): any[] {
    return [
      { categoryId: 1, nameEnglish: 'Electronics', nameArabic: 'إلكترونيات', name: 'Electronics' },
      { categoryId: 2, nameEnglish: 'Clothing', nameArabic: 'ملابس', name: 'Clothing' },
      {
        categoryId: 3,
        nameEnglish: 'Food & Beverages',
        nameArabic: 'طعام ومشروبات',
        name: 'Food & Beverages',
      },
      {
        categoryId: 4,
        nameEnglish: 'Home & Garden',
        nameArabic: 'منزل وحديقة',
        name: 'Home & Garden',
      },
    ];
  }

  /**
   * Mock warehouses data
   */
  getMockWarehouses(): any[] {
    return [
      {
        warehouseId: 1,
        nameEnglish: 'Main Warehouse',
        nameArabic: 'المستودع الرئيسي',
        name: 'Main Warehouse',
        branchId: 1,
      },
      {
        warehouseId: 2,
        nameEnglish: 'Secondary Warehouse',
        nameArabic: 'المستودع الثانوي',
        name: 'Secondary Warehouse',
        branchId: 1,
      },
      {
        warehouseId: 3,
        nameEnglish: 'Branch 2 Warehouse',
        nameArabic: 'مستودع الفرع الثاني',
        name: 'Branch 2 Warehouse',
        branchId: 2,
      },
      {
        warehouseId: 4,
        nameEnglish: 'Branch 3 Warehouse',
        nameArabic: 'مستودع الفرع الثالث',
        name: 'Branch 3 Warehouse',
        branchId: 3,
      },
    ];
  }

  /**
   * Mock units data
   */
  getMockUnits(): any[] {
    return [
      { unitId: 1, nameEnglish: 'Piece', nameArabic: 'قطعة', name: 'Piece' },
      { unitId: 2, nameEnglish: 'Kilogram', nameArabic: 'كيلوغرام', name: 'Kilogram' },
      { unitId: 3, nameEnglish: 'Liter', nameArabic: 'لتر', name: 'Liter' },
      { unitId: 4, nameEnglish: 'Meter', nameArabic: 'متر', name: 'Meter' },
    ];
  }

  /**
   * Check if we should use mock data (for testing)
   */
  shouldUseMockData(): boolean {
    // You can control this via environment variable or localStorage
    return (
      localStorage.getItem('useMockData') === 'true' || window.location.hostname === 'localhost'
    );
  }

  /**
   * Enable mock data for testing
   */
  enableMockData(): void {
    localStorage.setItem('useMockData', 'true');
    console.log('Mock data enabled. Refresh the page to see changes.');
  }

  /**
   * Disable mock data
   */
  disableMockData(): void {
    localStorage.setItem('useMockData', 'false');
    console.log('Mock data disabled. Refresh the page to see changes.');
  }
}
