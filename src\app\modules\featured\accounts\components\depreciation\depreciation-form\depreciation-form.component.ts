import { Component, OnInit } from '@angular/core';
import { UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { AuthService } from 'src/app/modules/core/core/services/auth.service';
import { Account, AccountBasic } from '../../../models/account';
import { AccountParams } from '../../../models/accountParams';
import { Depreciation } from '../../../models/depreciation';
import { ChartOfAccountsService } from '../../../services/chart-of-accounts.service';
import { DepreciationService } from '../../../services/depreciation.service';
import { DepreciationResponse } from './../../../models/depreciationResponse';
import { AccountAutoSearchComponent } from '../../accountAutoSearch/account-auto-search.component';
import { LocalStorageService } from 'src/app/modules/core/core/services/local-storage.service';
import { depreciationMethodTypes } from 'src/app/core/configs/dropDownConfig';
import { CommonService } from 'src/app/core/api/common.service';

@Component({
  selector: 'app-depreciation-form',
  templateUrl: './depreciation-form.component.html',
  styleUrls: ['./depreciation-form.component.scss'],
  providers: [
    AccountAutoSearchComponent, // added class in the providers
  ],
})
export class DepreciationFormComponent implements OnInit {
  depreciationCreationForm: UntypedFormGroup;
  formTitle: string;
  assetAccounts: Account[];
  expenseAccounts: Account[];
  deprAssetAccounts: Account[];
  contraAssetAccounts: Account[];
  depreciations: Depreciation[];
  depreciationResponse: DepreciationResponse;
  assetAccount: Account;
  cumDepreciationAccount: Account;
  depreciationExpenseAccount: Account;

  constructor(
    private authService: AuthService,
    private toastr: ToastrService,
    private route: ActivatedRoute,
    private router: Router,
    private depreciationService: DepreciationService,
    private formBuilder: UntypedFormBuilder,
    private chartOfAccountsService: ChartOfAccountsService,
    private localStorage: LocalStorageService,
    private commonService: CommonService
  ) {
    this.depreciationCreationForm = this.formBuilder.group({
      cumulativeDepreciationAccount: [null, Validators.compose([Validators.required])],
      assetAccount: [null, Validators.compose([Validators.required])],
      depreciationExpenseAccount: [null, Validators.compose([Validators.required])],
      depreciationPct: ['', Validators.compose([Validators.required])],
      purchaseDate: ['', Validators.compose([Validators.required])],
      supplierName: [''],
      purchaseAmount: [''],
      salvationValue: [0],
      assetLocation: [''],
      notes: [''],
      depreciationMethod: ['STRAIGHT_LINE', Validators.compose([Validators.required])],
      lastDepreciationDate: [''],
      depreciatedValue: [''],
      costAccountNumber: [null],
    });
  }

  isEditMode = false;
  isViewMode = false;
  isCreateMode = false;
  isLoading: boolean;
  depreciationId: string;
  depreciation: Depreciation;

  depreciationMethods = depreciationMethodTypes;

  ngOnInit(): void {
    console.log('NG Initilised');
    this.isLoading = true;
    this.formTitle = this.route.snapshot.data['title'];
    this.route.params.subscribe(params => {
      const id = params['id'];
      if (id) {
        if (this.router.url.includes('edit')) {
          this.isEditMode = true;
        } else if (this.router.url.includes('view')) {
          this.isViewMode = true;
        }
        this.depreciationId = id;
        this.getDepreciationById();
      } else {
        this.depreciationId = null;
        this.isCreateMode = true;
        this.isEditMode = false;
        this.isViewMode = false;
        this.initializeForm(null);
      }
    });
    this.getContraAssetAccounts();
  }

  getDepreciationById(): void {
    this.depreciationService
      .getDepreciationById(this.depreciationId)
      .subscribe((response: Depreciation) => {
        this.depreciation = response;
        this.initializeForm(this.depreciation);
      });
  }
  initializeForm(data: Depreciation): void {
    this.depreciationCreationForm = this.formBuilder.group({
      cumulativeDepreciationAccount: [null, Validators.compose([Validators.required])],
      assetAccount: [null, Validators.compose([Validators.required])],
      depreciationExpenseAccount: [null, Validators.compose([Validators.required])],
      depreciationPct: ['', Validators.compose([Validators.required])],
      purchaseDate: ['', Validators.compose([Validators.required])],
      supplierName: [''],
      purchaseAmount: ['', Validators.compose([Validators.required])],
      salvationValue: [0],
      assetLocation: [''],
      notes: [''],
      depreciationMethod: ['STRAIGHT_LINE', Validators.compose([Validators.required])],
      lastDepreciationDate: [''],
      depreciatedValue: [''],
      costAccountNumber: [null],
    });

    if (data) {
      if (this.isViewMode) {
        this.getAssetAccounts(data.assetAccount);
        this.getExpenseAccounts(data.depreciationExpenseAccount);
        this.getDepreciationAssetAccounts(data.cumulativeDepreciationAccount);
      }

      console.log('patch values');
      console.log('Data:', data);
      this.depreciationCreationForm.patchValue({
        cumulativeDepreciationAccount: data.cumulativeDepreciationAccount,
        assetAccount: data.assetAccount,
        depreciationExpenseAccount: data.depreciationExpenseAccount,
        depreciationPct: data.depreciationPct,
        purchaseDate: data.purchaseDate,
        supplierName: data.supplierName,
        purchaseAmount: data.purchaseAmount,
        salvationValue: data.salvationValue,
        assetLocation: data.assetLocation,
        notes: data.notes,
        depreciationMethod: data.depreciationMethod,
        lastDepreciationDate: data.lastDepreciationDate,
        depreciatedValue: data.depreciatedValue,
        costAccountNumber: data.costAccountNumber,
      });
    }

    if (this.isViewMode) {
      this.depreciationCreationForm.disable();
    }
  }
  onSubmit(event: Event) {
    event.preventDefault();
    this.depreciationCreationForm.markAllAsTouched();
    if (this.depreciationCreationForm && this.depreciationCreationForm?.valid) {
      if (this.isCreateMode) {
        this.depreciationService
          .createDepreciation(this.depreciationCreationForm.value)
          .subscribe(() => {
            //this.toastr.success('Depreciation Added Successfully');
            this.commonService.playSuccessSound();
            this.router.navigate(['../'], { relativeTo: this.route });
          });
      } else if (this.isEditMode) {
        this.depreciationService
          .updateDepreciation(this.depreciationId, this.depreciationCreationForm.value)
          .subscribe(() => {
            //this.toastr.success('Depreciation Updated Successfully');
            this.commonService.playSuccessSound();
            this.router.navigate(['../../'], { relativeTo: this.route });
          });
      }
    } else {
      this.commonService.scrollToError();
    }
  }

  getAssetAccounts(accountId: any): void {
    this.chartOfAccountsService.getChartOfAccountById(accountId).subscribe(result => {
      this.assetAccount = result;
      this.isLoading = false;
    });
  }

  getExpenseAccounts(accountId: any): void {
    this.chartOfAccountsService.getChartOfAccountById(accountId).subscribe(result => {
      this.cumDepreciationAccount = result;
      this.isLoading = false;
    });
  }

  getDepreciationAssetAccounts(accountId: any): void {
    this.chartOfAccountsService.getChartOfAccountById(accountId).subscribe(result => {
      this.depreciationExpenseAccount = result;
      this.isLoading = false;
    });
  }
  getContraAssetAccounts(): void {
    const accountParams: AccountParams = new AccountParams();
    accountParams.accountCategory = 'ASSETS';
    accountParams.isContraAccount = true;
    accountParams.accountType = 'DETAILED';
    this.chartOfAccountsService.getAllChartOfAccounts(accountParams).subscribe(result => {
      this.contraAssetAccounts = result.accounts;
      this.isLoading = false;
    });
  }
  //   getDepreciations(): void {
  //   const depreciationParams: DepreciationParams = new DepreciationParams();
  //   this.depreciationService.getAllDepreciation(depreciationParams).subscribe((result) => {
  //     this.depreciations = result.depreciations;
  //     this.isLoading = false;
  //   });
  // }
  getAccountName(account: AccountBasic): string {
    if (account && this.localStorage.getItem('locale') === 'AR' && account.nameArabic !== null) {
      return account.nameArabic + '-' + account.accountNumber;
    } else if (
      account &&
      this.localStorage.getItem('locale') === 'EN' &&
      account.nameEnglish !== null
    ) {
      return account.nameEnglish + '-' + account.accountNumber;
    } else if (account && account.nameEnglish !== null) {
      return account.nameEnglish + '-' + account.accountNumber;
    } else if (account && -account.nameArabic !== null) {
      return account.nameArabic + '-' + account.accountNumber;
    } else {
      return '';
    }
  }
}
