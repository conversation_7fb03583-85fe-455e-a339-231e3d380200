import { Component, OnInit } from '@angular/core';
import { DashboardModulesHolder } from 'src/app/modules/shared/components/dashboard-modules-holder/modulesHolder';
import { navItems } from 'src/app/layouts/full/app-navigation-common-menus';

@Component({
  selector: 'app-report-dashboard',
  templateUrl: './report-dashboard.component.html',
  styleUrls: ['./report-dashboard.component.scss'],
})
export class ReportDashboardComponent implements OnInit {
  productModulesList: DashboardModulesHolder[] = [];

  constructor() {
    // Constructor can be used for dependency injection if needed
  }

  ngOnInit(): void {
    const reportMenu = navItems.find(item => item.route === 'reports');
    if (reportMenu && reportMenu.children) {
      this.productModulesList = reportMenu.children.map(child => ({
        moduleName: child.displayName,
        moduleDescription: `Manage ${child.displayName}.`,
        modulePermission: child.permissions,
        moduleRouterLink: `../${child.route.split('/').pop()}`,
        moduleType: 'subModule',
      }));
    }
  }
}
