<div class="row">
  <div class="col-md-4 p-2 custom-card" *ngFor="let module of modules | hasRoleQualifier">
    <ng-container *ngIf="module?.moduleType === 'mainModule' || module?.moduleType === 'subModule'">
      <mat-card
        class="module-card cursor-pointer bg-primary"
        *appHasPermission="module?.modulePermission"
        [ngClass]="{
          'main-module': module?.moduleType === 'mainModule',
          'sub-module': module?.moduleType === 'subModule'
        }"
        [routerLink]="
          module.moduleType === 'mainModule'
            ? '/' + module.moduleRouterLink
            : module.moduleRouterLink
        ">
        <div class="text-center">
          <mat-card-title class="text-white">
            <p>{{ module.moduleName | translate }}</p>
          </mat-card-title>
        </div>
      </mat-card>
    </ng-container>
  </div>
</div>
