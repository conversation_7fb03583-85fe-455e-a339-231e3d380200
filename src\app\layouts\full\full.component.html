<!-- Main container with theme and layout configuration -->
<div
  class="green_theme"
  [ngClass]="{
    'sidebarNav-horizontal': options.horizontal,  
    cardBorder: options.cardBorder,  
  }"
  autosize
  autoFocus>
  <!-- Horizontal header section -->
  <ng-container>
    <app-horizontal-header (toggleMobileNav)="sidenav.toggle()"></app-horizontal-header>
  </ng-container>

  <!-- Main layout container with sidenav -->
  <mat-sidenav-container class="mainWrapper">
    <!-- Side navigation panel -->
    <mat-sidenav
      class="sidebarNav"
      #leftsidenav
      [mode]="'over'"
      (openedChange)="onSidenavOpenedChange($event)">
      <!-- <app-sidebar> </app-sidebar> -->
      <!-- Scrollable navigation menu -->
      <ng-scrollbar class="position-relative mini-scroll">
        <mat-nav-list class="sidebar-list">
          <!-- Dynamic navigation items with toggle functionality -->
          <app-vertical-side-nav
            *ngFor="let item of navItems"
            [item]="item"
            (notify)="sidenav.toggle()">
          </app-vertical-side-nav>
        </mat-nav-list>
      </ng-scrollbar>
    </mat-sidenav>

    <!-- Main content area -->
    <mat-sidenav-content class="contentWrapper">
      <!-- Conditional horizontal sidebar -->
      <app-horizontal-horizontal-side-nav
        *ngIf="options.horizontal"></app-horizontal-horizontal-side-nav>

      <!-- Main page content wrapper -->
      <main
        class="pageWrapper"
        [ngClass]="{
          maxWidth: options.boxed
        }">
        <!-- Breadcrumb navigation -->
        <app-breadcrumb class="d-block"></app-breadcrumb>

        <!-- Dynamic content from routing -->
        <router-outlet></router-outlet>

        <!-- Loading indicator -->
        <ngx-loading [show]="busyService.isLoading | async"></ngx-loading>
      </main>
    </mat-sidenav-content>
  </mat-sidenav-container>

  <!-- Footer section -->
  <footer class="copyright-footer">
    <div class="copyright-text" dir="ltr">
      &copy; 2025 Sawami Information Technology. All rights reserved.
    </div>
  </footer>
</div>
