import {
  AfterViewChecked,
  Component,
  Inject,
  OnInit,
  Optional,
  SkipSelf,
  ViewChild,
} from '@angular/core';
import { UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { MAT_DIALOG_DATA } from '@angular/material/dialog';
import { ActivatedRoute, Router } from '@angular/router';
import { CustomValidators } from 'ngx-custom-validators';
import { ToastrService } from 'ngx-toastr';
import { CommonService } from 'src/app/core/api/common.service';
import { SupplierService } from 'src/app/core/api/trading/supplier.service';
import { ActionType } from 'src/app/core/enums/actionType';
import { ISupplier } from 'src/app/core/interfaces/supplier';
import { AuthService } from 'src/app/modules/core/core/services/auth.service';
import { Account } from 'src/app/modules/featured/accounts/models/account';
import { ChartOfAccountsService } from 'src/app/modules/featured/accounts/services/chart-of-accounts.service';
import { AddressComponent } from 'src/app/modules/shared/components/address/address.component';

@Component({
  selector: 'app-supplier-form',
  templateUrl: './supplier-form.component.html',
  styleUrls: ['./supplier-form.component.scss'],
})
export class SupplierFormComponent implements OnInit, AfterViewChecked {
  @ViewChild('addressForm', { static: false }) addressForm: AddressComponent;
  supplierForm: UntypedFormGroup;
  imageChangedEvent: any = '';
  croppedImage: any = '';
  showCropper = false;
  defaultImage = 'assets/images/users/default.png';
  file: File;
  formTitle: string;
  supplierId: string;
  parentAccounts: Account[] = [];
  mode: ActionType;
  actionMode = ActionType;
  constructor(
    @Optional() @SkipSelf() @Inject(MAT_DIALOG_DATA) public modalData: any,
    private authService: AuthService,
    private toastr: ToastrService,
    private supplierService: SupplierService,
    private fb: UntypedFormBuilder,
    private route: ActivatedRoute,
    private router: Router,
    private chartOfAccountsService: ChartOfAccountsService,
    private commonService: CommonService
  ) {}

  get isViewMode() {
    return this.mode === this.actionMode.view || this.supplierForm?.disabled;
  }

  get isEditMode() {
    return this.mode === this.actionMode.edit;
  }

  get isCreateMode() {
    return this.mode === this.actionMode.create;
  }

  ngOnInit(): void {
    this.mode = this.route.snapshot.data['mode'];
    this.initializeForm(this.modalData?.data);
    this.getAccountIds();
    this.route.params.subscribe(params => {
      this.supplierId = params['id'];
      if (this.supplierId) {
        this.getSupplier(this.supplierId);
      }
    });
  }

  ngAfterViewChecked(): void {
    if (this.modalData?.data) {
      this.disbaleAllForms();
    }
  }

  getAccountIds() {
    this.chartOfAccountsService
      .getChartOfAccountsByAccountCategoryAndAccountSubCategory('ASSETS', 'SUPPLIER')
      .subscribe((response: Account[]) => {
        this.parentAccounts = response;
      });
  }

  getSupplier(branchId: string): void {
    this.supplierService.getSupplierById(branchId).subscribe(
      response => {
        this.patchData(response);
      },
      error => console.log(error)
    );
  }

  initializeForm(data?: ISupplier) {
    this.supplierForm = this.fb.group(
      {
        parentAccountId: [data?.parentAccountId ?? null, Validators.compose([Validators.required])],
        nameArabic: [data?.nameArabic ?? null, Validators.compose([Validators.required])],
        nameEnglish: [data?.nameEnglish ?? null, Validators.compose([Validators.required])],
        hasTransferPrivilege: [
          data?.hasTransferPrivilege ?? false,
          Validators.compose([Validators.required]),
        ],
        hasCardPrivilege: [
          data?.hasCardPrivilege ?? false,
          Validators.compose([Validators.required]),
        ],
        hasCashPrivilege: [
          data?.hasCashPrivilege ?? false,
          Validators.compose([Validators.required]),
        ],
        hasCreditPrivilege: [
          data?.hasCreditPrivilege ?? false,
          Validators.compose([Validators.required]),
        ],
        isFreezed: [data?.isFreezed ?? false, Validators.compose([Validators.required])],
        discountPercent: [data?.discountPercent ?? 0, Validators.compose([Validators.required])],
        paymentToleranceDays: [
          data?.paymentToleranceDays ?? 0,
          Validators.compose([Validators.required]),
        ],
        allowedBalance: [
          data?.allowedBalance ?? **********.0,
          Validators.compose([Validators.required]),
        ],
        phoneNumber: [data?.phoneNumber ?? null, Validators.compose([])],
        vatNumber: [data?.vatNumber ?? 0, Validators.compose([Validators.required])],
        emailId: [data?.emailId ?? null, Validators.compose([CustomValidators.email])],
        accountNumber: [data?.accountNumber ?? null],
        commission: [data?.commission ?? 0],
        yearlyTarget: [data?.yearlyTarget ?? 0],
        address: [data?.address ?? null],
      },
      { validators: this.commonService.atLeastOnePrivilegeValidator() }
    );
    this.disableForm();
  }

  patchData(data: ISupplier): void {
    this.initializeForm(data);
  }

  private disbaleAllForms(): void {
    this.supplierForm.disable();
    this.addressForm.disableForm();
  }

  private disableForm(): void {
    if (this.mode === this.actionMode.view) {
      this.disbaleAllForms();
    }
  }

  onSubmit(event: Event) {
    event.preventDefault();
    this.addressForm.markFormAsTouched();
    this.supplierForm.markAllAsTouched();
    if (this.supplierForm && this.supplierForm?.valid && this.addressForm.isValid()) {
      if (!this.isEditMode) {
        console.log('this.customerForm.value', this.supplierForm.value);
        this.supplierService.createSupplier(this.supplierForm.value).subscribe(() => {
          //this.toastr.success('Supplier Created Successfully');
          this.commonService.playSuccessSound();
          this.router.navigate(['../'], { relativeTo: this.route });
        });
      } else {
        this.supplierService
          .updateSupplier(this.supplierForm.value, this.supplierId)
          .subscribe(() => {
            //this.toastr.success('Supplier Updated Successfully');
            this.commonService.playSuccessSound();
            this.router.navigate(['../../'], { relativeTo: this.route });
          });
      }
    } else {
      this.commonService.scrollToError();
    }
  }
}
