import { Component, OnInit, ViewChild, Inject } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { UntypedFormBuilder, UntypedFormGroup } from '@angular/forms';
import { Account } from '../../models/account';
import { MatPaginator } from '@angular/material/paginator';
import { MatTableDataSource } from '@angular/material/table';
import { MatSort } from '@angular/material/sort';
import { AccountParams } from '../../models/accountParams';
import { ChartOfAccountsService } from '../../services/chart-of-accounts.service';
import { LocalStorageService } from 'src/app/modules/core/core/services/local-storage.service';
@Component({
  selector: 'app-accounts-list-window',
  templateUrl: './accounts-list-window.component.html',
  styleUrls: ['./accounts-list-window.component.scss'],
})
export class AccountsListWindowComponent implements OnInit {
  searchBoxForm: UntypedFormGroup;
  searchPlaceholder: string;
  touched = false;
  isFilterEnabled = false;
  defaultSearchType = null;
  @ViewChild(MatPaginator) paginator: MatPaginator;
  @ViewChild(MatSort) sort: MatSort;
  accounts: Account[];
  displayedColumns: string[];
  resultsLength: number;
  isLoading = true;
  dataSource: MatTableDataSource<Account>;
  accountCategorys = [
    {
      display: 'All',
      value: '',
    },
    {
      display: 'Assets',
      value: 'ASSETS',
    },
    {
      display: 'Liabilities',
      value: 'LIABILITIES',
    },
    {
      display: 'Equity',
      value: 'EQUITY',
    },
    {
      display: 'Revenues',
      value: 'REVENUES',
    },
    {
      display: 'Expenses',
      value: 'EXPENSES',
    },
  ];

  accountTypes = [
    {
      display: 'All',
      value: '',
    },
    {
      display: 'General',
      value: 'GENERAL',
    },
    {
      display: 'Detailed',
      value: 'DETAILED',
    },
  ];

  accountSubCategorys = [
    {
      display: 'All',
      value: '',
    },
    {
      display: 'General',
      value: 'GENERAL',
    },
    {
      display: 'Cashier',
      value: 'CASHIER',
    },
    {
      display: 'Bank',
      value: 'BANK',
    },
    {
      display: 'Customer',
      value: 'CUSTOMER',
    },
    {
      display: 'Supplier',
      value: 'SUPPLIER',
    },
    {
      display: 'Branch',
      value: 'BRANCH',
    },
    {
      display: 'Distributor',
      value: 'DISTRIBUTOR',
    },
  ];
  filterValues = {
    accountType: '',
    accountCategory: '',
    accountSubCategory: '',
  };
  constructor(
    private chartOfAccountsService: ChartOfAccountsService,
    private formBuilder: UntypedFormBuilder,
    public dialogRef: MatDialogRef<AccountsListWindowComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any,
    private localStorage: LocalStorageService
  ) {}

  applyFilters() {
    console.log(this.filterValues);
    this.dataSource.filter = JSON.stringify(this.filterValues);
  }

  ngOnInit(): void {
    this.searchBoxForm = this.formBuilder.group({
      searchString: [],
      searchType: [this.defaultSearchType],
      accountCategory: [],
      accountSubCategory: [],
      accountType: [],
    });

    this.updatePlaceHolder(this.defaultSearchType);
    console.log(this.searchBoxForm);
    this.searchBoxForm &&
      this.searchBoxForm?.controls['accountType'].valueChanges.subscribe(() => {
        this.filterValues.accountType = this.searchBoxForm.controls['accountType'].value;
        this.applyFilters();
      });
    this.searchBoxForm &&
      this.searchBoxForm?.controls['accountCategory'].valueChanges.subscribe(() => {
        this.filterValues.accountCategory = this.searchBoxForm.controls['accountCategory'].value;
        this.applyFilters();
      });
    this.searchBoxForm &&
      this.searchBoxForm?.controls['accountSubCategory'].valueChanges.subscribe(() => {
        this.filterValues.accountSubCategory =
          this.searchBoxForm.controls['accountSubCategory'].value;
        this.applyFilters();
      });
    this.searchBoxForm &&
      this.searchBoxForm?.controls['searchType'].valueChanges.subscribe(selection => {
        this.updatePlaceHolder(selection);
      });
    this.getAccounts();
    this.initColumns();
  }
  getAccounts(): void {
    const accountParams: AccountParams = new AccountParams();
    accountParams.pageNumber = 1;
    accountParams.pageSize = 100;

    accountParams.searchString = this.searchBoxForm.controls['searchString'].value;
    if (this.isFilterEnabled) {
      accountParams.searchType = this.searchBoxForm.controls['searchType'].value;
      accountParams.accountCategory = this.searchBoxForm.controls['accountCategory'].value;
      accountParams.accountType = this.searchBoxForm.controls['accountType'].value;
      accountParams.accountSubCategory = this.searchBoxForm.controls['accountSubCategory'].value;
    }
    this.chartOfAccountsService.getAllChartOfAccounts(accountParams).subscribe(result => {
      this.accounts = result.accounts;
      this.dataSource = new MatTableDataSource<Account>(this.accounts);
      this.dataSource.filterPredicate = (data: Account) => {
        // Check if the data matches the filter values for each column
        const accountTypeFilter = this.filterValues.accountType.toLowerCase();
        const accountCategoryFilter = this.filterValues.accountCategory.toLowerCase();
        const accountSubCategoryFilter = this.filterValues.accountSubCategory.toLowerCase();
        return (
          data.accountType.toLowerCase().includes(accountTypeFilter) &&
          data.accountCategory.toLowerCase().includes(accountCategoryFilter) &&
          data.accountSubCategory.toLowerCase().includes(accountSubCategoryFilter)
        );
      };
      this.dataSource.paginator = this.paginator;
      this.resultsLength = result.totalRecordsCount;
      this.isLoading = false;
    });
  }

  resetSearchType(searchType: string): void {
    this.searchBoxForm.controls['searchType'].setValue(searchType);
  }

  initColumns(): void {
    this.displayedColumns = [
      'accountNumber',
      'nameArabic',
      'accountType',
      'accountCategory',
      'accountSubCategory',
    ];
  }

  displayFilter() {
    this.isFilterEnabled = !this.isFilterEnabled;
  }

  clearFilters(event: Event) {
    event.preventDefault();
    this.resetSearchBox();
    this.getAccounts();
  }

  updatePlaceHolder(selection) {
    switch (selection) {
      case 'accountNumber':
        this.searchPlaceholder = 'Search by Account Number';
        break;
      case 'nameArabic':
        this.searchPlaceholder = 'Search by Arabic Account Name';
        break;
      case 'accountType':
        this.searchPlaceholder = 'Search by Account Type';
        break;
      case 'accountCategory':
        this.searchPlaceholder = 'Search by Account Group';
        break;
      case 'accountSubCategory':
        this.searchPlaceholder = 'Search by Sub Category';
        break;
      default:
        this.searchPlaceholder = 'Search by Any Text';
    }
  }

  resetSearchBox(): void {
    this.searchBoxForm.reset();
    this.searchBoxForm.controls['searchType'].setValue(this.defaultSearchType);
    this.searchBoxForm.updateValueAndValidity();
  }

  accountSelected(account: Account) {
    this.data.data = account;
    this.dialogRef.close();
  }

  getAccountName(account: Account) {
    if (this.localStorage.getItem('locale') === 'AR' && account.nameArabic !== null) {
      return account.nameArabic;
    } else if (this.localStorage.getItem('locale') === 'EN' && account.nameEnglish !== null) {
      return account.nameEnglish;
    } else if (account.nameEnglish !== null) {
      return account.nameEnglish;
    } else if (account.nameArabic !== null) {
      return account.nameArabic;
    } else {
      return '';
    }
  }
}
