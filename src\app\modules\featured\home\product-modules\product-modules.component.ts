import { Component, OnInit } from '@angular/core';
import { navItems } from 'src/app/layouts/full/app-navigation-common-menus';
import { DashboardModulesHolder } from 'src/app/modules/shared/components/dashboard-modules-holder/modulesHolder';

@Component({
  selector: 'app-product-modules',
  templateUrl: './product-modules.component.html',
  styleUrls: ['./product-modules.component.scss'],
})
export class ProductModulesComponent implements OnInit {
  productModulesList: DashboardModulesHolder[] = [];

  constructor() {
    this.productModulesList = navItems
      .filter(item => item.route) // Exclude items without a route
      .map(item => ({
        moduleName: item.displayName,
        moduleDescription: `Manage ${item.displayName}.`,
        modulePermission: item.permissions,
        moduleRouterLink: item.route,
        moduleType: 'mainModule',
      }));
  }

  ngOnInit(): void {}
}
