import { Component, OnInit, ViewChild, Optional, Inject } from '@angular/core';
import { UntypedFormGroup, UntypedFormBuilder, Validators } from '@angular/forms';
import { MAT_DIALOG_DATA } from '@angular/material/dialog';
import { fork<PERSON>oin } from 'rxjs';
import { PaginatedFilter } from 'src/app/core/interfaces/PaginatedFilter';
import { AuthService } from 'src/app/modules/core/core/services/auth.service';
import { SearchboxComponent } from 'src/app/modules/shared/components/searchbox/searchbox.component';
import { Branch } from '../../../catalog/models/branch';
import { BulkEditData } from '../../../catalog/models/bulkedit';
import { Category } from '../../../catalog/models/category';
import { CategoryParams } from '../../../catalog/models/categoryParams';
import { CategoryService } from '../../../../../core/api/category.service';
import { BranchParams } from '../../../settings/models/branchParams';
import { StoreParams } from '../../../settings/models/storeParams';
import { BranchService } from '../../../settings/services/branch.service';
import { ReportService } from '../../../settings/services/report.service';
import { StoreService } from 'src/app/core/api/store.service';
import { SearchConfigService } from '../services/search-config.service';
import { ISearchConfig } from '../models/search-config.interface';
import { CookieService } from 'ngx-cookie-service';

@Component({
  selector: 'stock-report',
  templateUrl: './stock-report.component.html',
  styleUrls: ['./stock-report.component.scss'],
})
export class StockReportComponent implements OnInit {
  @ViewChild('searchBoxForm') searchBoxForm: SearchboxComponent;
  loading = true;
  categoryList: Category[];
  branchList: Branch[];
  warehouseList: any;
  allWarehouses: any;
  filtersForm: UntypedFormGroup;
  isReportPulling = false;
  isAdvancedSearchEnabled = false;
  isAdvancedSearchVisible = false;
  searchConfigs: ISearchConfig[];
  formName = 'stockReportSearch';

  constructor(
    @Optional() @Inject(MAT_DIALOG_DATA) public data: BulkEditData,
    private branchService: BranchService,
    private storeService: StoreService,
    private categoryService: CategoryService,
    private authService: AuthService,
    private reportService: ReportService,
    private fb: UntypedFormBuilder,
    private searchConfigService: SearchConfigService,
    private cookieService: CookieService
  ) {}

  ngOnInit(): void {
    this.loading = true;
    this.initializeSearchConfigs();
    this.getAllDropDownData();
  }

  initializeSearchConfigs() {
    this.searchConfigs = this.searchConfigService.getStockReportConfigs();
    this.createForm();
  }

  createForm() {
    this.filtersForm = this.fb.group({});

    this.searchConfigs.forEach(config => {
      const validators = config.mandatory ? [Validators.required] : [];
      let defaultValue = null;

      // Set default values for specific fields
      if (config.backendParam === 'options') {
        defaultValue = 'PDF';
      } else if (config.backendParam === 'showDetailed') {
        defaultValue = false;
      } else if (config.type === 'multiSelectDropdown') {
        defaultValue = [];
      }

      this.filtersForm.addControl(config.backendParam, this.fb.control(defaultValue, validators));
    });

    this.subscribeToFormChanges();

    // Set default values from cookies
    const branchIdFromCookie = +this.cookieService.get('branchId');
    if (branchIdFromCookie) {
      this.filtersForm.patchValue({ branchId: [branchIdFromCookie] });
    }
  }

  subscribeToFormChanges() {
    this.filtersForm.valueChanges.subscribe(data => {
      if (data && data.branchId && data.branchId.length > 0) {
        this.warehouseList = this.allWarehouses.filter(warehouse =>
          data.branchId.includes(warehouse.branchId)
        );
      } else {
        this.warehouseList = this.allWarehouses;
      }
    });
  }

  getAllDropDownData() {
    const category = this.categoryService.getAllCategories(new CategoryParams());
    const branches = this.branchService.getAllBranches(new BranchParams());
    const warehouse = this.storeService.getStores(new StoreParams(), this.authService.getCompanyID);
    forkJoin([category, branches, warehouse]).subscribe(results => {
      this.categoryList = results[0];
      this.branchList = results[1];
      this.allWarehouses = results[2];
      this.warehouseList = results[2];
      this.loading = false;
    });
  }

  getListForField(field: string): any[] {
    switch (field) {
      case 'branchId':
        return this.branchList || [];
      case 'categoryId':
        return this.categoryList || [];
      case 'warehouseId':
        return this.warehouseList || [];
      default:
        return [];
    }
  }

  onBranchChange() {
    // Reset warehouse selection when branch changes
    this.filtersForm.patchValue({ warehouseId: [] });
  }

  getReportData(event?: Event, pageEvent?: PaginatedFilter) {
    event?.preventDefault();
    this.filtersForm.markAllAsTouched();

    // Check if searchbox is valid (if it exists)
    const searchBoxForm = this.filtersForm.get('searchBoxForm');
    const isSearchBoxValid =
      !searchBoxForm ||
      !searchBoxForm.value ||
      (searchBoxForm.value.searchString && searchBoxForm.value.searchType);

    if (this.filtersForm.valid && isSearchBoxValid) {
      this.isReportPulling = true;
      const params: any = {};

      // Map form values to API parameters
      params.categoryId = this.filtersForm.controls['categoryId'].value;
      params.warehouseId = this.filtersForm.controls['warehouseId'].value;
      params.branchId = this.filtersForm.controls['branchId'].value;
      params.type = this.filtersForm.controls['options'].value;
      params.showDetailed = this.filtersForm.controls['showDetailed'].value;

      // Handle searchbox data if present
      const searchBoxData = this.filtersForm.controls['searchBoxForm'].value;
      if (searchBoxData) {
        params.searchType = searchBoxData.searchType;
        params.searchString = searchBoxData.searchString;
      }

      this.reportService
        .getStockReports(params)
        .subscribe(
          result => {
            console.log('Success....');
          },
          error => {
            console.log(error);
          }
        )
        .add(() => (this.isReportPulling = false));
    }
  }

  // Note: clearFilters is now handled by the FilterFormComponent
}
