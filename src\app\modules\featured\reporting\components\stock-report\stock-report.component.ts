import { Component, OnInit, Optional, Inject } from '@angular/core';
import { UntypedFormGroup, UntypedFormBuilder } from '@angular/forms';
import { MAT_DIALOG_DATA } from '@angular/material/dialog';
import { fork<PERSON>oin } from 'rxjs';
import { AuthService } from 'src/app/modules/core/core/services/auth.service';
import { BulkEditData } from '../../../catalog/models/bulkedit';
import { CategoryParams } from '../../../catalog/models/categoryParams';
import { CategoryService } from '../../../../../core/api/category.service';
import { BranchParams } from '../../../settings/models/branchParams';
import { StoreParams } from '../../../settings/models/storeParams';
import { BranchService } from '../../../settings/services/branch.service';
import { ReportService } from '../../../settings/services/report.service';
import { StoreService } from 'src/app/core/api/store.service';
import { SearchConfigService } from '../../services/search-config.service';
import { ISearchConfig } from '../../models/search-config.interface';
import { CookieService } from 'ngx-cookie-service';
import { MockDataService } from '../../services/mock-data.service';

@Component({
  selector: 'stock-report',
  templateUrl: './stock-report.component.html',
  styleUrls: ['./stock-report.component.scss'],
})
export class StockReportComponent implements OnInit {
  // Component properties
  branchList: any[] = [];
  categoryList: any[] = [];
  allWarehouses: any[] = [];
  warehouseList: any[] = [];
  loading = false;
  isAdvancedSearchVisible = false;
  searchConfigs: ISearchConfig[];
  formName = 'stockReportSearch';

  // Report type selection properties
  reportTypeList: any[] = [];
  selectedReportType: any = null;
  showReportTypeSelector = true;

  // Form
  filtersForm: UntypedFormGroup;
  constructor(
    @Optional() @Inject(MAT_DIALOG_DATA) public data: BulkEditData,
    private branchService: BranchService,
    private storeService: StoreService,
    private categoryService: CategoryService,
    private authService: AuthService,
    private reportService: ReportService,
    private fb: UntypedFormBuilder,
    private searchConfigService: SearchConfigService,
    private cookieService: CookieService,
    private mockDataService: MockDataService
  ) {}

  ngOnInit(): void {
    this.loading = true;
    this.getAllDropDownData();
    this.initializeSearchConfigs();
    this.initializeForm();
  }

  initializeSearchConfigs() {
    // Initialize with default configuration - will be updated when report type is selected
    this.searchConfigs = [
      {
        field: 'branch',
        type: 'dropdown',
        mandatory: true,
        placeholder: ' ',
        position: 1,
        isAdvanced: false,
        fieldLabel: 'branch',
        backendParam: 'branchId',
        idField: 'branchId',
      },
      {
        field: 'category',
        type: 'multiSelectDropdown',
        mandatory: false,
        placeholder: ' ',
        position: 2,
        isAdvanced: false,
        fieldLabel: 'categories',
        backendParam: 'categoryIds',
        idField: 'categoryId',
      },
      {
        field: 'warehouse',
        type: 'multiSelectDropdown',
        mandatory: false,
        placeholder: ' ',
        position: 3,
        isAdvanced: false,
        fieldLabel: 'warehouse',
        backendParam: 'warehouseIds',
        idField: 'warehouseId',
      },
    ];
  }

  initializeForm() {
    this.filtersForm = this.fb.group({
      branchId: [null],
      categoryIds: [[]],
      warehouseIds: [[]],
      searchString: [''],
      showDetailed: [false],
      outputFormat: ['PDF'],
    });

    // Set default values from cookies
    const branchIdFromCookie = +this.cookieService.get('branchId');
    if (branchIdFromCookie) {
      this.filtersForm.patchValue({ branchId: branchIdFromCookie });
    }
  }

  getAllDropDownData() {
    // Check if we should use mock data for testing
    if (this.mockDataService.shouldUseMockData()) {
      console.log('Using mock data for testing...');
      this.categoryList = this.mockDataService.getMockCategories();
      this.branchList = this.mockDataService.getMockBranches();
      this.allWarehouses = this.mockDataService.getMockWarehouses();
      this.warehouseList = this.allWarehouses;
      this.reportTypeList = this.mockDataService.getMockReportTypes();
      this.loading = false;
      return;
    }

    // Use real API calls
    const category = this.categoryService.getAllCategories(new CategoryParams());
    const branches = this.branchService.getAllBranches(new BranchParams());
    const warehouse = this.storeService.getStores(new StoreParams(), this.authService.getCompanyID);
    const reportTypes = this.reportService.getAllReportType(1); // Type 1 for inventory reports

    forkJoin([category, branches, warehouse, reportTypes]).subscribe(
      results => {
        this.categoryList = results[0];
        this.branchList = results[1];
        this.allWarehouses = results[2];
        this.warehouseList = results[2];
        this.reportTypeList = results[3];
        this.loading = false;
      },
      error => {
        console.error('Error loading dropdown data, falling back to mock data:', error);
        // Fallback to mock data if API fails
        this.categoryList = this.mockDataService.getMockCategories();
        this.branchList = this.mockDataService.getMockBranches();
        this.allWarehouses = this.mockDataService.getMockWarehouses();
        this.warehouseList = this.allWarehouses;
        this.reportTypeList = this.mockDataService.getMockReportTypes();
        this.loading = false;
      }
    );
  }

  // Handle report type selection change
  onReportTypeChange = (reportType: any) => {
    this.selectedReportType = reportType;
    if (reportType && reportType.searchConfigs) {
      this.searchConfigs = reportType.searchConfigs;
      this.updateFormControls();
    }
  };

  // Update form controls based on search configs
  updateFormControls() {
    const formControls: any = {};
    this.searchConfigs.forEach(config => {
      if (config.type === 'multiSelectDropdown') {
        formControls[config.backendParam] = [[]];
      } else if (config.type === 'checkbox') {
        formControls[config.backendParam] = [false];
      } else {
        formControls[config.backendParam] = [null];
      }
    });

    this.filtersForm = this.fb.group(formControls);
  }

  // Get list for field (used by FilterFormComponent)
  getListForField = (field: string): any[] => {
    switch (field) {
      case 'branchId':
        return this.branchList;
      case 'categoryIds':
        return this.categoryList;
      case 'warehouseIds':
        return this.warehouseList;
      default:
        return [];
    }
  };

  // Handle branch change for warehouse filtering
  onBranchChange = () => {
    const selectedBranchId = this.filtersForm.get('branchId')?.value;
    if (selectedBranchId) {
      this.warehouseList = this.allWarehouses.filter(
        warehouse => warehouse.branchId === selectedBranchId
      );
    } else {
      this.warehouseList = this.allWarehouses;
    }
    // Reset warehouse selection when branch changes
    this.filtersForm.get('warehouseIds')?.setValue([]);
  };

  // Handle search input
  onSearchInput = (input: string) => {
    console.log('Search input:', input);
  };

  // Handle item code selection
  onItemCodeSelected = (item: any) => {
    console.log('Item selected:', item);
  };

  // Get report data
  getReportData = (event: Event) => {
    event.preventDefault();
    this.filtersForm.markAllAsTouched();

    if (this.filtersForm.valid) {
      const formValues = this.filtersForm.value;
      console.log('Form values:', formValues);
      console.log('Selected report type:', this.selectedReportType);

      // Here you would call the actual report service
      // For now, just log the data
      console.log('Generating report with configuration...');
    } else {
      console.log('Form is invalid');
    }
  };
}
