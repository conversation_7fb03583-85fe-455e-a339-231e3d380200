import {
  Component,
  Inject,
  <PERSON><PERSON><PERSON><PERSON>,
  OnInit,
  Optional,
  QueryList,
  ViewChild,
  ViewChildren,
} from '@angular/core';
import { UntypedFormGroup } from '@angular/forms';
import { MAT_DIALOG_DATA } from '@angular/material/dialog';
import { MatPaginator, PageEvent } from '@angular/material/paginator';
import { MatSelect } from '@angular/material/select';
import { MatSort, Sort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { BulkEditData } from '../../../catalog/models/bulkedit';
import { Report } from '../../models/report-template';
import { UserReportService, ReportState } from '../../services/user-report.service';
import { ActiveFilter } from '../filter-summary/filter-summary.component';

@Component({
  selector: 'inventory-report',
  templateUrl: './inventory-report.component.html',
  styleUrls: ['./inventory-report.component.scss'],
})
export class InventoryReportComponent implements OnInit, OnDestroy {
  @ViewChild(MatPaginator) paginator: MatPaginator;
  @ViewChild(MatSort) sort: MatSort;
  @ViewChildren(MatSelect) matSelects!: QueryList<MatSelect>;

  // UI State
  dataSource = new MatTableDataSource<object>([]);
  pageSize = 100;
  currentPage = 0;
  filtersForm: UntypedFormGroup;
  isBranchNotSelected = true;

  // Request parameters for sorting
  private sortRequest: Record<string, unknown> = {};

  // Component state from service
  state: ReportState;

  // Subscription management
  private destroy$ = new Subject<void>();

  constructor(
    @Optional() @Inject(MAT_DIALOG_DATA) public data: BulkEditData,
    private userReportService: UserReportService
  ) {
    this.state = this.userReportService.currentState;
  }

  ngOnInit(): void {
    this.dataSource = new MatTableDataSource<object>([]);
    this.subscribeToState();
    this.getAllDropDownData();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private subscribeToState(): void {
    this.userReportService.state$.pipe(takeUntil(this.destroy$)).subscribe(state => {
      this.state = state;
      this.updateUIFromState();
    });
  }

  private updateUIFromState(): void {
    this.dataSource.data = this.state.reportData;
    this.isBranchNotSelected = !this.filtersForm?.get('branchId')?.value;
  }

  filterYearIdForSelectedBranch(): void {
    const branchId = this.filtersForm?.get('branchId')?.value;
    this.userReportService.filterYearIdForSelectedBranch(branchId);
  }

  onItemCodeSelected(item: { itemId: string | number }): void {
    this.userReportService.setSelectedItemId(item?.itemId ?? null);
  }

  onBranchAndYearSelect(): void {
    const branchId = this.filtersForm?.get('branchId')?.value;
    const yearId = this.filtersForm?.get('yearId')?.value;

    if (!branchId || !yearId) return;

    // this.userReportService
    //   .loadProductsByBranchAndYear(branchId, yearId)
    //   .pipe(takeUntil(this.destroy$))
    //   .subscribe();
  }

  onSortChange(sortState: Sort): void {
    if (!sortState.active || !sortState.direction) return;

    this.sortRequest['sortBy'] = sortState.active;
    this.sortRequest['sortDir'] = sortState.direction;
    this.sortRequest['sortFields'] = `${sortState.active}:${sortState.direction}`;
    this.getReportData();
  }

  onPageChange(event: PageEvent): void {
    this.pageSize = event.pageSize;
    this.currentPage = event.pageIndex;
    this.getReportData();
  }

  getAllDropDownData(): void {
    this.userReportService
      .loadAllDropdownData()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          // Data loaded successfully, state updated automatically
        },
        error: error => {
          console.error('Error loading dropdown data:', error);
        },
      });
  }

  subscribeToFormChanges(): void {
    this.filtersForm.valueChanges.pipe(takeUntil(this.destroy$)).subscribe(data => {
      if (data && data.branchId) {
        this.userReportService.filterWarehousesByBranch(data.branchId);
      }
      this.isBranchNotSelected = !data || !data.branchId;
    });

    if (!this.filtersForm) return;

    const branchIdControl = this.filtersForm.get('branchId');
    const yearIdControl = this.filtersForm.get('yearId');

    if (branchIdControl) {
      branchIdControl.valueChanges.pipe(takeUntil(this.destroy$)).subscribe(() => {
        this.filterYearIdForSelectedBranch();
        this.onBranchAndYearSelect();
      });
    }

    if (yearIdControl) {
      yearIdControl.valueChanges.pipe(takeUntil(this.destroy$)).subscribe(() => {
        this.onBranchAndYearSelect();
      });
    }
  }

  getReportData(event?: Event): void {
    if (event) event.preventDefault();

    if (!this.userReportService.validateForm(this.filtersForm)) {
      return;
    }

    const sortFields = this.sortRequest['sortFields']
      ? [String(this.sortRequest['sortFields'])]
      : [];
    const sortDir = (this.sortRequest['sortDir'] as string) || '';
    const sortBy = (this.sortRequest['sortBy'] as string) || '';

    const params = this.userReportService.buildReportParams(
      this.filtersForm,
      this.currentPage,
      this.pageSize,
      sortFields,
      sortDir,
      sortBy
    );

    this.userReportService
      .getReportData(params)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          // Data updated automatically through state management
          this.dataSource = new MatTableDataSource<object>(this.state.reportData);
        },
        error: error => {
          console.error('Error fetching report data:', error);
        },
      });
  }

  downloadReport(): void {
    const sortFields = this.sortRequest['sortFields']
      ? [String(this.sortRequest['sortFields'])]
      : [];
    const sortDir = (this.sortRequest['sortDir'] as string) || '';
    const sortBy = (this.sortRequest['sortBy'] as string) || '';

    const params = this.userReportService.buildReportParams(
      this.filtersForm,
      this.currentPage,
      this.pageSize,
      sortFields,
      sortDir,
      sortBy
    );

    this.userReportService
      .downloadReport(params)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => console.log('Report download initiated successfully'),
        error: error => console.error('Error downloading report:', error),
      });
  }

  createForm(): void {
    this.filtersForm = this.userReportService.createForm(this.state.searchConfigs);
    this.subscribeToFormChanges();
    this.filterYearIdForSelectedBranch();
  }

  onBranchChange(): void {
    this.subscribeToFormChanges();
  }

  getListForField = (field: string): object[] => {
    return this.userReportService.getListForField(field);
  };

  selectReportType(report: Report): void {
    this.userReportService.selectReportType(report);
    this.createForm();
  }

  onReportTypeChange(reportType: Report | null): void {
    reportType ? this.selectReportType(reportType) : this.resetReportType();
  }

  toggleColumn(column: string, checked: boolean): void {
    this.userReportService.toggleColumn(column, checked);
  }

  resetReportType(): void {
    this.userReportService.resetReportType();
    this.filtersForm = this.userReportService.createForm([]);
  }

  resetFilters(): void {
    this.filtersForm?.reset();
    this.filtersForm?.markAsPristine();
    this.filtersForm?.markAsUntouched();
    this.filtersForm?.updateValueAndValidity();
  }

  onFilterRemoved(filterId: string): void {
    // Filter already removed by the filter summary component
    // Trigger any additional actions if needed
    console.log('Filter removed:', filterId);
  }

  onFilterClicked(filter: ActiveFilter): void {
    // Handle filter click - could scroll to the field or highlight it
    console.log('Filter clicked for editing:', filter);

    // Optional: Focus on the corresponding form field
    this.focusOnFilterField(filter);
  }

  onFiltersCleared(): void {
    // Handle clearing all filters
    console.log('All filters cleared');

    // Optionally trigger a report refresh
    if (this.filtersForm && this.selectedReportType) {
      // Auto-refresh the report after clearing filters
      setTimeout(() => {
        this.getReportData();
      }, 100);
    }
  }

  private focusOnFilterField(filter: ActiveFilter): void {
    // Try to focus on the corresponding form field
    try {
      const fieldElement = document.querySelector(
        `[formControlName="${filter.id}"]`
      ) as HTMLElement;
      if (fieldElement) {
        fieldElement.focus();
        fieldElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
      }
    } catch (error) {
      console.log('Could not focus on filter field:', error);
    }
  }

  // Getter methods for template access

  get displayedColumns() {
    return this.state.displayedColumns;
  }

  get visibleColumns() {
    return this.state.visibleColumns;
  }

  get totalItems() {
    return this.state.totalItems;
  }

  get reportTypeList() {
    return this.state.reportTypeList;
  }

  get selectedReportType() {
    return this.state.selectedReportType;
  }

  get searchConfigs() {
    return this.state.searchConfigs;
  }

  get reportData() {
    return this.state.reportData;
  }
}
