<ng-container *appHasPermission="item?.permissions">
  <a
    class="cursor-pointer menuLink"
    *ngIf="!item.navCap"
    [ngClass]="{
      activeMenu: item.route ? router.isActive(item.route, true) : false
    }"
    (click)="onItemSelected(item)">
    <i-tabler class="routeIcon icon-16" name="{{ item.iconName }}"></i-tabler>
    {{ item.displayName | translate }}
    <span class="down-icon d-flex m-l-auto" *ngIf="item.children && item.children.length">
      <mat-icon> expand_more </mat-icon>
    </span>
  </a>

  <div class="childBox" *ngIf="item.children">
    <app-horizontal-horizontal-navigation-menu
      class="ddmenu"
      *ngFor="let child of item.children"
      [item]="child">
    </app-horizontal-horizontal-navigation-menu>
  </div>
</ng-container>
